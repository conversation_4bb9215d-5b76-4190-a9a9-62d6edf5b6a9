##############################
#set up the summary variables#
##############################

#which policy was best? encoded according to the four orthogonal decisions:
#1.encode low, 2.encode high, 3.offload low, 4.offload high
best_policy=integer(4) 

#hit rates, stored separately for 1. low-value, 2. high-value
hits=integer(2) 
surprise_hits=integer(2) 

#####################################
#create the set of items to remember#
#####################################

items=NULL

#add one low-value and high-value item multiple times, according to the nItems variable
for (i in 1:nItems) {
  items=c(items,LOWVAL,HIGHVAL)
}

#####################
#run the simulations#
#####################

for (r in 1:runs) {
  rewards=integer(16) #use this variable to collect the total reward for each policy
  samples=integer(16) #number of samples collected for each policy
  
  hit=matrix(0,nrow=16,ncol=2) #number of hits for each policy, separate for high- and low-value
  surprise_hit=matrix(0,nrow=16,ncol=2) #number of surprise hits, i.e. hits from internal memory alone
  
  for (e in 1:episodes) {
    #first pick a random policy, making sure that it is allowed
    valid_policy=FALSE 
    
    while (valid_policy==FALSE) {
      #select a random policy, a number from 1-16
      policy=sample.int(16,1)
      
      #set up the strategy, according to this policy
      #we subtract 1 because we want to convert policies 1-16 to 0-15 so that we can work out binary equivalent
      encode_strategy=integer(2)
      encode_strategy[LOWVAL]=intToBits(policy-1)[ENCODE_LOWVAL]==TRUE
      encode_strategy[HIGHVAL]=intToBits(policy-1)[ENCODE_HIGHVAL]==TRUE
      
      offload_strategy=integer(2)
      offload_strategy[LOWVAL]=intToBits(policy-1)[OFFLOAD_LOWVAL]==TRUE
      offload_strategy[HIGHVAL]=intToBits(policy-1)[OFFLOAD_HIGHVAL]==TRUE
      
      #check whether the selected policy is allowed
      valid_policy=TRUE
      
      for (value in 1:2) {
        if ((offload_strategy[value]==TRUE)&(offloading_allowed[value]==FALSE)) {
          valid_policy=FALSE
        }
      }
    }
    
    reward=0 #initialise the reward variable, collecting the total reward on this run
    memory_set=NULL #initialise the memory set, containing all the memorised items
    offload_set=NULL #initialise the offload set, containing all the offloaded items
    
    #loop over items and offload/encode into memory according to strategy
    for (item in 1:length(items)) {
      #get the value of this item
      value=items[item]
      
      if (offload_strategy[value]) { #do we offload items of this value?
        #if so, add it to the offload set...
        offload_set=c(offload_set,item)
        #... and subtract the offloading cost
        reward=reward-offload_cost
      }
      
      if (encode_strategy[value]) { #do we encode items of this value into memory?
        #if so, add it to the memory set
        memory_set=c(memory_set,item)
      }
    }
    
    #now retrieve the items
    for (item in 1:length(items)) {
      #set this to true if the item was recalled
      item_recalled=FALSE
      
      #get the value of this item
      value=items[item]
      
      #is the item in the memory set?
      if (sum(item==memory_set)==1) {
        #get the total number of items held in memory
        memory_load=length(memory_set)
        
        #get the probability of remembering, with this number of items held in memory
        pRemember=remember_internal[memory_load]
        
        #recall the item with the specified probability
        if(runif(1)<pRemember) {
          item_recalled=TRUE
          
          #we can now work out the surprise hits, ignoring any information stored in the offloaded set
          surprise_hit[policy,value]=surprise_hit[policy,value]+1
        }
      }
      
      #is the item in the offloaded set?
      if (sum(item==offload_set)==1) {
        #recall the item
        if(runif(1)<remember_external) {
          item_recalled=TRUE
        }
      }
      
      #now work out the reward, and the hit rates
      if (item_recalled) {
        hit[policy,value]=hit[policy,value]+1
        reward=reward+reward_values[value]
      }
    }
    
    #record the reward associated with this policy, and the number of times it has been sampled
    rewards[policy]=rewards[policy]+reward
    samples[policy]=samples[policy]+1
  }
  
  #now work out the winning strategy, by calculating the mean reward for each policy
  mean_rewards=rewards/samples
  
  #which policy is associated with highest reward?
  maxReward=which(mean_rewards==max(mean_rewards[!is.na(mean_rewards)])) #need to exclude policies that were never sampled
  #if there are multiple policies associated with the same reward, pick one at random
  maxReward=maxReward[sample(length(maxReward),1)]
  
  #now record results in the summary variables
  
  #which policy was best?
  best_policy=best_policy+as.integer(intToBits(maxReward-1)) #we need the -1 because we want policies 0-15 for binary conversion
  
  #what were the hit rates associated with this policy?
  hits=hits+(hit[maxReward,]/(samples[maxReward]*nItems))
  
  #what were the hit rates associated with this policy, using the memory set alone?
  surprise_hits=surprise_hits+(surprise_hit[maxReward,]/(samples[maxReward]*nItems))
}