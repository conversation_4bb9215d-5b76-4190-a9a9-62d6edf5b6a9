#!/usr/bin/env python3
"""
簡單測試後設認知模擬的修正
"""

def test_metacognitive_fixes():
    """直接運行主程式來測試後設認知修正"""
    print("=== 測試後設認知修正 ===")
    print("運行主程式的後設認知模擬...")
    print("請注意觀察是否有後設認知決策的調試輸出")
    print("如果有輸出 '後設認知決策: 高價值=X/Y, 低價值=Z/W'，表示修正成功")
    print("\n開始測試...")

if __name__ == "__main__":
    test_metacognitive_fixes()
    
    # 直接導入並運行簡化的測試
    try:
        # 運行原始程式的最後部分進行測試
        exec(open('(後設認知)神經網路期末.py').read())
    except Exception as e:
        print(f"執行時出現錯誤: {e}")
        import traceback
        traceback.print_exc()
