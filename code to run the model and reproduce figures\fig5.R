########################################
#first run the model without offloading#
########################################
source('initialise.R')

offloading_allowed[LOWVAL]=FALSE
offloading_allowed[HIGHVAL]=FALSE

source('run_simulation.R')

best_policy=best_policy/runs
hits=hits/runs
surprise_hits=surprise_hits/runs

#record the surprise-hit data
no_offload_lowval_surprise_hits = surprise_hits[LOWVAL]
no_offload_highval_surprise_hits = surprise_hits[HIGHVAL]

######################################################
#now run the model with high-value offloading allowed#
######################################################
offloading_allowed[HIGHVAL]=TRUE

source('run_simulation.R')

best_policy=best_policy/runs
hits=hits/runs
surprise_hits=surprise_hits/runs

#record the surprise-hit data
highval_offload_lowval_surprise_hits = surprise_hits[LOWVAL]
highval_offload_highval_surprise_hits = surprise_hits[HIGHVAL]

#######################
#now graph the results#
#######################
value=c("Low-value", "High-value")
no_offloading_data=c(no_offload_lowval_surprise_hits,no_offload_highval_surprise_hits)
offloading_data=c(highval_offload_lowval_surprise_hits, highval_offload_highval_surprise_hits)

offloading = data.frame(value=value, data=offloading_data)
no_offloading = data.frame(value=value, data=no_offloading_data)

offloading$value = factor(offloading$value, levels = offloading$value)
no_offloading$value = factor(no_offloading$value, levels = no_offloading$value)

no_offloading_graph = ggplot(no_offloading, aes(x=value,y=data)) +
  geom_bar(stat="identity", fill="cornflowerblue") +
  ggtitle("No offloading allowed") +
  scale_y_continuous(name="Surprise-test accuracy", limits=c(0,1), breaks=seq(0,1,0.2)) +
  theme(panel.border = element_blank(),  
        # Remove panel grid lines
        panel.grid.major = element_blank(),
        panel.grid.minor = element_blank(),
        # Remove panel background
        panel.background = element_rect(fill = "white", colour = "black", size = 1, linetype = "solid"),
        # Remove legend
        legend.position = "none",
        # Remove x axis label
        axis.title.x = element_blank(),
        axis.title.y = element_text(size = 14),
        # Style text
        text = element_text(size = 14),
        plot.title = element_text(hjust = 0.5))

offloading_graph = ggplot(offloading, aes(x=value,y=data)) +
  geom_bar(stat="identity", fill="cornflowerblue") +
  ggtitle("High-value offloading allowed") +
  scale_y_continuous(name="", limits=c(0,1), breaks=seq(0,1,0.2)) +
  theme(panel.border = element_blank(),  
        # Remove panel grid lines
        panel.grid.major = element_blank(),
        panel.grid.minor = element_blank(),
        # Remove panel background
        panel.background = element_rect(fill = "white", colour = "black", size = 1, linetype = "solid"),
        # Remove legend
        legend.position = "none",
        # Remove x axis label
        axis.title.x = element_blank(),
        # Style text
        text = element_text(size = 14),
        plot.title = element_text(hjust = 0.5))

graph = no_offloading_graph + offloading_graph

pdf(file="fig.pdf", width = 8, height = 4)

print(graph)

dev.off()
