import random  
import numpy as np  
import matplotlib.pyplot as plt  


letter_A = np.array([[0, 0, 0, 0, 1, 1, 0, 0, 0, 0],
                      [0, 0, 0, 1, 1, 1, 1, 0, 0, 0],
                      [0, 0, 0, 1, 0, 0, 1, 0, 0, 0],
                      [0, 0, 1, 1, 0, 0, 1, 1, 0, 0],
                      [0, 0, 1, 1, 1, 1, 1, 1, 0, 0],
                      [0, 0, 1, 1, 1, 1, 1, 1, 0, 0],
                      [0, 1, 1, 1, 0, 0, 1, 1, 1, 0],
                      [0, 1, 1, 0, 0, 0, 0, 1, 1, 0],
                      [1, 1, 0, 0, 0, 0, 0, 0, 1, 1],
                      [1, 1, 0, 0, 0, 0, 0, 0, 1, 1]])
vector_A = np.ndarray.flatten(letter_A)  # 將字母A的矩陣展平為向量


letter_B = np.array([[1, 1, 1, 1, 1, 1, 0, 0, 0, 0],
                      [1, 1, 1, 1, 1, 1, 1, 0, 0, 0],
                      [1, 1, 0, 0, 0, 0, 1, 1, 0, 0],
                      [1, 1, 0, 0, 0, 0, 1, 1, 0, 0],
                      [1, 1, 1, 1, 1, 1, 1, 1, 0, 0],
                      [1, 1, 1, 1, 1, 1, 1, 0, 0, 0],
                      [1, 1, 0, 0, 0, 0, 1, 1, 0, 0],
                      [1, 1, 0, 0, 0, 0, 0, 1, 1, 0],
                      [1, 1, 0, 0, 0, 0, 0, 1, 1, 0],
                      [1, 1, 1, 1, 1, 1, 1, 1, 0, 0]])
vector_B = np.ndarray.flatten(letter_B)  # 將字母B的矩陣展平為向量

letter_C = np.array([[0, 0, 1, 1, 1, 1, 1, 1, 1, 0],
                      [0, 1, 1, 1, 0, 0, 0, 0, 0, 0],
                      [0, 1, 1, 0, 0, 0, 0, 0, 0, 0],
                      [0, 1, 1, 0, 0, 0, 0, 0, 0, 0],
                      [0, 1, 1, 0, 0, 0, 0, 0, 0, 0],
                      [0, 1, 1, 0, 0, 0, 0, 0, 0, 0],
                      [0, 1, 1, 0, 0, 0, 0, 0, 0, 0],
                      [0, 1, 1, 1, 0, 0, 0, 0, 0, 0],
                      [0, 0, 1, 1, 1, 1, 1, 1, 1, 0],
                      [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]])
vector_C = np.ndarray.flatten(letter_C)  # 將字母C的矩陣展平為向量

letter_D = np.array([[1, 1, 1, 1, 1, 1, 1, 0, 0, 0],
                      [1, 1, 1, 1, 1, 1, 1, 1, 0, 0],
                      [1, 1, 0, 0, 0, 0, 1, 1, 0, 0],
                      [1, 1, 0, 0, 0, 0, 0, 1, 1, 0],
                      [1, 1, 0, 0, 0, 0, 0, 1, 1, 0],
                      [1, 1, 0, 0, 0, 0, 0, 1, 1, 0],
                      [1, 1, 0, 0, 0, 0, 0, 1, 1, 0],
                      [1, 1, 0, 0, 0, 0, 1, 1, 0, 0],
                      [1, 1, 1, 1, 1, 1, 1, 1, 0, 0],
                      [1, 1, 1, 1, 1, 1, 1, 0, 0, 0]])
vector_D = np.ndarray.flatten(letter_D)  # 將字母D的矩陣展平為向量

letter_E = np.array([[1, 1, 1, 1, 1, 1, 1, 1, 1, 0],
                      [1, 1, 0, 0, 0, 0, 0, 0, 0, 0],
                      [1, 1, 0, 0, 0, 0, 0, 0, 0, 0],
                      [1, 1, 0, 0, 0, 0, 0, 0, 0, 0],
                      [1, 1, 1, 1, 1, 1, 1, 0, 0, 0],
                      [1, 1, 1, 1, 1, 1, 1, 0, 0, 0],
                      [1, 1, 0, 0, 0, 0, 0, 0, 0, 0],
                      [1, 1, 0, 0, 0, 0, 0, 0, 0, 0],
                      [1, 1, 0, 0, 0, 0, 0, 0, 0, 0],
                      [1, 1, 1, 1, 1, 1, 1, 1, 1, 0]])
vector_E = np.ndarray.flatten(letter_E)  # 將字母E的矩陣展平為向量

letter_F = np.array([[1, 1, 1, 1, 1, 1, 1, 1, 1, 0],
                      [1, 1, 0, 0, 0, 0, 0, 0, 0, 0],
                      [1, 1, 0, 0, 0, 0, 0, 0, 0, 0],
                      [1, 1, 0, 0, 0, 0, 0, 0, 0, 0],
                      [1, 1, 1, 1, 1, 1, 1, 0, 0, 0],
                      [1, 1, 1, 1, 1, 1, 1, 0, 0, 0],
                      [1, 1, 0, 0, 0, 0, 0, 0, 0, 0],
                      [1, 1, 0, 0, 0, 0, 0, 0, 0, 0],
                      [1, 1, 0, 0, 0, 0, 0, 0, 0, 0],
                      [1, 1, 0, 0, 0, 0, 0, 0, 0, 0]])
vector_F = np.ndarray.flatten(letter_F)  # 將字母F的矩陣展平為向量

letter_G = np.array([[0, 0, 1, 1, 1, 1, 1, 1, 0, 0],
                      [0, 1, 1, 0, 0, 0, 0, 0, 1, 0],
                      [1, 1, 0, 0, 0, 0, 0, 0, 0, 0],
                      [1, 1, 0, 0, 0, 0, 0, 0, 0, 0],
                      [1, 1, 0, 0, 0, 1, 1, 1, 1, 0],
                      [1, 1, 0, 0, 0, 1, 1, 1, 1, 0],
                      [1, 1, 0, 0, 0, 0, 0, 1, 1, 0],
                      [1, 1, 0, 0, 0, 0, 0, 1, 1, 0],
                      [0, 1, 1, 0, 0, 0, 1, 1, 0, 0],
                      [0, 0, 1, 1, 1, 1, 1, 0, 0, 0]])
vector_G = np.ndarray.flatten(letter_G)  # 將字母G的矩陣展平為向量

letter_H = np.array([[1, 1, 0, 0, 0, 0, 0, 0, 1, 1],
                      [1, 1, 0, 0, 0, 0, 0, 0, 1, 1],
                      [1, 1, 0, 0, 0, 0, 0, 0, 1, 1],
                      [1, 1, 0, 0, 0, 0, 0, 0, 1, 1],
                      [1, 1, 1, 1, 1, 1, 1, 1, 1, 1],
                      [1, 1, 1, 1, 1, 1, 1, 1, 1, 1],
                      [1, 1, 0, 0, 0, 0, 0, 0, 1, 1],
                      [1, 1, 0, 0, 0, 0, 0, 0, 1, 1],
                      [1, 1, 0, 0, 0, 0, 0, 0, 1, 1],
                      [1, 1, 0, 0, 0, 0, 0, 0, 1, 1]])
vector_H = np.ndarray.flatten(letter_H)  # 將字母H的矩陣展平為向量

letter_I = np.array([[0, 1, 1, 1, 1, 1, 1, 1, 1, 0],
                      [0, 0, 0, 0, 1, 1, 0, 0, 0, 0],
                      [0, 0, 0, 0, 1, 1, 0, 0, 0, 0],
                      [0, 0, 0, 0, 1, 1, 0, 0, 0, 0],
                      [0, 0, 0, 0, 1, 1, 0, 0, 0, 0],
                      [0, 0, 0, 0, 1, 1, 0, 0, 0, 0],
                      [0, 0, 0, 0, 1, 1, 0, 0, 0, 0],
                      [0, 0, 0, 0, 1, 1, 0, 0, 0, 0],
                      [0, 0, 0, 0, 1, 1, 0, 0, 0, 0],
                      [0, 1, 1, 1, 1, 1, 1, 1, 1, 0]])
vector_I = np.ndarray.flatten(letter_I)  # 將字母I的矩陣展平為向量

letter_J = np.array([[0, 0, 0, 0, 1, 1, 1, 1, 1, 0],
                      [0, 0, 0, 0, 1, 1, 1, 1, 1, 0],
                      [0, 0, 0, 0, 0, 0, 0, 1, 1, 0],
                      [0, 0, 0, 0, 0, 0, 0, 1, 1, 0],
                      [0, 0, 0, 0, 0, 0, 0, 1, 1, 0],
                      [0, 0, 0, 0, 0, 0, 0, 1, 1, 0],
                      [1, 1, 0, 0, 0, 0, 0, 1, 1, 0],
                      [1, 1, 0, 0, 0, 0, 1, 1, 0, 0],
                      [0, 1, 1, 0, 0, 1, 1, 0, 0, 0],
                      [0, 0, 1, 1, 1, 1, 0, 0, 0, 0]])
vector_J = np.ndarray.flatten(letter_J)  # 將字母J的矩陣展平為向量

letter_K = np.array([[1, 1, 0, 0, 0, 0, 1, 1, 1, 0],
                      [1, 1, 0, 0, 0, 1, 1, 1, 0, 0],
                      [1, 1, 0, 0, 1, 1, 1, 0, 0, 0],
                      [1, 1, 0, 1, 1, 1, 0, 0, 0, 0],
                      [1, 1, 1, 1, 1, 0, 0, 0, 0, 0],
                      [1, 1, 1, 1, 1, 0, 0, 0, 0, 0],
                      [1, 1, 0, 0, 1, 1, 1, 0, 0, 0],
                      [1, 1, 0, 0, 0, 1, 1, 1, 0, 0],
                      [1, 1, 0, 0, 0, 0, 1, 1, 1, 0],
                      [1, 1, 0, 0, 0, 0, 0, 1, 1, 1]])
vector_K = np.ndarray.flatten(letter_K)  # 將字母K的矩陣展平為向量
letter_L = np.array([[1, 1, 0, 0, 0, 0, 0, 0, 0, 0],
                      [1, 1, 0, 0, 0, 0, 0, 0, 0, 0],
                      [1, 1, 0, 0, 0, 0, 0, 0, 0, 0],
                      [1, 1, 0, 0, 0, 0, 0, 0, 0, 0],
                      [1, 1, 0, 0, 0, 0, 0, 0, 0, 0],
                      [1, 1, 0, 0, 0, 0, 0, 0, 0, 0],
                      [1, 1, 0, 0, 0, 0, 0, 0, 0, 0],
                      [1, 1, 0, 0, 0, 0, 0, 0, 0, 0],
                      [1, 1, 1, 1, 1, 1, 1, 1, 1, 1],
                      [1, 1, 1, 1, 1, 1, 1, 1, 1, 1]])
vector_L = np.ndarray.flatten(letter_L)  # 將字母L的矩陣展平為向量

letter_M = np.array([[1, 1, 0, 0, 0, 0, 0, 0, 1, 1],
                      [1, 1, 1, 0, 0, 0, 0, 1, 1, 1],
                      [1, 1, 1, 1, 0, 0, 1, 1, 1, 1],
                      [1, 1, 1, 1, 1, 1, 1, 1, 1, 1],
                      [1, 1, 1, 1, 1, 1, 1, 1, 1, 1],
                      [1, 1, 0, 1, 1, 1, 1, 0, 1, 1],
                      [1, 1, 0, 0, 1, 1, 0, 0, 1, 1],
                      [1, 1, 0, 0, 0, 0, 0, 0, 1, 1],
                      [1, 1, 0, 0, 0, 0, 0, 0, 1, 1],
                      [1, 1, 0, 0, 0, 0, 0, 0, 1, 1]])
vector_M = np.ndarray.flatten(letter_M)  # 將字母M的矩陣展平為向量

letter_N = np.array([[1, 1, 0, 0, 0, 0, 0, 0, 1, 1],
                      [1, 1, 1, 0, 0, 0, 0, 0, 1, 1],
                      [1, 1, 1, 1, 0, 0, 0, 0, 1, 1],
                      [1, 1, 1, 1, 1, 0, 0, 0, 1, 1],
                      [1, 1, 0, 1, 1, 1, 0, 0, 1, 1],
                      [1, 1, 0, 0, 1, 1, 1, 0, 1, 1],
                      [1, 1, 0, 0, 0, 1, 1, 1, 1, 1],
                      [1, 1, 0, 0, 0, 0, 1, 1, 1, 1],
                      [1, 1, 0, 0, 0, 0, 0, 1, 1, 1],
                      [1, 1, 0, 0, 0, 0, 0, 0, 1, 1]])
vector_N = np.ndarray.flatten(letter_N)

letter_O = np.array([[0, 0, 1, 1, 1, 1, 1, 1, 0, 0],
                      [0, 1, 1, 1, 1, 1, 1, 1, 1, 0],
                      [1, 1, 1, 0, 0, 0, 0, 1, 1, 1],
                      [1, 1, 0, 0, 0, 0, 0, 0, 1, 1],
                      [1, 1, 0, 0, 0, 0, 0, 0, 1, 1],
                      [1, 1, 0, 0, 0, 0, 0, 0, 1, 1],
                      [1, 1, 0, 0, 0, 0, 0, 0, 1, 1],
                      [1, 1, 1, 0, 0, 0, 0, 1, 1, 1],
                      [0, 1, 1, 1, 1, 1, 1, 1, 1, 0],
                      [0, 0, 1, 1, 1, 1, 1, 1, 0, 0]])
vector_O = np.ndarray.flatten(letter_O)

letter_P = np.array([[1, 1, 1, 1, 1, 1, 1, 1, 0, 0],
                      [1, 1, 0, 0, 0, 0, 0, 1, 1, 0],
                      [1, 1, 0, 0, 0, 0, 0, 1, 1, 0],
                      [1, 1, 0, 0, 0, 0, 0, 1, 1, 0],
                      [1, 1, 1, 1, 1, 1, 1, 1, 0, 0],
                      [1, 1, 0, 0, 0, 0, 0, 0, 0, 0],
                      [1, 1, 0, 0, 0, 0, 0, 0, 0, 0],
                      [1, 1, 0, 0, 0, 0, 0, 0, 0, 0],
                      [1, 1, 0, 0, 0, 0, 0, 0, 0, 0],
                      [1, 1, 0, 0, 0, 0, 0, 0, 0, 0]])
vector_P = np.ndarray.flatten(letter_P)

letter_Q = np.array([[0, 0, 1, 1, 1, 1, 1, 1, 0, 0],
                      [0, 1, 1, 1, 1, 1, 1, 1, 1, 0],
                      [1, 1, 1, 0, 0, 0, 0, 1, 1, 1],
                      [1, 1, 0, 0, 0, 0, 0, 0, 1, 1],
                      [1, 1, 0, 0, 0, 0, 0, 0, 1, 1],
                      [1, 1, 0, 0, 0, 1, 0, 0, 1, 1],
                      [1, 1, 0, 0, 0, 1, 1, 0, 1, 1],
                      [1, 1, 1, 0, 0, 0, 1, 1, 1, 1],
                      [0, 1, 1, 1, 1, 1, 1, 1, 1, 1],
                      [0, 0, 1, 1, 1, 1, 1, 1, 0, 1]])
vector_Q = np.ndarray.flatten(letter_Q)

letter_R = np.array([[1, 1, 1, 1, 1, 1, 1, 1, 0, 0],
                      [1, 1, 0, 0, 0, 0, 0, 1, 1, 0],
                      [1, 1, 0, 0, 0, 0, 0, 1, 1, 0],
                      [1, 1, 0, 0, 0, 0, 0, 1, 1, 0],
                      [1, 1, 1, 1, 1, 1, 1, 1, 0, 0],
                      [1, 1, 0, 1, 1, 1, 0, 0, 0, 0],
                      [1, 1, 0, 0, 1, 1, 1, 0, 0, 0],
                      [1, 1, 0, 0, 0, 1, 1, 1, 0, 0],
                      [1, 1, 0, 0, 0, 0, 1, 1, 1, 0],
                      [1, 1, 0, 0, 0, 0, 0, 1, 1, 1]])
vector_R = np.ndarray.flatten(letter_R)

letter_S = np.array([[0, 1, 1, 1, 1, 1, 1, 1, 0, 0],
                      [1, 1, 1, 0, 0, 0, 0, 1, 1, 0],
                      [1, 1, 0, 0, 0, 0, 0, 0, 0, 0],
                      [1, 1, 1, 0, 0, 0, 0, 0, 0, 0],
                      [0, 1, 1, 1, 1, 1, 1, 0, 0, 0],
                      [0, 0, 0, 1, 1, 1, 1, 1, 0, 0],
                      [0, 0, 0, 0, 0, 0, 1, 1, 1, 0],
                      [0, 0, 0, 0, 0, 0, 0, 1, 1, 0],
                      [1, 1, 0, 0, 0, 0, 1, 1, 1, 0],
                      [0, 1, 1, 1, 1, 1, 1, 1, 0, 0]])
vector_S = np.ndarray.flatten(letter_S)

letter_T = np.array([[1, 1, 1, 1, 1, 1, 1, 1, 1, 1],
                      [1, 1, 1, 1, 1, 1, 1, 1, 1, 1],
                      [0, 0, 0, 0, 1, 1, 0, 0, 0, 0],
                      [0, 0, 0, 0, 1, 1, 0, 0, 0, 0],
                      [0, 0, 0, 0, 1, 1, 0, 0, 0, 0],
                      [0, 0, 0, 0, 1, 1, 0, 0, 0, 0],
                      [0, 0, 0, 0, 1, 1, 0, 0, 0, 0],
                      [0, 0, 0, 0, 1, 1, 0, 0, 0, 0],
                      [0, 0, 0, 0, 1, 1, 0, 0, 0, 0],
                      [0, 0, 0, 0, 1, 1, 0, 0, 0, 0]])
vector_T = np.ndarray.flatten(letter_T)

letter_U = np.array([[1, 1, 0, 0, 0, 0, 0, 0, 1, 1],
                      [1, 1, 0, 0, 0, 0, 0, 0, 1, 1],
                      [1, 1, 0, 0, 0, 0, 0, 0, 1, 1],
                      [1, 1, 0, 0, 0, 0, 0, 0, 1, 1],
                      [1, 1, 0, 0, 0, 0, 0, 0, 1, 1],
                      [1, 1, 0, 0, 0, 0, 0, 0, 1, 1],
                      [1, 1, 0, 0, 0, 0, 0, 0, 1, 1],
                      [1, 1, 0, 0, 0, 0, 0, 0, 1, 1],
                      [0, 1, 1, 1, 1, 1, 1, 1, 1, 0],
                      [0, 0, 1, 1, 1, 1, 1, 1, 0, 0]])
vector_U = np.ndarray.flatten(letter_U)

letter_V = np.array([[1, 1, 0, 0, 0, 0, 0, 0, 1, 1],
                      [1, 1, 0, 0, 0, 0, 0, 0, 1, 1],
                      [1, 1, 0, 0, 0, 0, 0, 0, 1, 1],
                      [1, 1, 0, 0, 0, 0, 0, 0, 1, 1],
                      [0, 1, 1, 0, 0, 0, 0, 1, 1, 0],
                      [0, 1, 1, 0, 0, 0, 0, 1, 1, 0],
                      [0, 0, 1, 1, 0, 0, 1, 1, 0, 0],
                      [0, 0, 1, 1, 0, 0, 1, 1, 0, 0],
                      [0, 0, 0, 1, 1, 1, 1, 0, 0, 0],
                      [0, 0, 0, 0, 1, 1, 0, 0, 0, 0]])
vector_V = np.ndarray.flatten(letter_V)

letter_W = np.array([[1, 1, 0, 0, 0, 0, 0, 0, 1, 1],
                      [1, 1, 0, 0, 0, 0, 0, 0, 1, 1],
                      [1, 1, 0, 0, 0, 0, 0, 0, 1, 1],
                      [1, 1, 0, 0, 0, 0, 0, 0, 1, 1],
                      [1, 1, 0, 0, 1, 1, 0, 0, 1, 1],
                      [1, 1, 0, 1, 1, 1, 1, 0, 1, 1],
                      [1, 1, 1, 1, 0, 0, 1, 1, 1, 1],
                      [1, 1, 1, 0, 0, 0, 0, 1, 1, 1],
                      [1, 1, 0, 0, 0, 0, 0, 0, 1, 1],
                      [1, 1, 0, 0, 0, 0, 0, 0, 1, 1]])
vector_W = np.ndarray.flatten(letter_W)

letter_X = np.array([[1, 1, 0, 0, 0, 0, 0, 0, 1, 1],
                      [0, 1, 1, 0, 0, 0, 0, 1, 1, 0],
                      [0, 0, 1, 1, 0, 0, 1, 1, 0, 0],
                      [0, 0, 0, 1, 1, 1, 1, 0, 0, 0],
                      [0, 0, 0, 0, 1, 1, 0, 0, 0, 0],
                      [0, 0, 0, 0, 1, 1, 0, 0, 0, 0],
                      [0, 0, 0, 1, 1, 1, 1, 0, 0, 0],
                      [0, 0, 1, 1, 0, 0, 1, 1, 0, 0],
                      [0, 1, 1, 0, 0, 0, 0, 1, 1, 0],
                      [1, 1, 0, 0, 0, 0, 0, 0, 1, 1]])
vector_X = np.ndarray.flatten(letter_X)

letter_Y = np.array([[1, 1, 0, 0, 0, 0, 0, 0, 1, 1],
                      [0, 1, 1, 0, 0, 0, 0, 1, 1, 0],
                      [0, 0, 1, 1, 0, 0, 1, 1, 0, 0],
                      [0, 0, 0, 1, 1, 1, 1, 0, 0, 0],
                      [0, 0, 0, 0, 1, 1, 0, 0, 0, 0],
                      [0, 0, 0, 0, 1, 1, 0, 0, 0, 0],
                      [0, 0, 0, 0, 1, 1, 0, 0, 0, 0],
                      [0, 0, 0, 0, 1, 1, 0, 0, 0, 0],
                      [0, 0, 0, 0, 1, 1, 0, 0, 0, 0],
                      [0, 0, 0, 0, 1, 1, 0, 0, 0, 0]])
vector_Y = np.ndarray.flatten(letter_Y)

letter_Z = np.array([[1, 1, 1, 1, 1, 1, 1, 1, 1, 1],
                      [0, 0, 0, 0, 0, 0, 0, 1, 1, 0],
                      [0, 0, 0, 0, 0, 0, 1, 1, 0, 0],
                      [0, 0, 0, 0, 0, 1, 1, 0, 0, 0],
                      [0, 0, 0, 0, 1, 1, 0, 0, 0, 0],
                      [0, 0, 0, 1, 1, 0, 0, 0, 0, 0],
                      [0, 0, 1, 1, 0, 0, 0, 0, 0, 0],
                      [0, 1, 1, 0, 0, 0, 0, 0, 0, 0],
                      [1, 1, 0, 0, 0, 0, 0, 0, 0, 0],
                      [1, 1, 1, 1, 1, 1, 1, 1, 1, 1]])
vector_Z = np.ndarray.flatten(letter_Z)
#生成權重
num_inp = len(vector_A)
print(num_inp)  # 輸入向量的長度
num_outp = 26  # 輸出類別數     
np.random.seed()  # 設置隨機種子
wei = np.random.uniform(size=(num_inp, num_outp))  # 隨機生成權重矩陣

# 正規化權重
def normalize_wei(wei):
    for i in range(num_outp):  
        wei1 = np.sum(wei[:, i])  
        wei[:, i] = wei[:, i] / wei1  
    return wei
wei = normalize_wei(wei)  # 正規化權重

# 計算神經元輸出
def calculate_output(inp, wei):
    a = 1 # 激活函數的斜率
    memP = np.dot(inp, wei) 
    bias = sum(memP) / num_outp  
    return 1 / (1 + np.exp(-a * (memP - bias)))  


# # 定義softmax函數
# def softmax(x):
#     expX = np.exp(x)  # 計算指數
#     sumExpX = sum(expX)  # 計算指數的總和
#     return expX / sumExpX  # 返回softmax結果

# print(outA)
# print(np.argmax(output_A))
# winner=np.random.multinomial(1,outA)
# print(winner)

# #計算字母A、B、M的輸出
output_A_before = calculate_output(vector_A, wei)  
print(f'尚未經過學習前的輸出矩陣:\n{output_A_before}')
# outA = softmax(output_A)  
# output_B = calculate_output(vector_B, wei)  
# print(output_B)
# outB = softmax(output_B)  
# output_M = calculate_output(vector_M, wei)  
# print(output_M)
# outM = softmax(output_M)  



## Hebb學習 ##
gamma = 0.5  # 學習率

# 定義Hebb學習函數
def Hebb_learning(inV, outV, wei_old):
    delta_wei = gamma * np.outer(inV, outV)  # 計算權重的變化量
    wei_new = wei_old + delta_wei  # 更新權重
    wei_new = normalize_wei(wei_new)  # 正規化更新後的權重
    return wei_new

vactors = [vector_A,vector_B,vector_C, vector_D, vector_E, vector_F, vector_G, vector_H,
                  vector_I, vector_J, vector_K, vector_L, vector_M, vector_N,
                  vector_O, vector_P, vector_Q, vector_R, vector_S, vector_T,
                  vector_U, vector_V, vector_W, vector_X, vector_Y, vector_Z]


# 執行Hebb學習
for i in range(100):  # 重複10次
    for vector in (vactors):
        output = calculate_output(vector, wei)
        winner = np.argmax(output)  # 競爭機制：選擇輸出值最大的神經元為贏家
        Out = [0] * 26
        Out[winner] = 1  # 只有獲勝神經元的輸出為1
        wei = Hebb_learning(vector, Out, wei)


# # 最終輸出計算
output_A_after =calculate_output(vector_A, wei)
print(f'經過學習後的輸出矩陣:\n{output_A_after}')
# # output_B = calculate_output(vector_B, wei) 
# # print(output_B)
# # output_M = calculate_output(vector_M, wei)  
# # print(output_M)

# winnerA = np.argmax(output_A_after)  
# weiA = wei[:, winnerA]  
# weiA = np.reshape(weiA, (10, 10))  
# plt.imshow(weiA)  
# plt.colorbar()  
# plt.show()  


plt.figure(figsize=(18, 12))

vectors = [vector_A, vector_B, vector_C, vector_D, vector_E, vector_F, 
          vector_G, vector_H, vector_I, vector_J, vector_K, vector_L,
          vector_M, vector_N, vector_O, vector_P, vector_Q, vector_R,
          vector_S, vector_T, vector_U, vector_V, vector_W, vector_X,
          vector_Y, vector_Z]
labels = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L',
          'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X',
          'Y', 'Z']


for i, (vector, label) in enumerate(zip(vectors, labels)):
    plt.subplot(5, 6, i+1)
    
    output = calculate_output(vector, wei)
    winner = np.argmax(output)
    weight = wei[:, winner]
    weight = np.reshape(weight, (10, 10))
    
    plt.imshow(weight)
    plt.title(f'Letter {label}')
    plt.axis('off')


plt.tight_layout()
plt.show()





