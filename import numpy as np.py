import numpy as np
import matplotlib.pyplot as plt
from matplotlib.animation import FuncAnimation
import time # 引入 time 模組 (雖然原始碼沒有用到，但保留以備將來擴展)

# --- 迷宮初始化 ---
fig = plt.figure(figsize=(5, 5))
ax = plt.gca() # get current axis (獲取當前圖形的軸)

def ini_maze():
    """初始化並繪製迷宮的牆壁和狀態標籤"""
    ax.clear() # 清除之前的繪圖，防止重複繪製
    ax.set_xlim(0,3)
    ax.set_ylim(0,3)
    # 隱藏X、Y軸的刻度標籤和刻度線
    ax.tick_params(axis='both', which='both', bottom=False, top=False,
                   left=False, right=False, labelbottom=False, labelleft=False)

    # 繪製迷宮牆壁
    # plt.plot([x1, x2], [y1, y2]) 用於繪製從 (x1, y1) 到 (x2, y2) 的線段
    plt.plot([1,1],[0,1],color = "red",linewidth = 2) # 牆壁 1: 從 (1,0) 到 (1,1)
    plt.plot([1,2],[2,2],color = "red",linewidth = 2) # 牆壁 2: 從 (1,2) 到 (2,2)
    plt.plot([2,2],[2,1],color = "red",linewidth = 2) # 牆壁 3: 從 (2,2) 到 (2,1)
    plt.plot([2,3],[1,1],color = "red",linewidth = 2) # 牆壁 4: 從 (2,1) 到 (3,1)

    # 標示狀態 (State) 編號和起點/終點
    # plt.text(x, y, text) 在座標 (x, y) 處添加文字
    # ha='center', va='center' 表示文字水平和垂直居中對齊
    plt.text(0.5,2.5,"S0",fontsize = 14, ha = "center", va = "center")
    plt.text(0.5, 2.3, "START", fontsize=10, ha="center", va="center") # 標示起點
    plt.text(1.5,2.5,"S1",fontsize = 14, ha = "center", va = "center")
    plt.text(2.5,2.5,"S2",fontsize = 14, ha = "center", va = "center")
    plt.text(0.5,1.5,"S3",fontsize = 14, ha = "center", va = "center")
    plt.text(1.5,1.5,"S4",fontsize = 14, ha = "center", va = "center")
    plt.text(2.5,1.5,"S5",fontsize = 14, ha = "center", va = "center")
    plt.text(0.5,0.5,"S6",fontsize = 14, ha = "center", va = "center")
    plt.text(1.5,0.5,"S7",fontsize = 14, ha = "center", va = "center")
    plt.text(2.5,0.5,"S8",fontsize = 14, ha = "center", va = "center")
    plt.text(2.5, 0.7, "FINISH", fontsize=10, ha="center", va="center") # 標示終點

# --- 策略參數與函數 ---

# 動作定義: 0:上, 1:右, 2:下, 3:左
# theta[s, a] 代表在狀態 s 採取動作 a 的傾向（參數）
# 初始策略參數 theta_0
# 維度為 (狀態數, 動作數) = (9, 4) 對應 S0 到 S8
# np.nan 表示該狀態下無法執行該動作（撞牆或超出邊界）
# 數值 1.0 (或其他非 NaN 值) 表示初始時認為該動作可行
theta_0 = np.array([
    # S0: 只能 向右(1), 向下(2)
    [np.nan, 1,      1,      np.nan],
    # S1: 只能 向右(1), 向左(3)
    [np.nan, 1,      np.nan, 1],
    # S2: 只能 向下(2), 向左(3)
    [np.nan, np.nan, 1,      1],
    # S3: 只能 向上(0), 向右(1), 向下(2)
    [1,      1,      1,      np.nan],
    # S4: 只能 向下(2), 向左(3)  <- 根據牆壁 (2,1) 到 (2,2) 和 (1,2) 到 (2,2)
    [np.nan, np.nan, 1,      1], # 原始碼的設定
    # [np.nan, 1,      1,      1], # 修正後：向上(S1)撞牆, 向右(S5)可行, 向下(S7)可行, 向左(S3)可行
    # S5: 只能 向上(0), 向左(3) <- 根據牆壁 (2,1) 到 (3,1)
    [1,      np.nan, np.nan, 1], # 原始碼的設定
    # [1,      np.nan, 1,      1], # 修正後：向上(S2)可行, 向右撞牆, 向下(S8)可行, 向左(S4)可行
    # S6: 只能 向上(0)
    [1,      np.nan, np.nan, np.nan],
    # S7: 只能 向上(0), 向右(1)
    [1,      1,      np.nan, np.nan],
    # S8: 終點，無需動作
    [np.nan, np.nan, np.nan, np.nan]
])
# 注意：原始碼在 S4, S5 的可行動作與迷宮圖示似乎有出入，這裡暫時保留原始碼的設定，
# 但在註解中標示了根據圖示修正後的版本。如果學習效果不佳，可以嘗試更換為修正後的版本。

def softmax(theta):
    """將策略參數 theta 轉換為行動機率 pi (使用 Softmax 函數)"""
    beta = 1.0 # Softmax 的溫度參數，控制機率分佈的平滑度
    [m, n] = theta.shape # m: 狀態數, n: 動作數
    pi = np.zeros((m, n)) # 初始化機率矩陣

    # 對 theta 中的每個元素計算 exp(theta * beta)
    # np.exp 對 NaN 計算結果仍為 NaN
    exp_theta = np.exp(theta * beta)

    # 遍歷每個狀態 s (每一列)
    for i in range(0, m):
        # 計算分母 sum_exp 時，使用 np.nansum 忽略 NaN 值
        sum_exp = np.nansum(exp_theta[i, :])

        # 避免除以零 (如果一個狀態所有方向都是 NaN 或 exp 結果為 0)
        if sum_exp == 0:
            # 如果無法計算機率 (例如 S8 或完全被困住的狀態)，將機率設為 0
            pi[i, :] = 0
        else:
            # 計算 Softmax 機率：exp(theta_i) / sum(exp(theta))
            pi[i, :] = exp_theta[i, :] / sum_exp

    # 將計算過程中可能產生的 NaN (來自 exp_theta 中的 NaN / sum_exp) 轉換為 0
    # 這確保了不可行動作的機率為 0
    pi = np.nan_to_num(pi)

    # (可選但推薦) 重新正規化確保每行總和嚴格為 1 (處理浮點數精度問題)
    row_sums = pi.sum(axis=1, keepdims=True)
    # 處理總和為 0 的情況 (如 S8)，避免除以零
    safe_row_sums = np.where(row_sums == 0, 1, row_sums)
    pi = pi / safe_row_sums

    return pi

# 動作名稱列表 (與索引對應: 0:上, 1:右, 2:下, 3:左)
action_names = ["up", "right", "down", "left"]

def choose_action_and_get_next_state(pi, s):
    """根據狀態 s 和策略 pi 選擇動作，並計算下一個狀態"""
    # 檢查當前狀態是否為終點
    if s == 8:
        return None, s # 終點無需動作

    current_pi = pi[s, :] # 獲取當前狀態的動作機率分佈

    # 檢查機率分佈是否有效 (總和是否接近 1)
    if not np.isclose(np.sum(current_pi), 1.0):
        # 如果機率總和不為 1 (可能因為 theta 全為 NaN 或計算錯誤)
        print(f"警告：狀態 S{s} 的機率總和 ({np.sum(current_pi)}) 不為 1。嘗試從有效動作中隨機選擇。")
        # 找出在 theta_0 中定義為可行的動作
        available_actions = np.where(~np.isnan(theta_0[s, :]))[0]
        if len(available_actions) == 0:
            print(f"錯誤：狀態 S{s} 沒有可行的動作！")
            return None, s # 無法移動
        # 從可行動作中隨機選擇一個
        action = np.random.choice(available_actions)
        print(f"隨機選擇的動作: {action_names[action]} ({action})")
    else:
        # 機率有效，根據機率分佈隨機選擇一個動作索引
        try:
            action = np.random.choice(np.arange(len(action_names)), p=current_pi)
        except ValueError as e:
            # 如果 p 的總和不為 1，np.random.choice 會報錯
            print(f"錯誤發生在狀態 S{s}，機率分佈：{current_pi}")
            print(f"錯誤訊息：{e}")
            # 備用策略：選擇機率最大的可行動作或隨機選擇
            available_actions = np.where(current_pi > 0)[0]
            if len(available_actions) == 0: # 如果所有機率都是0
                 available_actions = np.where(~np.isnan(theta_0[s, :]))[0] # 退回使用 theta_0
            if len(available_actions) == 0:
                 print(f"嚴重錯誤：狀態 S{s} 無法選擇動作！")
                 return None, s
            action = np.random.choice(available_actions) # 隨機選一個可行的
            print(f"錯誤後隨機選擇動作: {action_names[action]} ({action})")


    # 根據選擇的動作計算下一個狀態 s_next
    if action == 0: # 上
        s_next = s - 3
    elif action == 1: # 右
        s_next = s + 1
    elif action == 2: # 下
        s_next = s + 3
    elif action == 3: # 左
        s_next = s - 1
    else: # 理論上不會發生
        print(f"警告：選擇了無效的動作索引 {action}")
        s_next = s # 保持原地

    # 邊界檢查和牆壁檢查 (雖然 theta_0 和 pi 應已處理，多一層保險更好)
    # 簡單檢查：確保 s_next 在 0-8 範圍內
    if not (0 <= s_next <= 8):
        print(f"警告：從 S{s} 執行動作 {action_names[action]} 導致無效狀態 {s_next}。保持在 S{s}。")
        s_next = s # 如果移動無效，停在原地
    # 更精確的檢查應再次核對 theta_0[s, action] 是否為 NaN

    return action, s_next

def run_maze(pi):
    """根據策略 pi 模擬一次智能體在迷宮中的行走過程"""
    s = 0 # 從狀態 S0 開始
    history = [] # 用於儲存 (狀態, 動作, 下一個狀態) 的序列
    step = 0 # 記錄步數
    max_steps = 100 # 設定最大步數，防止無限迴圈

    while s != 8 and step < max_steps: # 當前狀態不是終點 S8 且未達最大步數
        action, next_s = choose_action_and_get_next_state(pi, s)

        if action is None: # 如果無法選擇動作 (例如在終點或卡住)
            print(f"在狀態 S{s} 無法移動，模擬終止。")
            break

        # 記錄這一步的 (狀態, 動作, 下一個狀態)
        history.append((s, action, next_s))

        # 更新當前狀態
        s = next_s
        step += 1

    # 檢查是否因為達到最大步數而終止
    if step >= max_steps:
        print(f"達到最大步數 ({max_steps})，模擬終止。可能陷入迴圈。")

    return history

# --- 策略更新 ---
def update_theta(theta, pi, history):
    """根據歷史記錄 history 和當前策略 pi 更新策略參數 theta"""
    eta = 0.1 # 學習率 (learning rate)
    T = len(history) # 總步數 (一個 episode 的長度)

    # 如果 history 為空 (例如一開始就在終點)，則不更新
    if T == 0:
        return theta

    [m, n] = theta.shape # m: 狀態數, n: 動作數
    delta_theta = np.zeros_like(theta) # 初始化 theta 的變化量矩陣，大小與 theta 相同，元素全為 0

    # --- 計算 N(s) 和 N(s, a) ---
    # N(s): 狀態 s 在 history 中出現的次數
    # N(s, a): 狀態-動作對 (s, a) 在 history 中出現的次數
    N_s = np.zeros(m)
    N_sa = np.zeros((m, n))

    # 遍歷 history 中的每一步 (state, action, next_state)
    for step_data in history:
        state, action, _ = step_data # 解包元組，只需要 state 和 action
        # 確保 state 和 action 索引有效
        if 0 <= state < m:
            N_s[state] += 1
            if 0 <= action < n:
                N_sa[state, action] += 1
            else:
                 print(f"警告: history 中發現無效 action {action} (state={state})")
        else:
            print(f"警告: history 中發現無效 state {state}")


    # --- 更新 delta_theta ---
    # 遍歷所有狀態 s
    for i in range(m):
        # 只更新在本回合中訪問過的狀態 (N_s[i] > 0)
        if N_s[i] > 0:
            # 遍歷所有動作 a
            for j in range(n):
                # 只更新在 theta_0 中定義為可行的動作 (非 NaN)
                if not np.isnan(theta_0[i, j]):
                    # 根據原始碼的更新規則計算 delta_theta
                    # 這個規則類似於比較實際執行的動作頻率 (N_ij / T) 與策略期望的頻率 (pi_ij * N_i / T)
                    # 注意：這不是標準的 Policy Gradient 或 Q-learning 更新，其收斂性需視具體問題而定
                    delta_theta[i, j] = (N_sa[i, j] - pi[i, j] * N_s[i]) / T

    # --- 更新 theta ---
    # 使用學習率 eta 更新 theta
    new_theta = theta + eta * delta_theta

    # 關鍵：確保 theta 中原本是 NaN 的位置在更新後仍然是 NaN
    # 使用 np.where: 如果 theta_0 是 NaN，則 new_theta 對應位置設為 NaN，否則保留 new_theta 的計算結果
    new_theta = np.where(np.isnan(theta_0), np.nan, new_theta)

    return new_theta

# --- 主學習迴圈 ---
theta = theta_0.copy() # 從初始策略參數開始學習
max_episodes = 100 # 設定學習的回合數 (episodes)
final_history_for_animation = [] # 儲存最後一回合的路徑用於動畫顯示

print("開始學習...")
for episode in range(max_episodes):
    # 1. 根據當前的策略參數 theta 計算策略機率 pi
    pi = softmax(theta)

    # 2. 使用當前策略 pi 運行迷宮，獲取一個回合的歷史記錄 history
    history = run_maze(pi)

    # 3. 根據得到的 history 和 pi 更新策略參數 theta
    theta = update_theta(theta, pi, history)

    # 打印學習進度 (例如每 10 回合打印一次)
    if (episode + 1) % 10 == 0:
        print(f"Episode {episode + 1}/{max_episodes} 完成。歷史步數: {len(history)}")
        # 可以取消註解以下行來觀察 theta 和 pi 的變化
        # print("當前 Theta (部分):")
        # print(np.round(theta[:4], 3)) # 只顯示前 4 個狀態的 theta
        # print("當前 Pi (部分):")
        # print(np.round(pi[:4], 3)) # 只顯示前 4 個狀態的 pi

    # 儲存最後一回合的歷史記錄，用於動畫展示
    if episode == max_episodes - 1:
        # 從 history (state, action, next_state) 中提取狀態序列
        if history: # 確保 history 不是空的
            final_history_for_animation = [step[0] for step in history] # 取出每一步的起始狀態
            final_history_for_animation.append(history[-1][2]) # 加入最後一步的目標狀態
        else:
            # 如果 history 是空的 (例如一開始就在終點)，動畫列表只包含初始狀態
            final_history_for_animation = [0]


print("\n學習完成！")
print("最終 Theta:")
print(np.round(theta, 3))
final_pi = softmax(theta) # 計算最終策略
print("\n最終 Pi:")
print(np.round(final_pi, 3))

# --- 動畫顯示學習結果 ---

# 檢查是否有有效的路徑歷史記錄用於顯示
if final_history_for_animation:
    print("\n顯示學習結果路徑動畫...")

    # 重新初始化迷宮繪圖 (確保背景乾淨)
    ini_maze()

    # 初始化代表智能體的藍色圓點 'line' 對象
    # 初始位置設在第一個狀態，或者設為空列表 ([]) 也可以
    start_state = final_history_for_animation[0]
    start_x = (start_state % 3) + 0.5
    start_y = 2.5 - int(start_state / 3)
    line, = ax.plot([start_x], [start_y], marker="o", markersize=20, color="lightblue", linestyle='None') # 初始大小設小一點

    def update_anim(frame_index):
        """動畫更新函數，每一幀調用一次"""
        # 從歷史記錄中獲取當前幀對應的狀態
        state = final_history_for_animation[frame_index]

        # 計算該狀態在圖中的中心座標 (x, y)
        x = (state % 3) + 0.5
        y = 2.5 - int(state / 3)

        # 更新藍色圓點的位置
        # set_data 需要傳入 x 和 y 的列表或陣列
        line.set_data([x], [y])

        # 更新圖標題，顯示當前步數和狀態
        ax.set_title(f"Step: {frame_index}, State: S{state}")

        # 返回被更新的圖形元素 (對於 blit=True 模式是必要的)
        return line,

    # 創建動畫
    # FuncAnimation(fig, update_func, frames, interval, blit, repeat)
    # fig: 動畫所在的圖形
    # update_anim: 每幀調用的更新函數
    # frames: 動畫的總幀數 (等於路徑長度)
    # interval: 每幀之間的間隔時間 (毫秒)
    # blit: 是否使用 blitting 優化繪圖 (True 可能更快，但有時會出錯)
    # repeat: 動畫是否重複播放
    anim = None # 初始化動畫變數
    try:
        print("嘗試創建動畫 (blit=True)...")
        anim = FuncAnimation(fig, update_anim, frames=len(final_history_for_animation),
                             interval=300, blit=True, repeat=False)
        plt.show() # 顯示動畫
    except Exception as e_blit:
        print(f"創建動畫時發生錯誤 (blit=True): {e_blit}")
        print("嘗試禁用 blit (blit=False)...")

        # 如果 blit=True 失敗，嘗試 blit=False
        # blit=False 會重新繪製整個圖形，通常兼容性更好但可能稍慢
        try:
            # 確保 figure 仍然可用，如果 plt.show() 關閉了窗口，可能需要重新創建
            if not plt.fignum_exists(fig.number):
                 print("錯誤：Figure 在嘗試 blit=False 前已關閉。")
                 raise RuntimeError("Figure closed unexpectedly")

            # 重新繪製迷宮背景和線條，因為 blit=False 需要完整的背景
            ini_maze()
            line, = ax.plot([], [], marker="o", markersize=20, color="lightblue", linestyle='None') # 重新創建 line

            anim = FuncAnimation(fig, update_anim, frames=len(final_history_for_animation),
                                 interval=300, blit=False, repeat=False)
            plt.show()
        except Exception as e_no_blit:
            print(f"創建動畫時仍然失敗 (blit=False): {e_no_blit}")
            print("無法顯示動畫。")

else:
    print("\n沒有生成有效的路徑來顯示動畫 (final_history_for_animation 為空)。")

