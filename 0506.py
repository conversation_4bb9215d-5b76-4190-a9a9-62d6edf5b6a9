import numpy as np
import matplotlib.pyplot as plt
import random


try:
    plt.rcParams['font.sans-serif'] = ['Arial Unicode MS'] # 優先使用此字體
    plt.rcParams['axes.unicode_minus'] = False  # 解決負號顯示問題
except Exception:
    print("警告：未能成功設定中文字體 'Arial Unicode MS'。圖表中的中文可能無法正常顯示。")
    print("請嘗試安裝 'Arial Unicode MS' 字體，或修改程式碼中的 plt.rcParams['font.sans-serif'] 設定。")
    plt.rcParams['axes.unicode_minus'] = False # 即使字體失敗，也嘗試解決負號問題


class IGTEnvironment:
    """
    定義愛荷華賭博任務 (IGT) 的環境。
    包含四個牌組 (A, B, C, D) 的獎懲規則。
    """
    def __init__(self):
        # 定義每個牌組的獎勵和懲罰
        # 牌組 A, B: 高立即獎勵，但有更高的偶發性高額懲罰 (長期不利)
        # 牌組 C, D: 低立即獎勵，但偶發性懲罰較低 (長期有利)
        self.deck_configs = {
            0: {'name': '牌組 A', 'reward_mean': 100, 'loss_prob': 0.5, 'loss_fix': -250, 'loss_range': (-25, 25)}, # 不利, 平均懲罰 -125 (0.5 * -250)
            1: {'name': '牌組 B', 'reward_mean': 100, 'loss_prob': 0.1, 'loss_fix': -1250, 'loss_range': (-50, 50)},# 不利, 平均懲罰 -125 (0.1 * -1250)
            2: {'name': '牌組 C', 'reward_mean': 50,  'loss_prob': 0.5, 'loss_fix': -50, 'loss_range': (-10, 10)},  # 有利, 平均懲罰 -25 (0.5 * -50)
            3: {'name': '牌組 D', 'reward_mean': 50,  'loss_prob': 0.1, 'loss_fix': -25, 'loss_range': (-5, 5)}   # 有利, 平均懲罰 -2.5 (0.1 * -25) -> 調整使其更明顯有利
        }
        # 調整牌組 D 的懲罰使其更具吸引力 (此調整已在原始碼中，此處保留註釋)
        # self.deck_configs[3]['loss_fix'] = -25 

        self.n_decks = len(self.deck_configs)
        self._calculate_expected_values()

    def _calculate_expected_values(self):
        print("各牌組的理論期望值：")
        for i in range(self.n_decks):
            config = self.deck_configs[i]
            expected_loss = config['loss_prob'] * config['loss_fix']
            expected_value = config['reward_mean'] + expected_loss
            print(f"{config['name']}: {expected_value:.2f}")
        print("-" * 20)


    def step(self, action):
        """
        執行一個行動 (選擇一個牌組)，返回獎勵。
        Args:
            action (int): 選擇的牌組索引 (0-3)。
        Returns:
            reward (int): 該次選擇獲得的淨獎勵 (獎勵 - 可能的懲罰)。
        """
        if action not in self.deck_configs:
            raise ValueError("無效的行動 (牌組索引)")

        config = self.deck_configs[action]
        reward = config['reward_mean']

        loss = 0
        if random.random() < config['loss_prob']:
            loss_variation = random.randint(config['loss_range'][0], config['loss_range'][1])
            loss = config['loss_fix'] + loss_variation

        net_reward = reward + loss
        return net_reward

class QLearningAgent:
    """
    實現一個簡單的 Q-learning 智能體。
    """
    def __init__(self, n_actions, learning_rate=0.1, discount_factor=0.9, epsilon=1.0, epsilon_decay=0.995, epsilon_min=0.01):
        self.n_actions = n_actions
        self.lr = learning_rate
        self.gamma = discount_factor
        self.epsilon = epsilon
        self.epsilon_decay = epsilon_decay
        self.epsilon_min = epsilon_min
        self.q_table = np.zeros(n_actions)

    def choose_action(self):
        if random.random() < self.epsilon:
            action = random.randint(0, self.n_actions - 1)
        else:
            max_q_value = np.max(self.q_table)
            best_actions = np.where(self.q_table == max_q_value)[0]
            action = random.choice(best_actions)
        return action

    def update_q_table(self, action, reward):
        current_q = self.q_table[action]
        max_future_q = np.max(self.q_table)
        new_q = current_q + self.lr * (reward + self.gamma * max_future_q - current_q)
        self.q_table[action] = new_q

        if self.epsilon > self.epsilon_min:
            self.epsilon *= self.epsilon_decay
            if self.epsilon < self.epsilon_min:
                self.epsilon = self.epsilon_min

# --- 模擬設定 ---
n_trials = 200
agent = QLearningAgent(n_actions=4,
                       learning_rate=0.1,
                       discount_factor=0.6,
                       epsilon=1.0,
                       epsilon_decay=0.99,
                       epsilon_min=0.05)
env = IGTEnvironment()

# --- 記錄數據 ---
choices = []
rewards_per_trial = []
total_rewards_accumulated = 0
cumulative_rewards_history = []
q_values_history = []
epsilon_history = []

# --- 執行模擬 ---
print(f"開始進行 {n_trials} 次試驗的 IGT 模擬...")
for trial in range(n_trials):
    action = agent.choose_action()
    choices.append(action)

    reward = env.step(action)
    rewards_per_trial.append(reward)
    total_rewards_accumulated += reward
    cumulative_rewards_history.append(total_rewards_accumulated)

    agent.update_q_table(action, reward)

    q_values_history.append(agent.q_table.copy())
    epsilon_history.append(agent.epsilon)

    if (trial + 1) % (n_trials // 10) == 0:
        print(f"試驗 {trial + 1}/{n_trials} 完成。累積獎勵: {total_rewards_accumulated}, Epsilon: {agent.epsilon:.3f}")

print("模擬完成。")
print(f"最終 Q 值: {agent.q_table}")
for i in range(env.n_decks):
    print(f"  {env.deck_configs[i]['name']}: {agent.q_table[i]:.2f}")
print(f"總獲得獎勵: {total_rewards_accumulated}")

# --- 結果視覺化 ---
q_values_history_array = np.array(q_values_history)
deck_names = [env.deck_configs[i]['name'] for i in range(env.n_decks)]

plt.figure(figsize=(18, 8)) # 調整畫布大小以適應 2x3 佈局

# 1. 累積獎勵圖
plt.subplot(2, 3, 1) # 改為 2x3 網格中的第 1 個
plt.plot(cumulative_rewards_history, linewidth=2)
plt.title('智能體的累積獎勵變化', fontsize=14)
plt.xlabel('試驗次數', fontsize=12)
plt.ylabel('累積獎勵', fontsize=12)
plt.grid(True, linestyle='--', alpha=0.7)

# 2. 牌組選擇頻率圖
plt.subplot(2, 3, 2) # 改為 2x3 網格中的第 2 個
num_segments = 10
segment_length = n_trials // num_segments
bins = np.arange(0, n_trials + 1, segment_length)
if bins[-1] < n_trials:
    bins = np.append(bins, n_trials)
    num_segments = len(bins) -1

choice_counts = np.zeros((num_segments, env.n_decks))
bin_labels = []

for i in range(num_segments):
    start = bins[i]
    end = bins[i+1]
    segment_choices = choices[start:end]
    for j in range(env.n_decks):
        choice_counts[i, j] = segment_choices.count(j)
    bin_labels.append(f'{start+1}-{end}')

bottom = np.zeros(num_segments)
colors = plt.cm.get_cmap('viridis', env.n_decks)

for i in range(env.n_decks):
    plt.bar(bin_labels, choice_counts[:, i], bottom=bottom, label=deck_names[i], color=colors(i/env.n_decks))
    bottom += choice_counts[:, i]

plt.title('牌組選擇頻率 (分段)', fontsize=14)
plt.xlabel('試驗區間', fontsize=12)
plt.ylabel('選擇次數', fontsize=12)
plt.xticks(rotation=45, ha='right', fontsize=10)
plt.legend(title='牌組', fontsize=10, title_fontsize=11)
plt.grid(axis='y', linestyle='--', alpha=0.7)
# plt.tight_layout(rect=[0, 0.03, 1, 0.95]) # 暫時移除單獨的 tight_layout，由最後的全局調用處理

# 3. 最終 Q 值比較
plt.subplot(2, 3, 3) # 改為 2x3 網格中的第 3 個
bar_colors = [colors(i/env.n_decks) for i in range(env.n_decks)]
plt.bar(deck_names, agent.q_table, color=bar_colors)
plt.title('最終 Q 值', fontsize=14)
plt.xlabel('牌組', fontsize=12)
plt.ylabel('估計的長期價值 (Q 值)', fontsize=12)
plt.grid(axis='y', linestyle='--', alpha=0.7)
for i, v in enumerate(agent.q_table):
    plt.text(i, v + (0.05 * np.max(agent.q_table) if v >=0 else -0.1 * np.max(agent.q_table) if np.max(agent.q_table) > 0 else -1), f"{v:.2f}", ha='center', va='bottom' if v>=0 else 'top', fontsize=10)


# 4. Q 值隨時間變化圖
plt.subplot(2, 3, 4) # 改為 2x3 網格中的第 4 個
for i in range(env.n_decks):
    plt.plot(q_values_history_array[:, i], label=deck_names[i], color=colors(i/env.n_decks), linewidth=1.5)
plt.title('Q 值隨試驗次數的變化', fontsize=14)
plt.xlabel('試驗次數', fontsize=12)
plt.ylabel('估計的長期價值 (Q 值)', fontsize=12)
plt.legend(title='牌組', fontsize=10, title_fontsize=11)
plt.grid(True, linestyle='--', alpha=0.7)

# 5. Epsilon 衰減圖
plt.subplot(2, 3, 5) # 改為 2x3 網格中的第 5 個
plt.plot(epsilon_history, color='red', linewidth=1.5)
plt.title('Epsilon (探索率) 衰減過程', fontsize=14)
plt.xlabel('試驗次數', fontsize=12)
plt.ylabel('Epsilon 值', fontsize=12)
plt.grid(True, linestyle='--', alpha=0.7)

# 第 6 個子圖位置 (2,3,6) 保持空白

plt.suptitle('愛荷華賭博任務 (IGT) Q-learning 模擬結果', fontsize=18, fontweight='bold')
plt.tight_layout(rect=[0, 0, 1, 0.95]) # 全局調整佈局， rect 的 bottom 調整為 0
plt.show()

print("\n--- 圖表說明 ---")
print("1. 累積獎勵圖: 展示智能體在模擬過程中獲得的總獎勵如何隨時間增長。")
print("2. 牌組選擇頻率圖: 顯示智能體在不同階段選擇各個牌組的次數。成功的學習應表現為從初期隨機探索逐漸轉向更頻繁地選擇有利牌組。")
print("3. 最終 Q 值比較: 顯示模擬結束時，智能體對每個牌組長期價值的估計。有利牌組的 Q 值應高於不利牌組。")
print("4. Q 值隨時間變化圖: 展示智能體對每個牌組價值的估計是如何隨着經驗的積累而變化的。")
print("5. Epsilon 衰減圖: 展示探索率 (epsilon) 如何隨試驗次數減少，表明智能體從探索逐漸轉向利用。")


