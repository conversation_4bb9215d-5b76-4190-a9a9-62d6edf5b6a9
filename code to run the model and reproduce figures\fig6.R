########################################################################
#first run the model with 3 targets at each value (standard parameters)#
########################################################################
source('initialise.R')
source('run_simulation.R')

best_policy=best_policy/runs
hits=hits/runs
surprise_hits=surprise_hits/runs

#record the offloading data
lowval_offloading_3targets = best_policy[OFFLOAD_LOWVAL]
highval_offloading_3targets = best_policy[OFFLOAD_HIGHVAL]

###############################################
#now run the model with 1 target at each value#
###############################################
nItems=1

source('run_simulation.R')

best_policy=best_policy/runs
hits=hits/runs
surprise_hits=surprise_hits/runs

#record the offloading data
lowval_offloading_1target = best_policy[OFFLOAD_LOWVAL]
highval_offloading_1target = best_policy[OFFLOAD_HIGHVAL]

#######################
#now graph the results#
#######################
library(patchwork)
library(ggplot2)

value=c("Low-value", "High-value")
item1_data=c(lowval_offloading_1target,highval_offloading_1target)
item3_data=c(lowval_offloading_3targets,highval_offloading_3targets)

item1 = data.frame(value=value, data=item1_data)
item3 = data.frame(value=value, data=item3_data)

item1$value = factor(item1$value, levels = item1$value)
item3$value = factor(item3$value, levels = item3$value)

item1_graph = ggplot(item1, aes(x=value,y=data)) +
  geom_bar(stat="identity", fill="cornflowerblue") +
  ggtitle("1 item at each value") +
  scale_y_continuous(name="Offloading rate", limits=c(0,1), breaks=seq(0,1,0.2)) +
  theme(panel.border = element_blank(),  
        # Remove panel grid lines
        panel.grid.major = element_blank(),
        panel.grid.minor = element_blank(),
        # Remove panel background
        panel.background = element_rect(fill = "white", colour = "black", size = 1, linetype = "solid"),
        # Remove legend
        legend.position = "none",
        # Remove x axis label
        axis.title.x = element_blank(),
        axis.title.y = element_text(size = 14),
        # Style text
        text = element_text(size = 14),
        plot.title = element_text(hjust = 0.5))

item3_graph = ggplot(item3, aes(x=value,y=data)) +
  geom_bar(stat="identity", fill="cornflowerblue") +
  ggtitle("3 items at each value") +
  scale_y_continuous(name="", limits=c(0,1), breaks=seq(0,1,0.2)) +
  theme(panel.border = element_blank(),  
        # Remove panel grid lines
        panel.grid.major = element_blank(),
        panel.grid.minor = element_blank(),
        # Remove panel background
        panel.background = element_rect(fill = "white", colour = "black", size = 1, linetype = "solid"),
        # Remove legend
        legend.position = "none",
        # Remove x axis label
        axis.title.x = element_blank(),
        # Style text
        text = element_text(size = 14),
        plot.title = element_text(hjust = 0.5))

graph = item1_graph + item3_graph

pdf(file="fig.pdf", width = 8, height = 4)

print(graph)

dev.off()