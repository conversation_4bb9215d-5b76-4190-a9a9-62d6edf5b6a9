import numpy as np
import matplotlib.pyplot as plt
from matplotlib.animation import FuncAnimation

## 繪製迷宮
fig = plt.figure(figsize=(8,8))
ax = plt.gca() # 獲取當前座標軸
def ini_maze():
    plt.plot([1,2],[1,1], color='red', linewidth=2)
    plt.plot([3,4],[1,1], color='red', linewidth=2)
    plt.plot([0,1],[2,2], color='red', linewidth=2)
    plt.plot([3,4],[2,2], color='red', linewidth=2)
    plt.plot([1,2],[3,3], color='red', linewidth=2)
    plt.plot([3,4],[3,3], color='red', linewidth=2)
    plt.plot([0,1],[4,4], color='red', linewidth=2)
    plt.plot([2,3],[4,4], color='red', linewidth=2)
    
    plt.plot([1,1],[0,1], color='red', linewidth=2)
    plt.plot([2,2],[1,2], color='red', linewidth=2)
    plt.plot([4,4],[0,1], color='red', linewidth=2)
    plt.plot([2,2],[2,3], color='red', linewidth=2)
    plt.plot([3,3],[3,4], color='red', linewidth=2)
    plt.plot([4,4],[3,4], color='red', linewidth=2)
    
    for i in range(5):
        for j in range(5):
            state_num = i * 5 + j
            plt.text(j+0.5, 4.5-i, f'S{state_num}', size=10, ha='center')
    
    plt.text(0.5, 4.3, 'START', ha='center', size=8)
    plt.text(4.5, 0.3, 'GOAL', ha='center', size=8)
    
    ax.set_xlim(0,5)
    ax.set_ylim(0,5)
    plt.tick_params(axis='both', which='both', bottom=False, top=False, labelbottom=False, right=False, left=False, labelleft=False)

theta_0 = np.array([
    # S0 
    [np.nan, 1, np.nan, np.nan],
    # S1
    [np.nan, 1, 1, 1],
    # S2
    [np.nan, 1, np.nan, 1],
    # S3
    [np.nan, 1, 1, 1],
    # S4 (右上角)
    [np.nan, np.nan, 1, 1],
    
    # S5
    [np.nan, 1, 1, np.nan],
    # S6
    [1, 1, np.nan, 1],
    # S7
    [np.nan, np.nan, 1, 1],
    # S8
    [1, np.nan, np.nan, np.nan],
    # S9 (右上中)
    [1, np.nan, 1, np.nan],
    
    # S10
    [1, 1, np.nan, np.nan],
    # S11
    [np.nan, np.nan, 1, 1],
    # S12
    [1, 1, 1, np.nan],
    # S13
    [np.nan, 1, np.nan, 1],
    # S14 (右中)
    [1, np.nan, 1, 1],
    
    # S15
    [np.nan, 1, 1, np.nan],
    # S16
    [1, np.nan, np.nan, 1],
    # S17
    [1, 1, 1, np.nan],
    # S18
    [np.nan, 1, np.nan, 1],
    # S19 (右下中)
    [1, np.nan, 1, 1],
    
    # S20 (左下角)
    [1, np.nan, np.nan, np.nan],
    # S21
    [np.nan, 1, np.nan, np.nan],
    # S22
    [1, 1, np.nan, 1],
    # S23
    [np.nan, np.nan, np.nan, 1],
    # S24 (右下角，終點)
    [1, np.nan, np.nan, np.nan]
])

ini_maze()
line, = ax.plot([0.5],[4.5], marker='o', color='lightgreen', markersize=60)
# plt.show()

def softmax_pi_from_theta(theta):
    beta = 2
    [m, n] = theta.shape
    pi = np.zeros((m, n))
    exp_theta = np.exp(beta * theta)
    for i in range(0, m):
        sum_exp = np.nansum(exp_theta[i, :])
        pi[i, :] = exp_theta[i, :] / sum_exp
    pi = np.nan_to_num(pi)
    return pi

pi_0 = softmax_pi_from_theta(theta_0)
[a, b] = theta_0.shape
Q_0 = np.random.rand(a, b) * theta_0 * 0.1
print("初始 Q 值 = ", Q_0)

direction = ["up", "right", "down", "left"]

def get_next_s(action, s):
    if action == 0:  
        s_next = s - 5
    elif action == 1:  
        s_next = s + 1
    elif action == 2:  
        s_next = s + 5
    elif action == 3:  
        s_next = s - 1
    return s_next

def get_action(s, Q, epsilon, pi):
    if np.random.rand() < epsilon:
        next_direction = np.random.choice(direction, p=pi[s, :])
    else:
        next_direction = direction[np.nanargmax(Q[s, :])]
    
    if next_direction == "up":
        next_action = 0
    elif next_direction == "right":
        next_action = 1
    elif next_direction == "down":
        next_action = 2
    else:
        next_action = 3
    return next_action

def update_theta(theta, pi, history_s, history_a):
    eta = 0.5
    TotalN = len(history_s)
    [m, n] = theta.shape
    delta_theta = theta.copy()
    N_i = np.zeros(m)
    N_ij = np.zeros((m, n))
    for t in range(0, TotalN-1):
        i = history_s[t]
        j = history_a[t]
        N_i[i] = N_i[i] + 1
        N_ij[i, j] = N_ij[i, j] + 1
    for i in range(0, m):
        for j in range(0, n):
            delta_theta[i, j] = (N_ij[i, j] - pi[i, j] * N_i[i]) / TotalN
    new_theta = theta + eta * delta_theta
    return new_theta

def Q_Learning(s, a, s_next, r, Q, eta, gamma):
    if s_next == 24:  
        Q[s, a] = Q[s, a] + eta * (r - Q[s, a])
    else:
        Q[s, a] = Q[s, a] + eta * (r - Q[s, a] + gamma * np.nanmax(Q[s_next, :]))
    return Q


def run_maze(pi, Q):
    eps = 0.5
    eta = 0.1
    gamma = 0.8
    s = 0  
    history_s = [0]  
    action = get_action(s, Q, eps, pi)
    history_a = [action]  
    
    while s != 24:  
        next_s = get_next_s(action, s)
        if next_s != 24:
            r = 0
            next_a = get_action(next_s, Q, eps, pi)
        else:
            r = 1
            next_a = np.nan
        
        history_s.append(next_s)
        history_a.append(next_a)
        Q = Q_Learning(s, action, next_s, r, Q, eta, gamma)
        action = next_a
        s = next_s
    
    return (history_s, history_a, Q)

theta_run = theta_0
pi_run = pi_0
step_history = []
Q_run = Q_0

for i in range(0, 50):
    pi_run = softmax_pi_from_theta(theta_run)
    state_history, action_history, Q_run = run_maze(pi_run, Q_run)
    step_history.append(len(state_history))
    theta_run = update_theta(theta_run, pi_run, state_history, action_history)

print("步數歷史:", step_history)
print("最終 Q 值 = ", Q_run)

def update(i):
    state = state_history[i]
    x = (state % 5) + 0.5
    y = 4.5 - int(state / 5)
    plt.cla()
    ini_maze()
    ax.plot(x, y, marker='o', color='green', markersize=60)
    plt.text(0.5, 5.2, f'WALKED: {i}/{len(state_history)-1}', ha='center', fontsize=12)


pi_run = softmax_pi_from_theta(theta_run)
state_history, action_history, _ = run_maze(pi_run, Q_run)
anim = FuncAnimation(fig=fig, func=update, frames=len(state_history), interval=100, repeat=False)
plt.show()

plt.figure(figsize=(10, 6))
plt.plot(step_history)
plt.title('Mixed-Policy: Steps per Iteration')
plt.xlabel('Iteration Number')
plt.ylabel('Steps')
plt.grid(True)
plt.show()
