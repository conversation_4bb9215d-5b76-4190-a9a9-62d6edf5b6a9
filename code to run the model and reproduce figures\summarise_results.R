best_policy=best_policy/runs
hits=hits/runs
surprise_hits=surprise_hits/runs

#summarise the results
print(sprintf('encode low value: %0.2f', best_policy[ENCODE_LOWVAL]))
print(sprintf('encode high value: %0.2f', best_policy[ENCODE_HIGHVAL]))
print(sprintf('offload low value: %0.2f', best_policy[OFFLOAD_LOWVAL]))
print(sprintf('offload high value: %0.2f', best_policy[OFFLOAD_HIGHVAL]))
print(sprintf('hits low value: %0.2f', hits[LOWVAL]))
print(sprintf('hits high value: %0.2f', hits[HIGHVAL]))
print(sprintf('surprise hits low value: %0.2f', surprise_hits[LOWVAL]))
print(sprintf('surprise hits high value: %0.2f', surprise_hits[HIGHVAL]))

#graph the results
library(patchwork)
library(ggplot2)

strategy_list=c("Encode\nlow-value", "Encode\nhigh-value")
data_list=best_policy[1:2]

if ((offloading_allowed[LOWVAL]==TRUE)|(offloading_allowed[HIGHVAL]==TRUE)) {
  strategy_list=c(strategy_list, "Offload\nlow-value", "Offload\nhigh-value")
  data_list=c(data_list,best_policy[3:4])
}

strategy = data.frame(strategies=strategy_list, data=data_list)

strategy$strategies = factor(strategy$strategies, levels = strategy$strategies)

strategy_graph = ggplot(strategy, aes(x=strategies,y=data)) +
  geom_bar(stat="identity", fill="cornflowerblue") +
  ggtitle("Strategy") +
  scale_y_continuous(name="", limits=c(0,1), breaks=seq(0,1,0.2)) +
  theme(panel.border = element_blank(),  
        # Remove panel grid lines
        panel.grid.major = element_blank(),
        panel.grid.minor = element_blank(),
        # Remove panel background
        panel.background = element_rect(fill = "white", colour = "black", linewidth = 1, linetype = "solid"),
        # Remove legend
        legend.position = "none",
        # Remove x axis label
        axis.title.x = element_blank(),
        # Style text
        text = element_text(size = 14),
        plot.title = element_text(hjust = 0.5))

include_surprise=FALSE

if(include_surprise) {
  performance = data.frame(conditions=c("Low-value\nmain task", "High-value\nmain task", "Low-value\nsurprise test", "High-value\nsurprise test"), 
                           data=c(hits,surprise_hits))
} else {
  performance = data.frame(conditions=c("Low-value", "High-value"), data=hits)
}

performance$conditions = factor(performance$conditions, levels = performance$conditions)

performance_graph = ggplot(performance, aes(x=conditions,y=data)) +
  geom_bar(stat="identity", fill="cornflowerblue") +
  ggtitle("Accuracy") +
  scale_y_continuous(name="", limits=c(0,1), breaks=seq(0,1,0.2)) +
  theme(panel.border = element_blank(),  
        # Remove panel grid lines
        panel.grid.major = element_blank(),
        panel.grid.minor = element_blank(),
        # Remove panel background
        panel.background = element_rect(fill = "white", colour = "black", linewidth = 1, linetype = "solid"),
        # Remove legend
        legend.position = "none",
        # Remove x axis label
        axis.title.x = element_blank(),
        # Style text
        text = element_text(size = 14),
        plot.title = element_text(hjust = 0.5))

graph = strategy_graph + performance_graph

if(!include_surprise) {
  if((offloading_allowed[LOWVAL]==TRUE)|(offloading_allowed[HIGHVAL]==TRUE)) {
    graph = strategy_graph + performance_graph + plot_layout(widths = c(2,1))
  }
}

print(graph)
# pdf(file="fig.pdf", width = 8, height = 4)

print(graph)
dev.off()