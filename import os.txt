import os
import glob
import cv2
import numpy as np
import matplotlib.pyplot as plt
from sklearn.model_selection import train_test_split  
from keras.utils import to_categorical
from keras.models import Sequential
from keras.layers import Conv2D, MaxPooling2D, Flatten, Dense, Dropout
os.environ["TF_ENABLE_ONEDNN_OPTS"] = "0"

# 創建一個空列表用於存儲圖片數據
pics = []
# 創建一個空列表用於存儲標籤數據
labels = []

# 創建一個字典，將貓狗類別映射為數字標籤(0代表貓，1代表狗)
dict_label = {"Cat":0, "Dog":1} #和檔案名相同
# 定義處理後的圖片尺寸為40x40像素
pic_size = (50, 50)  #50x50   

# 使用glob遍歷指定目錄下的所有文件夾
for folder in glob.glob("C:\\Users\\<USER>\Downloads\\archive (2)\\PetImages\\*"):
    # 從文件夾路徑中提取最後一個部分作為標籤名稱（Cat或Dog）
    label = folder.split("\\")[-1]
    # print(folder)
    # print(label)
    # 如果是貓的文件夾
    if label == "Cat":
        # 顯示正在讀取貓的圖片文件
        print(label,"picture files reading...")
        for filename in os.listdir(folder): #讀取資料夾裡的檔案，listdir將所有資料列出來
            try:
                img = cv2.imread(os.path.join(folder, filename)) #讀取檔案，path.join將路徑合併
                # 如果圖片讀取成功
                if img is not None:
                    # 將圖片調整為指定大小
                    img = cv2.resize(img, pic_size) #調整圖片大小
                    # 將處理後的圖片添加到pics列表中
                    pics.append(img)
                    # 將對應的標籤添加到labels列表中
                    labels.append(dict_label[label])
            except:
                print("Error reading file:", filename)
                pass 
    # 如果是狗的文件夾
    elif label == "Dog":
        print(label,"picture files reading...")
        for filename in os.listdir(folder): #讀取資料夾裡的檔案，listdir將所有資料列出來
            try:
                img = cv2.imread(os.path.join(folder, filename)) #讀取檔案，path.join將路徑合併
                if img is not None:
                    # 將圖片調整為指定大小
                    img = cv2.resize(img, pic_size) #調整圖片大小
                    # 將處理後的圖片添加到pics列表中
                    pics.append(img)
                    # 將對應的標籤添加到labels列表中
                    labels.append(dict_label[label])
            except:
                print("Error reading file:", filename)
                pass

print("total images =" , str(len(pics)) )





def imshow_(image):
    b,g,r = cv2.split(image)
    plt.imshow(cv2.merge([r,g,b])) #因為opencv是bgr，plt是rgb
    plt.show()

# imshow_(pics[0])

pics_array=np.array(pics, dtype="float32")
labels_array=np.array(labels,dtype="float32")
train_pics, test_pics, train_labels, test_labels = train_test_split(pics_array, labels_array, test_size=0.2)
# print(train_pics.shape)

##圖片預處理
train_pics = train_pics/255
test_pics = test_pics/255
#將標籤轉換為one-hot編碼
train_labels = to_categorical(train_labels)
test_labels = to_categorical(test_labels)

##建立模型
model = Sequential()
model.add(Conv2D(filters =10, padding = "same",kernel_size = (5,5), activation = "relu", input_shape = (40,40,3)) )
model.add(MaxPooling2D(pool_size = (2,2)))
model.add(Dropout(0.1))
#有兩個捲基層，不用input，因為是接著傳下來的
model.add(Conv2D(filters =20, padding = "same",kernel_size = (5,5), activation = "relu"))
model.add(MaxPooling2D(pool_size = (2,2))) 
model.add(Dropout(0.2))

model.add(Flatten())
#隱藏層
model.add(Dense(units = 512, activation = "relu"))
model.add(Dense(units = 2, activation = "softmax"))
model.summary()

# 原來的程式碼
# model.add(Conv2D(filters =10, padding = "same",kernel_size = (5,5), activation = "relu", input_shape = (40,40,3)) )

# 修正後的程式碼 (假設 pic_size = (50, 50))
model.add(Conv2D(filters =10, padding = "same",kernel_size = (5,5), activation = "relu", input_shape = (50,50,3)) )

score = model.evaluate(test_pics, test_labels)
print("Testing ACC", score[1])# 原來的程式碼
# model.add(Conv2D(filters =10, padding = "same",kernel_size = (5,5), activation = "relu", input_shape = (40,40,3)) )

# 修正後的程式碼 (假設 pic_size = (50, 50))
model.add(Conv2D(filters =10, padding = "same",kernel_size = (5,5), activation = "relu", input_shape = (50,50,3)) )
# 原來的程式碼
# model.add(Conv2D(filters =10, padding = "same",kernel_size = (5,5), activation = "relu", input_shape = (40,40,3)) )

# 修正後的程式碼 (假設 pic_size = (50, 50))
model.add(Conv2D(filters =10, padding = "same",kernel_size = (5,5), activation = "relu", input_shape = (50,50,3)) )
