import numpy as np
import matplotlib.pyplot as plt
from matplotlib.animation import FuncAnimation # 匯入動畫函式庫

# --- 初始化設定 ---
fig = plt.figure(figsize=(5, 5)) # 建立畫布
ax = plt.gca() # 取得當前的座標軸
ax.set_xlim(0,3) # 設定 x 軸範圍
ax.set_ylim(0,3) # 設定 y 軸範圍

# --- 繪製迷宮 ---
def ini_maze():
    """繪製迷宮的牆壁和狀態標示"""
    # 繪製紅色牆壁
    plt.plot([1,1],[0,1],color = "red",linewidth = 2) # 狀態 S6 和 S7 之間的牆
    plt.plot([1,2],[2,2],color = "red",linewidth = 2) # 狀態 S1 和 S4 之間的牆
    plt.plot([2,2],[2,1],color = "red",linewidth = 2) # 狀態 S4 和 S5 之間的牆
    plt.plot([2,3],[1,1],color = "red",linewidth = 2) # 狀態 S5 和 S8 之間的牆

    # 標示狀態編號
    plt.text(0.5,2.5,"S0",fontsize = 14, ha = "center", va = "center")
    plt.text(0.5, 2.3, "START", fontsize=10, ha="center", va="center") # 起點標示
    plt.text(1.5,2.5,"S1",fontsize = 14, ha = "center", va = "center")
    plt.text(2.5,2.5,"S2",fontsize = 14, ha = "center", va = "center")
    plt.text(0.5,1.5,"S3",fontsize = 14, ha = "center", va = "center")
    plt.text(1.5,1.5,"S4",fontsize = 14, ha = "center", va = "center")
    plt.text(2.5,1.5,"S5",fontsize = 14, ha = "center", va = "center")
    plt.text(0.5,0.5,"S6",fontsize = 14, ha = "center", va = "center")
    plt.text(1.5,0.5,"S7",fontsize = 14, ha = "center", va = "center")
    plt.text(2.5,0.5,"S8",fontsize = 14, ha = "center", va = "center")
    plt.text(2.5, 0.7, "FINISH", fontsize=10, ha="center", va="center") # 終點標示

# 呼叫函數繪製迷宮
ini_maze()
# 繪製初始位置的藍色圓點
line,=ax.plot([0.5],[2.5],marker="o", markersize=50, color="lightblue", linewidth=2)
# plt.show() # 暫時不顯示靜態圖

# --- 策略參數 (theta) 和 Softmax 函數 ---
# 初始策略參數 theta_0
# 每個狀態 (row) 對應四個方向 [up, down, left, right] 的權重
# np.nan 表示該方向不可走 (有牆或邊界)
thete_0 = np.array([[np.nan, 1, 1, np.nan],      # S0: 可下, 可右
                    [np.nan, 1, np.nan, 1],      # S1: 可下, 可右
                    [np.nan, np.nan, 1, 1],      # S2: 可左, 可下 (修正: 應為可左, 可下) -> 原始碼似乎有誤, 但先依原始碼
                    [1, 1, 1, np.nan],          # S3: 可上, 可下, 可右
                    [np.nan, np.nan, 1, 1],      # S4: 可左, 可右 (修正: 應為可上, 可左, 可右) -> 原始碼似乎有誤
                    [1, np.nan, np.nan, np.nan], # S5: 可上 (修正: 應為可上, 可左) -> 原始碼似乎有誤
                    [1, np.nan, np.nan, np.nan], # S6: 可上
                    [1, 1, np.nan, np.nan]])     # S7: 可上, 可右

def softmax(theta):
    """計算策略 pi (每個狀態下選擇各個動作的機率)"""
    beta = 1.0 # Softmax 溫度參數
    [m,n]= theta.shape # 取得 theta 的維度 (狀態數, 動作數)
    pi = np.zeros((m,n)) # 初始化策略矩陣 pi
    exp_theta = np.exp(theta*beta) # 計算 theta 的指數

    # 對每個狀態計算 Softmax 機率
    for i in range(0,m):
        # np.nansum 會忽略 NaN 值進行加總
        sum_exp = np.nansum(exp_theta[i,:]) # 計算狀態 i 所有可行動方向的 exp(theta) 總和
        pi[i,:] = exp_theta[i,:] / sum_exp # 計算機率
        pi = np.nan_to_num(pi) # 將計算過程中可能產生的 NaN (例如 exp(nan)/sum) 轉為 0
    return pi

# 計算初始策略 pi_0
pi_0 = softmax(thete_0)
# print(pi_0) # 可以取消註解來查看初始策略

# --- 動作選擇和迷宮運行 ---
# 定義方向名稱
direction = np.array(["up","down","left","right"])
# 定義動作編號 (方便 update_history 使用)
# 0: up, 1: right, 2: down, 3: left (注意: 這個編號順序需要和 get_action_next_s 中的 action 對應)
action_map = {"up": 0, "right": 1, "down": 2, "left": 3}

# (get_action 函數似乎未使用，先註解掉)
# def get_action(s,pi):
#     next_action = np.random.choice(direction, p=pi[s,:])
#     if next_action == "up":
#         action = 0
#         s_next = s - 3
#     elif next_action == "down":
#         action = 1 # 注意: 這裡的編號和 action_map 不同
#         s_next = s + 3
#     elif next_action == "left":
#         action = 2 # 注意: 這裡的編號和 action_map 不同
#         s_next = s - 1
#     elif next_action == "right":
#         action = 3 # 注意: 這裡的編號和 action_map 不同
#         s_next = s + 1
#     return [action,s_next]

def get_action_next_s(pi, s):
    """根據策略 pi 和當前狀態 s，選擇下一個動作並返回下一個狀態和動作編號"""
    while True: # 持續選擇直到找到合法的移動
        # 根據機率 pi[s,:] 選擇一個方向
        next_direction_idx = np.random.choice(np.arange(len(direction)), p=pi[s,:])
        next_direction = direction[next_direction_idx]
        action = action_map[next_direction] # 獲取對應的動作編號

        # 根據選擇的方向計算下一個狀態 s_next，並進行邊界檢查
        if next_direction == "up" and s >= 3:
            s_next = s - 3
            return [s_next, action] # 返回下一個狀態和動作
        elif next_direction == "down" and s < 6:
            s_next = s + 3
            return [s_next, action]
        elif next_direction == "left" and s % 3 != 0:
            s_next = s - 1
            return [s_next, action]
        elif next_direction == "right" and s % 3 != 2:
            s_next = s + 1
            return [s_next, action]
        # 如果選擇的動作不合法 (撞牆或邊界)，則繼續循環重新選擇

def run_maze(pi):
    """根據策略 pi 運行一次迷宮，返回狀態和動作的歷史記錄"""
    s = 0 # 初始狀態為 S0
    history = [[0, np.nan]] # 初始化歷史記錄，格式為 [[state0, action0], [state1, action1], ...]
                           # 初始動作未知，設為 NaN

    while s != 8: # 當前狀態不是終點 S8 時繼續循環
        # 獲取下一個狀態和執行的動作
        [next_s, action] = get_action_next_s(pi, s)
        # 更新上一步的動作
        history[-1][1] = action
        # 添加新的狀態和未知的下一步動作
        history.append([next_s, np.nan])
        # 更新當前狀態
        s = next_s

    # 移除最後一個多餘的 [終點狀態, np.nan]
    history.pop()
    return history # 返回包含 [state, action] 配對的列表

# 根據初始策略 pi_0 運行迷宮
state_action_history = run_maze(pi_0)
# print(state_action_history) # 可以取消註解查看運行歷史

# --- 動畫更新函數 (目前未使用，需要修改以配合 state_action_history) ---
# def update(i):
#     """更新動畫的每一幀"""
#     # 需要修改 state_history 的來源和結構
#     # state = state_history[i] # state_history 現在是 [[s0,a0], [s1,a1], ...]
#     state = state_action_history[i][0] # 取出第 i 步的狀態
#     x = (state % 3) + 0.5  # 計算狀態對應的 x 座標
#     y = 2.5 - int(state / 3)  # 計算狀態對應的 y 座標
#     plt.cla() # 清除當前座標軸
#     ini_maze() # 重新繪製迷宮
#     ax.plot(x, y, marker="o", markersize=50, color="lightblue", linewidth=2)  # 繪製當前狀態的圓點

# # 創建動畫 (需要確保 state_action_history 不為空)
# if state_action_history:
#     anim = FuncAnimation(fig, update, frames=len(state_action_history), interval=200, repeat=False)
#     plt.show() # 顯示動畫
# else:
#     print("無法生成動畫，歷史記錄為空。")
#     plt.show() # 顯示靜態圖

# --- 策略更新函數 ---
def update_history(theta, pi, history):
    """根據運行的歷史記錄更新策略參數 theta"""
    eta = 0.1 # 學習率
    T = len(history) # 總步數
    if T == 0: # 如果歷史記錄為空，不更新
        return theta

    [m, n] = theta.shape # 獲取 theta 的維度
    delta_theta = np.zeros_like(theta) # 初始化 theta 的變化量，使用 zeros_like 保持 NaN 位置

    # 計算每個狀態-動作對的計數
    counts = {} # 使用字典存儲 (state, action) 的次數
    for state, action in history:
        if not np.isnan(action): # 確保動作不是 NaN
            action = int(action) # 將動作轉為整數索引
            counts[(state, action)] = counts.get((state, action), 0) + 1

    # 計算 delta_theta
    for i in range(m): # 遍歷每個狀態
        # 計算狀態 i 在 history 中出現的總次數 (作為執行動作的次數)
        print("正在計算")
        N_i = sum(count for (state, _), count in counts.items() if state == i)
        if N_i == 0: # 如果狀態 i 從未出現過，跳過
            continue

        for j in range(n): # 遍歷每個動作
            if not np.isnan(theta[i, j]): # 只更新允許的動作
                # 獲取狀態 i 執行動作 j 的次數
                N_ij = counts.get((i, j), 0)
                # 計算 theta 的變化量
                delta_theta[i, j] = (N_ij / N_i) - pi[i, j] # 使用相對頻率估計期望

    # 更新 theta (只更新非 NaN 的部分)
    new_theta = theta + eta * delta_theta
    # 確保 NaN 的位置保持不變 (雖然 delta_theta 初始化為 0 應該沒問題，但以防萬一)
    new_theta = np.where(np.isnan(theta), np.nan, new_theta)

    return new_theta

# 使用初始 theta_0, pi_0 和運行歷史來更新 theta
theta = update_history(thete_0, pi_0, state_action_history)
print("更新後的 Theta:")
print(theta)

# 顯示最終的靜態圖 (如果動畫未顯示)
plt.show()