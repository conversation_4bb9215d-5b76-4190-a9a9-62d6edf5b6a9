import numpy as np
np.random.seed()
w1=np.random.normal(loc=0,scale = 0.1, size = (2,3))
w2=np.random.normal(loc=0,scale = 0.1, size = (3))
a = 20
bias = 0.7
def sigmoid(m):
    return 1/(1+np.exp(-a*(m-bias)))

#caculate output
def get_output(inp, wei):
    memP = np.dot(inp, wei)
    out =sigmoid(memP)
    return out
 
x = np.array([[0,0],[1,0],[0,1],[1,1]])
t = np.array([0,1,1,1]) #產生目標 #and gate
o1 = get_output(x[1,:], w1) 


al=0.5 #學習速率
#learning
def delta_rule_output(target,outp):
    #修改delta變成oup-target-->這樣會差一個負號
    delta = outp-target
    # dw = -al*np.outer(delta*outp*(1-outp),inp)
    delt= delta*a*outp*(1-outp)
    return delt

def delta_rule_hidden(delta_p, wei, outp):
    delta = np.dot(delta_p, wei)    
    delt = delta*outp*(1-outp)
    return delt

o1 = get_output(x[1,:],w1)
o2 = get_output(o1,w2)
del2=delta_rule_output(t[1], o2)
print(del2)

del1=delta_rule_hidden(del2,w2,o1)
print(del1)

#caculate output before training
def get_all_output():
    ov = np.zeros(4)
    for i in range(4):
        o1 = get_output(x[i,:],w1)
        o2 = get_output(o1,w2)
        ov[i] =o2
    return ov

out = get_all_output()
print(out)

for rpt in range(10):
    for i in range(4):
        o1 = get_output(x[i,:],w1)
        o2=get_output(o1,w2)
        del2 = delta_rule_output(t[i], o2)
        del1 = delta_rule_hidden(del2,w2,o1)
        w2=w2-al*del2*o1
        

       
