<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" lang="en" xml:lang="en"><head>

<meta charset="utf-8">
<meta name="generator" content="quarto-1.6.42">

<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=yes">


<title>quarto-input2a6c4fb2d0f9d4e</title>
<style>
code{white-space: pre-wrap;}
span.smallcaps{font-variant: small-caps;}
div.columns{display: flex; gap: min(4vw, 1.5em);}
div.column{flex: auto; overflow-x: auto;}
div.hanging-indent{margin-left: 1.5em; text-indent: -1.5em;}
ul.task-list{list-style: none;}
ul.task-list li input[type="checkbox"] {
  width: 0.8em;
  margin: 0 0.8em 0.2em -1em; /* quarto-specific, see https://github.com/quarto-dev/quarto-cli/issues/4556 */ 
  vertical-align: middle;
}
</style>


<script src="神經網路介紹_files/libs/clipboard/clipboard.min.js"></script>
<script src="神經網路介紹_files/libs/quarto-html/quarto.js"></script>
<script src="神經網路介紹_files/libs/quarto-html/popper.min.js"></script>
<script src="神經網路介紹_files/libs/quarto-html/tippy.umd.min.js"></script>
<script src="神經網路介紹_files/libs/quarto-html/anchor.min.js"></script>
<link href="神經網路介紹_files/libs/quarto-html/tippy.css" rel="stylesheet">
<link href="神經網路介紹_files/libs/quarto-html/quarto-syntax-highlighting-2f5df379a58b258e96c21c0638c20c03.css" rel="stylesheet" id="quarto-text-highlighting-styles">
<script src="神經網路介紹_files/libs/bootstrap/bootstrap.min.js"></script>
<link href="神經網路介紹_files/libs/bootstrap/bootstrap-icons.css" rel="stylesheet">
<link href="神經網路介紹_files/libs/bootstrap/bootstrap-1bc8a17f135ab3d594c857e9f48e611b.min.css" rel="stylesheet" append-hash="true" id="quarto-bootstrap" data-mode="light">


</head>

<body class="fullcontent">

<div id="quarto-content" class="page-columns page-rows-contents page-layout-article">

<main class="content" id="quarto-document-content">




<p>好的，這是一個比較深入的問題。以下我將詳細解釋循環神經網路 (RNN)、長短期記憶網路 (LSTM)、卷積神經網路 (CNN) 和 Transformer 的運作機制與其中的重點數學公式。內容會比較技術性。</p>
<hr>
<section id="循環神經網路-recurrent-neural-network-rnn" class="level2">
<h2 class="anchored" data-anchor-id="循環神經網路-recurrent-neural-network-rnn">1. 循環神經網路 (Recurrent Neural Network, RNN)</h2>
<section id="用途" class="level3">
<h3 class="anchored" data-anchor-id="用途">用途：</h3>
<p>主要用於處理<strong>序列數據 (Sequential Data)</strong>，例如自然語言文本、時間序列數據（股票價格、天氣）、語音等。其核心思想是讓網路能夠<strong>記住</strong>先前時間步的資訊，並利用這些資訊來影響當前時間步的處理。</p>
</section>
<section id="運作機制" class="level3">
<h3 class="anchored" data-anchor-id="運作機制">運作機制：</h3>
<p>RNN 的基本結構包含一個「循環」。在處理序列中的每個元素時（例如一個詞或一個時間點的數據），RNN 不僅考慮當前的輸入，還會考慮來自上一個時間步的「<strong>隱藏狀態 (Hidden State)</strong>」。這個隱藏狀態可以看作是網路對到目前為止所見序列的「記憶」。</p>
<ul>
<li><strong>時間步 <code>t</code> 的計算：</strong>
<ul>
<li>接收當前時間步的輸入 <code>x_t</code>。</li>
<li>接收上一個時間步的隱藏狀態 <code>h_{t-1}</code> (初始 <code>h_0</code> 通常設為零向量)。</li>
<li>計算當前時間步的隱藏狀態 <code>h_t</code>：結合 <code>x_t</code> 和 <code>h_{t-1}</code> 的資訊，通過一個激活函數（通常是 <code>tanh</code> 或 <code>ReLU</code>）。</li>
<li>計算當前時間步的輸出 <code>y_t</code>（可選）：基於 <code>h_t</code> 計算輸出，激活函數取決於具體任務（例如分類用 <code>softmax</code>，回歸可能不用）。</li>
</ul></li>
<li><strong>權重共享 (Weight Sharing)：</strong> 最重要的一點是，在所有時間步中，用於計算 <code>h_t</code> 和 <code>y_t</code> 的權重矩陣（<code>W_xh</code>, <code>W_hh</code>, <code>W_hy</code>）和偏置項（<code>b_h</code>, <code>b_y</code>）是<strong>相同</strong>的。這使得模型能夠將學到的模式應用於序列的不同位置，並大大減少了參數數量。</li>
</ul>
</section>
<section id="重點數學公式" class="level3">
<h3 class="anchored" data-anchor-id="重點數學公式">重點數學公式：</h3>
<p>假設在時間步 <code>t</code>： * 輸入向量：<code>x_t</code> * 前一隱藏狀態：<code>h_{t-1}</code> * 當前隱藏狀態：<code>h_t</code> * 輸出向量：<code>y_t</code> * 權重矩陣：<code>W_xh</code> (input-to-hidden), <code>W_hh</code> (hidden-to-hidden), <code>W_hy</code> (hidden-to-output) * 偏置向量：<code>b_h</code> (hidden bias), <code>b_y</code> (output bias) * 激活函數：<code>tanh</code> (常用於隱藏層), <code>σ</code> (或其他，用於輸出層)</p>
<ol type="1">
<li><p><strong>隱藏狀態計算 (Hidden State Calculation):</strong> <code>h_t = tanh(W_xh * x_t + W_hh * h_{t-1} + b_h)</code> 這裡 <code>*</code> 通常表示矩陣乘法。<code>W_xh * x_t</code> 處理當前輸入，<code>W_hh * h_{t-1}</code> 處理來自過去的記憶，兩者相加後加上偏置，再通過 <code>tanh</code> 激活函數（將輸出壓縮到 -1 到 1 之間）。</p></li>
<li><p><strong>輸出計算 (Output Calculation):</strong> <code>y_t = σ(W_hy * h_t + b_y)</code> 使用當前的隱藏狀態 <code>h_t</code>，通過輸出權重 <code>W_hy</code> 和偏置 <code>b_y</code> 計算最終輸出，並應用適合任務的激活函數 <code>σ</code>。</p></li>
</ol>
</section>
<section id="優點" class="level3">
<h3 class="anchored" data-anchor-id="優點">優點：</h3>
<ul>
<li>能處理任意長度的序列。</li>
<li>模型大小不隨序列長度增加（權重共享）。</li>
<li>能捕捉序列中的時間依賴性。</li>
</ul>
</section>
<section id="缺點" class="level3">
<h3 class="anchored" data-anchor-id="缺點">缺點：</h3>
<ul>
<li><strong>梯度消失/爆炸 (Vanishing/Exploding Gradients)：</strong> 在訓練長序列時，梯度在反向傳播過程中可能變得非常小（消失）或非常大（爆炸），導致難以學習長期依賴關係。</li>
<li>難以捕捉非常長距離的依賴關係。</li>
<li>計算是序列化的，難以並行處理（必須先算完 <code>t-1</code> 才能算 <code>t</code>）。</li>
</ul>
<hr>
</section>
</section>
<section id="長短期記憶網路-long-short-term-memory-lstm" class="level2">
<h2 class="anchored" data-anchor-id="長短期記憶網路-long-short-term-memory-lstm">2. 長短期記憶網路 (Long Short-Term Memory, LSTM)</h2>
<section id="用途-1" class="level3">
<h3 class="anchored" data-anchor-id="用途-1">用途：</h3>
<p>是 RNN 的一種改進變體，專門設計來解決 RNN 的<strong>長期依賴問題</strong>和<strong>梯度消失問題</strong>。同樣用於處理序列數據。</p>
</section>
<section id="運作機制-1" class="level3">
<h3 class="anchored" data-anchor-id="運作機制-1">運作機制：</h3>
<p>LSTM 的核心是引入了一個「<strong>細胞狀態 (Cell State)</strong>」 (<code>C_t</code>)，可以看作是資訊的傳送帶，資訊可以在上面流動而不太會被改變。LSTM 通過稱為「<strong>門 (Gates)</strong>」的結構來精細地控制資訊的添加或移除。</p>
<ul>
<li><strong>三個關鍵門控單元：</strong>
<ol type="1">
<li><strong>遺忘門 (Forget Gate, <code>f_t</code>)：</strong> 決定從細胞狀態中<strong>丟棄</strong>哪些信息。它查看 <code>h_{t-1}</code> 和 <code>x_t</code>，為細胞狀態 <code>C_{t-1}</code> 中的每個數字輸出一個 0 到 1 之間的數值（0 表示完全丟棄，1 表示完全保留）。</li>
<li><strong>輸入門 (Input Gate, <code>i_t</code>)：</strong> 決定讓哪些<strong>新信息</strong>存入細胞狀態。它包含兩部分：一個 <code>sigmoid</code> 層決定哪些值需要更新，一個 <code>tanh</code> 層創建一個候選值向量 <code>~C_t</code>。</li>
<li><strong>輸出門 (Output Gate, <code>o_t</code>)：</strong> 決定細胞狀態的哪些部分將作為<strong>輸出</strong> <code>h_t</code>。它先用 <code>sigmoid</code> 層決定哪些部分要輸出，然後將細胞狀態通過 <code>tanh</code> 處理（壓縮到 -1 到 1），再與 <code>sigmoid</code> 的輸出相乘。</li>
</ol></li>
<li><strong>細胞狀態更新：</strong> 結合遺忘門的結果（舊狀態乘以 <code>f_t</code>）和輸入門的結果（候選值乘以 <code>i_t</code>）來更新細胞狀態。</li>
<li><strong>隱藏狀態更新：</strong> 由輸出門和經過 <code>tanh</code> 的細胞狀態共同決定。</li>
</ul>
</section>
<section id="重點數學公式-1" class="level3">
<h3 class="anchored" data-anchor-id="重點數學公式-1">重點數學公式：</h3>
<p>符號同 RNN，增加： * 細胞狀態：<code>C_t</code>, <code>C_{t-1}</code> * 遺忘門：<code>f_t</code> * 輸入門：<code>i_t</code> * 輸出門：<code>o_t</code> * 候選細胞狀態：<code>~C_t</code> * 門控單元的權重矩陣和偏置（例如 <code>W_f</code>, <code>b_f</code> 等） * <code>σ</code> 表示 Sigmoid 函數 (<code>1 / (1 + exp(-x))</code>，輸出 0 到 1) * <code>*</code> 表示<strong>逐元素乘法 (Element-wise Multiplication)</strong> * <code>[h_{t-1}, x_t]</code> 表示將兩個向量拼接起來</p>
<ol type="1">
<li><p><strong>遺忘門 (Forget Gate):</strong> <code>f_t = σ(W_f * [h_{t-1}, x_t] + b_f)</code> 決定保留多少過去的細胞狀態。</p></li>
<li><p><strong>輸入門 (Input Gate):</strong></p>
<pre><code>    i_t = σ(W_i * [h_{t-1}, x_t] + b_i)
    ```
決定更新哪些新的值。</code></pre>
<pre><code>~C_t = tanh(W_C * [h_{t-1}, x_t] + b_C)
```</code></pre>
<p>創建候選的新信息。</p></li>
<li><p><strong>細胞狀態更新 (Cell State Update):</strong> <code>C_t = f_t * C_{t-1} + i_t * ~C_t</code> 結合遺忘和輸入，更新細胞狀態。</p></li>
<li><p><strong>輸出門 (Output Gate):</strong></p>
<pre><code>    o_t = σ(W_o * [h_{t-1}, x_t] + b_o)
    ```
決定輸出細胞狀態的哪些部分。</code></pre>
<pre><code>h_t = o_t * tanh(C_t)
```</code></pre>
<p>計算隱藏狀態。最終輸出 <code>y_t</code> 的計算方式與 RNN 類似，通常基於 <code>h_t</code>。</p></li>
</ol>
</section>
<section id="優點-1" class="level3">
<h3 class="anchored" data-anchor-id="優點-1">優點：</h3>
<ul>
<li>能有效地學習長期依賴關係。</li>
<li>顯著緩解了梯度消失問題。</li>
</ul>
</section>
<section id="缺點-1" class="level3">
<h3 class="anchored" data-anchor-id="缺點-1">缺點：</h3>
<ul>
<li>結構比 RNN 複雜，計算量更大。</li>
<li>仍然是序列化計算，限制了並行能力。</li>
<li>（GRU 是 LSTM 的一個簡化變體，效果相似但參數稍少）</li>
</ul>
<hr>
</section>
</section>
<section id="卷積神經網路-convolutional-neural-network-cnn" class="level2">
<h2 class="anchored" data-anchor-id="卷積神經網路-convolutional-neural-network-cnn">3. 卷積神經網路 (Convolutional Neural Network, CNN)</h2>
<section id="用途-2" class="level3">
<h3 class="anchored" data-anchor-id="用途-2">用途：</h3>
<p>主要用於處理<strong>網格狀數據 (Grid-like Data)</strong>，最成功的應用是在<strong>計算機視覺</strong>領域（圖像識別、物體偵測等）。它也能應用於其他網格數據，如聲音頻譜圖或一維序列數據（1D CNN）。</p>
</section>
<section id="運作機制-2" class="level3">
<h3 class="anchored" data-anchor-id="運作機制-2">運作機制：</h3>
<p>CNN 的核心思想是利用<strong>局部連接 (Local Connectivity)</strong>、<strong>權重共享 (Weight Sharing)</strong> 和<strong>池化 (Pooling)</strong> 來提取空間層次結構的特徵。</p>
<ul>
<li><strong>卷積層 (Convolutional Layer):</strong>
<ul>
<li>使用稱為<strong>卷積核 (Kernel)</strong> 或<strong>濾波器 (Filter)</strong> 的小型權重矩陣。</li>
<li>這個卷積核在輸入數據（如圖像）上<strong>滑動 (Slide)</strong>，在每個位置計算核與其覆蓋的輸入區域之間的<strong>點積 (Dot Product)</strong>（或稱卷積操作，實作上常是互相關 Cross-correlation）。</li>
<li>結果形成一個<strong>特徵圖 (Feature Map)</strong>，表示該卷積核所偵測的特定特徵（如邊緣、紋理）在輸入中的位置和強度。</li>
<li><strong>局部連接：</strong> 每個輸出神經元只連接到輸入的一個局部區域（感受野 Receptive Field）。</li>
<li><strong>權重共享：</strong> 同一個卷積核（及其權重）在整個輸入上滑動使用，大大減少了參數數量，並使模型具有一定的<strong>平移不變性 (Translation Invariance)</strong>。</li>
<li>通常一個卷積層包含多個卷積核，以學習多種不同的局部特徵。</li>
</ul></li>
<li><strong>激活函數 (Activation Function):</strong> 通常在卷積層之後應用非線性激活函數，最常用的是 <strong>ReLU (Rectified Linear Unit)</strong> (<code>max(0, x)</code>)，它能加速訓練並緩解梯度消失。</li>
<li><strong>池化層 (Pooling Layer):</strong>
<ul>
<li>用於<strong>降維 (Downsampling)</strong>，減少特徵圖的空間尺寸（寬和高），從而減少計算量和參數數量，並增強模型的魯棒性（對微小平移、變形不敏感）。</li>
<li>最常用的是<strong>最大池化 (Max Pooling)</strong>：在一個小區域內（如 2x2）取最大值作為輸出。平均池化 (Average Pooling) 也是一種選擇。</li>
<li>通常沒有可學習的參數。</li>
</ul></li>
<li><strong>全連接層 (Fully Connected Layer):</strong> 在經過多個卷積和池化層提取特徵後，通常會將最終的特徵圖<strong>展平 (Flatten)</strong> 成一個向量，然後輸入到一或多個標準的全連接層中，進行最終的分類或回歸。</li>
</ul>
</section>
<section id="重點數學公式-2" class="level3">
<h3 class="anchored" data-anchor-id="重點數學公式-2">重點數學公式：</h3>
<ol type="1">
<li><p><strong>卷積操作 (Convolution Operation):</strong> 對於輸入 <code>I</code> 和卷積核 <code>K</code>，輸出特徵圖 <code>O</code> 在位置 <code>(i, j)</code> 的值（簡化版，忽略 padding 和 stride 影響）： <code>O(i, j) = (I * K)(i, j) = Σ_m Σ_n I(i+m, j+n) * K(m, n) + b</code> 這裡 <code>*</code> 代表卷積（或實作上的互相關）。<code>m</code> 和 <code>n</code> 遍歷卷積核 <code>K</code> 的維度。<code>b</code> 是偏置項。實際上計算會考慮步長 (Stride) 和填充 (Padding)。 更直觀地：將卷積核 <code>K</code> 覆蓋在輸入 <code>I</code> 的一個區域上，對應元素相乘後求和，再加上偏置 <code>b</code>。</p></li>
<li><p><strong>ReLU 激活函數 (ReLU Activation):</strong> <code>ReLU(x) = max(0, x)</code> 將所有負值變為 0，正值保持不變。</p></li>
<li><p><strong>最大池化 (Max Pooling):</strong> 對於輸入的一個 <code>k x k</code> 的區域 <code>R</code>，輸出為： <code>Output = max_{(m, n) in R} Input(m, n)</code></p></li>
</ol>
</section>
<section id="優點-2" class="level3">
<h3 class="anchored" data-anchor-id="優點-2">優點：</h3>
<ul>
<li>在圖像等網格數據上表現極佳，能有效提取空間層次特徵。</li>
<li>權重共享大大減少了模型參數，降低了過擬合風險。</li>
<li>對平移具有一定的不變性。</li>
</ul>
</section>
<section id="缺點-2" class="level3">
<h3 class="anchored" data-anchor-id="缺點-2">缺點：</h3>
<ul>
<li>對輸入的旋轉、縮放等變換比較敏感（除非使用數據增強）。</li>
<li>不太適合處理非網格結構的數據或長序列依賴。</li>
<li>需要較大的數據集進行訓練。</li>
</ul>
<hr>
</section>
</section>
<section id="transformer" class="level2">
<h2 class="anchored" data-anchor-id="transformer">4. Transformer</h2>
<section id="用途-3" class="level3">
<h3 class="anchored" data-anchor-id="用途-3">用途：</h3>
<p>最初是為<strong>序列到序列 (Sequence-to-Sequence, Seq2Seq)</strong> 任務設計的（如機器翻譯），但其核心機制 <strong>自注意力 (Self-Attention)</strong> 非常強大，使其在<strong>自然語言處理 (NLP)</strong> 領域取得了革命性成功（如 BERT, GPT 系列），並逐漸擴展到計算機視覺 (ViT)、語音處理等領域。</p>
</section>
<section id="運作機制-3" class="level3">
<h3 class="anchored" data-anchor-id="運作機制-3">運作機制：</h3>
<p>Transformer 的關鍵創新是<strong>完全拋棄了循環和卷積結構</strong>，僅依賴於<strong>注意力機制 (Attention Mechanism)</strong>，特別是<strong>自注意力 (Self-Attention)</strong>。這使得模型能夠直接捕捉序列中任意兩個位置之間的依賴關係，並且<strong>高度並行化</strong>。</p>
<ul>
<li><strong>自注意力機制 (Self-Attention):</strong>
<ul>
<li>對於序列中的每個輸入元素（例如一個詞的嵌入向量），自注意力會計算它與序列中<strong>所有其他元素（包括自身）</strong> 的<strong>關聯度 (Attention Score)</strong>。</li>
<li>這個關聯度決定了在計算該元素的<strong>新表示 (Representation)</strong> 時，應該給予序列中其他元素多少「關注」或「權重」。</li>
<li><strong>Query, Key, Value (Q, K, V):</strong> 每個輸入向量 <code>x</code> 會通過不同的權重矩陣 (<code>W_Q</code>, <code>W_K</code>, <code>W_V</code>) 轉換成三個向量：查詢向量 (Query, <code>q</code>)、鍵向量 (Key, <code>k</code>) 和值向量 (Value, <code>v</code>)。
<ul>
<li><code>q</code>：代表當前元素 “想要查詢什麼”。</li>
<li><code>k</code>：代表序列中其他元素 “能提供什麼樣的標識/鍵”。</li>
<li><code>v</code>：代表序列中其他元素 “實際包含的內容/值”。</li>
</ul></li>
<li><strong>計算過程：</strong>
<ol type="1">
<li><strong>計算得分 (Score):</strong> 用當前元素的 <code>q</code> 與所有元素的 <code>k</code> 進行點積，得到原始的注意力得分。<code>score(q, k_i) = q · k_i</code>。</li>
<li><strong>縮放 (Scale):</strong> 將得分除以 <code>sqrt(d_k)</code>（<code>d_k</code> 是 <code>k</code> 向量的維度），防止點積結果過大導致 <code>softmax</code> 梯度消失。</li>
<li><strong>歸一化 (Normalize):</strong> 使用 <code>softmax</code> 函數將得分轉換為概率分佈（權重），所有權重加起來等於 1。<code>weights = softmax(scores / sqrt(d_k))</code>。</li>
<li><strong>加權求和 (Weighted Sum):</strong> 將得到的權重乘以對應元素的 <code>v</code> 向量，然後求和，得到該元素的自注意力輸出。<code>output = Σ (weights_i * v_i)</code>。</li>
</ol></li>
</ul></li>
<li><strong>多頭注意力 (Multi-Head Attention):</strong> 並行地執行多次自注意力計算，每次使用不同的 <code>W_Q</code>, <code>W_K</code>, <code>W_V</code> 矩陣（稱為一個 “頭” Head）。這樣可以讓模型從不同的表示子空間學習相關性。最後將所有頭的輸出拼接起來，再通過一個線性變換得到最終輸出。</li>
<li><strong>位置編碼 (Positional Encoding):</strong> 由於自注意力本身不包含位置信息（它平等對待所有位置），需要額外加入位置編碼向量到輸入嵌入中，以告知模型元素在序列中的相對或絕對位置。常用 <code>sin</code> 和 <code>cos</code> 函數的不同頻率來生成。</li>
<li><strong>前饋神經網路 (Feed-Forward Network):</strong> 在自注意力層之後，每個位置的輸出會獨立地通過一個相同的前饋神經網路（通常包含兩層線性變換和一個 ReLU 激活）。</li>
<li><strong>殘差連接 (Residual Connection) 和層歸一化 (Layer Normalization):</strong> 在每個子層（自注意力層、前饋網路）的輸出都使用了殘差連接 (<code>output = Layer(x) + x</code>) 和層歸一化，這對於訓練非常深的 Transformer 模型至關重要，能穩定訓練過程，加速收斂。</li>
<li><strong>編碼器-解碼器架構 (Encoder-Decoder Architecture):</strong> 原始 Transformer 用於 Seq2Seq 任務。
<ul>
<li><strong>編碼器 (Encoder):</strong> 由多層相同的層堆疊而成，每層包含多頭自注意力和前饋網路。負責處理輸入序列，生成一系列上下文感知的表示。</li>
<li><strong>解碼器 (Decoder):</strong> 也由多層堆疊而成，除了有類似編碼器的多頭自注意力（Masked，防止看到未來信息）和前饋網路外，還有一個<strong>交叉注意力 (Cross-Attention)</strong> 層，使其能夠關注編碼器的輸出表示，從而生成目標序列。</li>
</ul></li>
</ul>
</section>
<section id="重點數學公式-scaled-dot-product-attention" class="level3">
<h3 class="anchored" data-anchor-id="重點數學公式-scaled-dot-product-attention">重點數學公式 (Scaled Dot-Product Attention):</h3>
<p>假設輸入序列的嵌入矩陣為 <code>X</code>。 1. <strong>生成 Q, K, V 矩陣:</strong> <code>Q = X * W_Q     K = X * W_K     V = X * W_V</code> <code>W_Q</code>, <code>W_K</code>, <code>W_V</code> 是可學習的權重矩陣。</p>
<ol start="2" type="1">
<li><strong>計算注意力輸出:</strong> <code>Attention(Q, K, V) = softmax( (Q * K^T) / sqrt(d_k) ) * V</code>
<ul>
<li><code>Q * K^T</code>：計算所有查詢向量與所有鍵向量的點積得分矩陣。<code>K^T</code> 是 K 的轉置。</li>
<li><code>/ sqrt(d_k)</code>：縮放因子，<code>d_k</code> 是鍵向量的維度。</li>
<li><code>softmax(...)</code>：對得分矩陣的每一行（對應每個查詢）應用 softmax，得到注意力權重。</li>
<li><code>... * V</code>：用得到的注意力權重對值向量 <code>V</code> 進行加權求和。</li>
</ul></li>
</ol>
</section>
<section id="多頭注意力-multi-head-attention" class="level3">
<h3 class="anchored" data-anchor-id="多頭注意力-multi-head-attention">多頭注意力 (Multi-Head Attention):</h3>
<ul>
<li><code>W_Q_i</code>, <code>W_K_i</code>, <code>W_V_i</code> 是第 <code>i</code> 個頭的權重矩陣。</li>
<li><code>Concat</code> 表示將所有頭的輸出拼接起來。</li>
<li><code>W_O</code> 是最後的輸出權重矩陣。</li>
</ul>
</section>
<section id="位置編碼-positional-encoding" class="level3">
<h3 class="anchored" data-anchor-id="位置編碼-positional-encoding">位置編碼 (Positional Encoding):</h3>
<ul>
<li><code>pos</code> 是位置索引，<code>i</code> 是維度索引，<code>d_model</code> 是嵌入維度。</li>
</ul>
</section>
<section id="優點-3" class="level3">
<h3 class="anchored" data-anchor-id="優點-3">優點：</h3>
<ul>
<li>極佳地捕捉長距離依賴關係。</li>
<li>計算可以高度並行化（相比 RNN/LSTM），訓練速度更快（在有足夠硬體資源時）。</li>
<li>已成為許多 NLP 任務的 SOTA (State-of-the-Art) 基礎。</li>
<li>模型可擴展性強（可以構建非常大的模型，如 GPT-3/4）。</li>
</ul>
</section>
<section id="缺點-3" class="level3">
<h3 class="anchored" data-anchor-id="缺點-3">缺點：</h3>
<ul>
<li>自注意力的計算複雜度是序列長度的平方 (<code>O(n^2)</code>)，對於非常長的序列計算成本和內存消耗巨大（有一些變體在嘗試解決這個問題）。</li>
<li>本身缺乏位置信息，需要額外的位置編碼。</li>
<li>通常需要大量的數據和計算資源進行訓練。</li>
<li>模型的可解釋性相對較差。</li>
</ul>
<hr>
</section>
</section>

</main>
<!-- /main column -->
<script id="quarto-html-after-body" type="application/javascript">
window.document.addEventListener("DOMContentLoaded", function (event) {
  const toggleBodyColorMode = (bsSheetEl) => {
    const mode = bsSheetEl.getAttribute("data-mode");
    const bodyEl = window.document.querySelector("body");
    if (mode === "dark") {
      bodyEl.classList.add("quarto-dark");
      bodyEl.classList.remove("quarto-light");
    } else {
      bodyEl.classList.add("quarto-light");
      bodyEl.classList.remove("quarto-dark");
    }
  }
  const toggleBodyColorPrimary = () => {
    const bsSheetEl = window.document.querySelector("link#quarto-bootstrap");
    if (bsSheetEl) {
      toggleBodyColorMode(bsSheetEl);
    }
  }
  toggleBodyColorPrimary();  
  const icon = "";
  const anchorJS = new window.AnchorJS();
  anchorJS.options = {
    placement: 'right',
    icon: icon
  };
  anchorJS.add('.anchored');
  const isCodeAnnotation = (el) => {
    for (const clz of el.classList) {
      if (clz.startsWith('code-annotation-')) {                     
        return true;
      }
    }
    return false;
  }
  const onCopySuccess = function(e) {
    // button target
    const button = e.trigger;
    // don't keep focus
    button.blur();
    // flash "checked"
    button.classList.add('code-copy-button-checked');
    var currentTitle = button.getAttribute("title");
    button.setAttribute("title", "Copied!");
    let tooltip;
    if (window.bootstrap) {
      button.setAttribute("data-bs-toggle", "tooltip");
      button.setAttribute("data-bs-placement", "left");
      button.setAttribute("data-bs-title", "Copied!");
      tooltip = new bootstrap.Tooltip(button, 
        { trigger: "manual", 
          customClass: "code-copy-button-tooltip",
          offset: [0, -8]});
      tooltip.show();    
    }
    setTimeout(function() {
      if (tooltip) {
        tooltip.hide();
        button.removeAttribute("data-bs-title");
        button.removeAttribute("data-bs-toggle");
        button.removeAttribute("data-bs-placement");
      }
      button.setAttribute("title", currentTitle);
      button.classList.remove('code-copy-button-checked');
    }, 1000);
    // clear code selection
    e.clearSelection();
  }
  const getTextToCopy = function(trigger) {
      const codeEl = trigger.previousElementSibling.cloneNode(true);
      for (const childEl of codeEl.children) {
        if (isCodeAnnotation(childEl)) {
          childEl.remove();
        }
      }
      return codeEl.innerText;
  }
  const clipboard = new window.ClipboardJS('.code-copy-button:not([data-in-quarto-modal])', {
    text: getTextToCopy
  });
  clipboard.on('success', onCopySuccess);
  if (window.document.getElementById('quarto-embedded-source-code-modal')) {
    const clipboardModal = new window.ClipboardJS('.code-copy-button[data-in-quarto-modal]', {
      text: getTextToCopy,
      container: window.document.getElementById('quarto-embedded-source-code-modal')
    });
    clipboardModal.on('success', onCopySuccess);
  }
    var localhostRegex = new RegExp(/^(?:http|https):\/\/localhost\:?[0-9]*\//);
    var mailtoRegex = new RegExp(/^mailto:/);
      var filterRegex = new RegExp('/' + window.location.host + '/');
    var isInternal = (href) => {
        return filterRegex.test(href) || localhostRegex.test(href) || mailtoRegex.test(href);
    }
    // Inspect non-navigation links and adorn them if external
 	var links = window.document.querySelectorAll('a[href]:not(.nav-link):not(.navbar-brand):not(.toc-action):not(.sidebar-link):not(.sidebar-item-toggle):not(.pagination-link):not(.no-external):not([aria-hidden]):not(.dropdown-item):not(.quarto-navigation-tool):not(.about-link)');
    for (var i=0; i<links.length; i++) {
      const link = links[i];
      if (!isInternal(link.href)) {
        // undo the damage that might have been done by quarto-nav.js in the case of
        // links that we want to consider external
        if (link.dataset.originalHref !== undefined) {
          link.href = link.dataset.originalHref;
        }
      }
    }
  function tippyHover(el, contentFn, onTriggerFn, onUntriggerFn) {
    const config = {
      allowHTML: true,
      maxWidth: 500,
      delay: 100,
      arrow: false,
      appendTo: function(el) {
          return el.parentElement;
      },
      interactive: true,
      interactiveBorder: 10,
      theme: 'quarto',
      placement: 'bottom-start',
    };
    if (contentFn) {
      config.content = contentFn;
    }
    if (onTriggerFn) {
      config.onTrigger = onTriggerFn;
    }
    if (onUntriggerFn) {
      config.onUntrigger = onUntriggerFn;
    }
    window.tippy(el, config); 
  }
  const noterefs = window.document.querySelectorAll('a[role="doc-noteref"]');
  for (var i=0; i<noterefs.length; i++) {
    const ref = noterefs[i];
    tippyHover(ref, function() {
      // use id or data attribute instead here
      let href = ref.getAttribute('data-footnote-href') || ref.getAttribute('href');
      try { href = new URL(href).hash; } catch {}
      const id = href.replace(/^#\/?/, "");
      const note = window.document.getElementById(id);
      if (note) {
        return note.innerHTML;
      } else {
        return "";
      }
    });
  }
  const xrefs = window.document.querySelectorAll('a.quarto-xref');
  const processXRef = (id, note) => {
    // Strip column container classes
    const stripColumnClz = (el) => {
      el.classList.remove("page-full", "page-columns");
      if (el.children) {
        for (const child of el.children) {
          stripColumnClz(child);
        }
      }
    }
    stripColumnClz(note)
    if (id === null || id.startsWith('sec-')) {
      // Special case sections, only their first couple elements
      const container = document.createElement("div");
      if (note.children && note.children.length > 2) {
        container.appendChild(note.children[0].cloneNode(true));
        for (let i = 1; i < note.children.length; i++) {
          const child = note.children[i];
          if (child.tagName === "P" && child.innerText === "") {
            continue;
          } else {
            container.appendChild(child.cloneNode(true));
            break;
          }
        }
        if (window.Quarto?.typesetMath) {
          window.Quarto.typesetMath(container);
        }
        return container.innerHTML
      } else {
        if (window.Quarto?.typesetMath) {
          window.Quarto.typesetMath(note);
        }
        return note.innerHTML;
      }
    } else {
      // Remove any anchor links if they are present
      const anchorLink = note.querySelector('a.anchorjs-link');
      if (anchorLink) {
        anchorLink.remove();
      }
      if (window.Quarto?.typesetMath) {
        window.Quarto.typesetMath(note);
      }
      if (note.classList.contains("callout")) {
        return note.outerHTML;
      } else {
        return note.innerHTML;
      }
    }
  }
  for (var i=0; i<xrefs.length; i++) {
    const xref = xrefs[i];
    tippyHover(xref, undefined, function(instance) {
      instance.disable();
      let url = xref.getAttribute('href');
      let hash = undefined; 
      if (url.startsWith('#')) {
        hash = url;
      } else {
        try { hash = new URL(url).hash; } catch {}
      }
      if (hash) {
        const id = hash.replace(/^#\/?/, "");
        const note = window.document.getElementById(id);
        if (note !== null) {
          try {
            const html = processXRef(id, note.cloneNode(true));
            instance.setContent(html);
          } finally {
            instance.enable();
            instance.show();
          }
        } else {
          // See if we can fetch this
          fetch(url.split('#')[0])
          .then(res => res.text())
          .then(html => {
            const parser = new DOMParser();
            const htmlDoc = parser.parseFromString(html, "text/html");
            const note = htmlDoc.getElementById(id);
            if (note !== null) {
              const html = processXRef(id, note);
              instance.setContent(html);
            } 
          }).finally(() => {
            instance.enable();
            instance.show();
          });
        }
      } else {
        // See if we can fetch a full url (with no hash to target)
        // This is a special case and we should probably do some content thinning / targeting
        fetch(url)
        .then(res => res.text())
        .then(html => {
          const parser = new DOMParser();
          const htmlDoc = parser.parseFromString(html, "text/html");
          const note = htmlDoc.querySelector('main.content');
          if (note !== null) {
            // This should only happen for chapter cross references
            // (since there is no id in the URL)
            // remove the first header
            if (note.children.length > 0 && note.children[0].tagName === "HEADER") {
              note.children[0].remove();
            }
            const html = processXRef(null, note);
            instance.setContent(html);
          } 
        }).finally(() => {
          instance.enable();
          instance.show();
        });
      }
    }, function(instance) {
    });
  }
      let selectedAnnoteEl;
      const selectorForAnnotation = ( cell, annotation) => {
        let cellAttr = 'data-code-cell="' + cell + '"';
        let lineAttr = 'data-code-annotation="' +  annotation + '"';
        const selector = 'span[' + cellAttr + '][' + lineAttr + ']';
        return selector;
      }
      const selectCodeLines = (annoteEl) => {
        const doc = window.document;
        const targetCell = annoteEl.getAttribute("data-target-cell");
        const targetAnnotation = annoteEl.getAttribute("data-target-annotation");
        const annoteSpan = window.document.querySelector(selectorForAnnotation(targetCell, targetAnnotation));
        const lines = annoteSpan.getAttribute("data-code-lines").split(",");
        const lineIds = lines.map((line) => {
          return targetCell + "-" + line;
        })
        let top = null;
        let height = null;
        let parent = null;
        if (lineIds.length > 0) {
            //compute the position of the single el (top and bottom and make a div)
            const el = window.document.getElementById(lineIds[0]);
            top = el.offsetTop;
            height = el.offsetHeight;
            parent = el.parentElement.parentElement;
          if (lineIds.length > 1) {
            const lastEl = window.document.getElementById(lineIds[lineIds.length - 1]);
            const bottom = lastEl.offsetTop + lastEl.offsetHeight;
            height = bottom - top;
          }
          if (top !== null && height !== null && parent !== null) {
            // cook up a div (if necessary) and position it 
            let div = window.document.getElementById("code-annotation-line-highlight");
            if (div === null) {
              div = window.document.createElement("div");
              div.setAttribute("id", "code-annotation-line-highlight");
              div.style.position = 'absolute';
              parent.appendChild(div);
            }
            div.style.top = top - 2 + "px";
            div.style.height = height + 4 + "px";
            div.style.left = 0;
            let gutterDiv = window.document.getElementById("code-annotation-line-highlight-gutter");
            if (gutterDiv === null) {
              gutterDiv = window.document.createElement("div");
              gutterDiv.setAttribute("id", "code-annotation-line-highlight-gutter");
              gutterDiv.style.position = 'absolute';
              const codeCell = window.document.getElementById(targetCell);
              const gutter = codeCell.querySelector('.code-annotation-gutter');
              gutter.appendChild(gutterDiv);
            }
            gutterDiv.style.top = top - 2 + "px";
            gutterDiv.style.height = height + 4 + "px";
          }
          selectedAnnoteEl = annoteEl;
        }
      };
      const unselectCodeLines = () => {
        const elementsIds = ["code-annotation-line-highlight", "code-annotation-line-highlight-gutter"];
        elementsIds.forEach((elId) => {
          const div = window.document.getElementById(elId);
          if (div) {
            div.remove();
          }
        });
        selectedAnnoteEl = undefined;
      };
        // Handle positioning of the toggle
    window.addEventListener(
      "resize",
      throttle(() => {
        elRect = undefined;
        if (selectedAnnoteEl) {
          selectCodeLines(selectedAnnoteEl);
        }
      }, 10)
    );
    function throttle(fn, ms) {
    let throttle = false;
    let timer;
      return (...args) => {
        if(!throttle) { // first call gets through
            fn.apply(this, args);
            throttle = true;
        } else { // all the others get throttled
            if(timer) clearTimeout(timer); // cancel #2
            timer = setTimeout(() => {
              fn.apply(this, args);
              timer = throttle = false;
            }, ms);
        }
      };
    }
      // Attach click handler to the DT
      const annoteDls = window.document.querySelectorAll('dt[data-target-cell]');
      for (const annoteDlNode of annoteDls) {
        annoteDlNode.addEventListener('click', (event) => {
          const clickedEl = event.target;
          if (clickedEl !== selectedAnnoteEl) {
            unselectCodeLines();
            const activeEl = window.document.querySelector('dt[data-target-cell].code-annotation-active');
            if (activeEl) {
              activeEl.classList.remove('code-annotation-active');
            }
            selectCodeLines(clickedEl);
            clickedEl.classList.add('code-annotation-active');
          } else {
            // Unselect the line
            unselectCodeLines();
            clickedEl.classList.remove('code-annotation-active');
          }
        });
      }
  const findCites = (el) => {
    const parentEl = el.parentElement;
    if (parentEl) {
      const cites = parentEl.dataset.cites;
      if (cites) {
        return {
          el,
          cites: cites.split(' ')
        };
      } else {
        return findCites(el.parentElement)
      }
    } else {
      return undefined;
    }
  };
  var bibliorefs = window.document.querySelectorAll('a[role="doc-biblioref"]');
  for (var i=0; i<bibliorefs.length; i++) {
    const ref = bibliorefs[i];
    const citeInfo = findCites(ref);
    if (citeInfo) {
      tippyHover(citeInfo.el, function() {
        var popup = window.document.createElement('div');
        citeInfo.cites.forEach(function(cite) {
          var citeDiv = window.document.createElement('div');
          citeDiv.classList.add('hanging-indent');
          citeDiv.classList.add('csl-entry');
          var biblioDiv = window.document.getElementById('ref-' + cite);
          if (biblioDiv) {
            citeDiv.innerHTML = biblioDiv.innerHTML;
          }
          popup.appendChild(citeDiv);
        });
        return popup.innerHTML;
      });
    }
  }
});
</script>
</div> <!-- /content -->




</body></html>