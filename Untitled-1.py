# import numpy as np

# # 定義均值向量與協方差矩陣
# mean = [0, 0]                # 均值 μ_x = 0, μ_y = 0
# cov = [[1, 0.7], [0.7, 1]]   # 協方差矩陣（σ_x²=1, σ_y²=1, σ_xy=0.5） #這是一個矩陣

# # 生成1000個樣本點
# samples = np.random.multivariate_normal(mean, cov, 1000)#生成多元高斯分佈的樣本點
# import matplotlib.pyplot as plt

# plt.figure(figsize=(8, 6))
# plt.scatter(samples[:, 0], samples[:, 1], alpha=0.6, c='blue', edgecolors='w')#繪製散點圖，alpha是透明度，c是顏色，edgecolors是邊框顏色，samples[:, 1]是y軸，samples[:, 0]是x軸
# plt.title("雙變量高斯分佈散點圖")
# plt.xlabel("X"), plt.ylabel("Y")
# plt.grid(True)
# plt.show()
# from scipy.stats import multivariate_normal

# # 生成網格點
# x, y = np.mgrid[-4:4:0.1, -4:4:0.1]#生成網格點，-4:4:0.1是範圍，0.1是步長
# pos = np.dstack((x, y))#將x和y堆疊成一個三維陣列

# # 計算機率密度
# rv = multivariate_normal(mean, cov)
# z = rv.pdf(pos)

# # 繪製等高線
# plt.figure(figsize=(8, 6))
# plt.contourf(x, y, z, levels=10, cmap='viridis')
# plt.colorbar(label="機率密度")
# plt.title("雙變量高斯分佈等高線圖")
# plt.xlabel("X"), plt.ylabel("Y")
# plt.show()

# cov_independent = [[1, 0], [0, 1]]    # 無相關性
# samples_indep = np.random.multivariate_normal(mean, cov_independent, 1000)

# plt.figure(figsize=(12, 5))
# plt.subplot(121)
# plt.scatter(samples[:, 0], samples[:, 1], alpha=0.6, c='blue')
# plt.title("正相關 (σ_xy=0.5)")
# plt.subplot(122)
# plt.scatter(samples_indep[:, 0], samples_indep[:, 1], alpha=0.6, c='red')
# plt.title("無相關 (σ_xy=0)")
# plt.tight_layout()
# plt.show()

import numpy as np
import pandas as pd
import statsmodels.api as sm
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats

# 設置隨機種子
np.random.seed(123)

# 生成數據
n = 100
x = np.random.normal(0, 2, n)
y = 1 + 0.5 * x + 0.3 * x**2 + np.random.normal(0, 1, n)
data = pd.DataFrame({'x': x, 'y': y})

# 創建二次項
data['x_squared'] = data['x']**2

# 擬合模型
model_m1 = sm.OLS(data['y'], sm.add_constant(data['x'])).fit()
model_m2 = sm.OLS(data['y'], sm.add_constant(pd.DataFrame({'x': data['x'], 'x_squared': data['x_squared']}))).fit()

# 顯示模型摘要
print(model_m1.summary())
print(model_m2.summary())

# 計算對數似然
loglik_m1 = model_m1.llf
loglik_m2 = model_m2.llf
print(f"Log-likelihood Model 1: {loglik_m1}")
print(f"Log-likelihood Model 2: {loglik_m2}")

# 進行似然比檢驗
lrt_stat = -2 * (loglik_m1 - loglik_m2)
print(f"LRT Statistic: {lrt_stat}")
p_value = stats.chi2.sf(lrt_stat, df=1)
print(f"P-value: {p_value}")

# 繪製散點圖和擬合線
plt.figure(figsize=(10, 6))
sns.scatterplot(x='x', y='y', data=data, alpha=0.6, color='black')
sns.regplot(x='x', y='y', data=data, scatter=False, color='blue', 
            line_kws={'linestyle': 'dashed', 'linewidth': 1}, 
            order=1)  # 線性模型
sns.regplot(x='x', y='y', data=data, scatter=False, color='red', 
            line_kws={'linewidth': 1}, 
            order=2)  # 二次模型

# 添加標題和副標題
plt.title("模型比較：線性 vs 二次模型")
plt.suptitle(f"LRT統計量 = {round(lrt_stat, 2)}, p = {round(p_value, 4)}", y=0.95)
plt.xlabel("X")
plt.ylabel("Y")
plt.grid(True)
plt.show()