import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import copy
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass, field
import os
import pickle

# 导入原始模型参数类（确保与原始代码兼容）
from dataclasses import dataclass, field

@dataclass
class ModelParameters:
    """模型參數類，用於保存模型所需的所有參數"""
    # 策略相關參數
    p_high: float = 0.5  # 高價值項目出現的概率
    p_internal_actual_H: float = 0.5  # 高價值項目內部記憶實際記住的概率
    p_internal_actual_L: float = 0.3  # 低價值項目內部記憶實際記住的概率
    p_external_actual: float = 0.8  # 外部存儲實際記住的概率
    
    # 每種策略的項目數量，用於模擬實驗
    n_high_items: int = 10  # 高價值項目數
    n_low_items: int = 10  # 低價值項目數
    n_episodes_per_strategy_eval: int = 100  # 每個策略評估的情節數
    n_model_runs: int = 5000  # 模型運行次數

    # 後設認知參數
    metacognitive_threshold: float = 0.1  # 後設認知閾值
    metacognitive_bias: float = 0.0  # 後設認知偏差
    confidence_noise_std: float = 0.05  # 信心判斷的噪音標準差

    # 價值相關參數
    value_high: float = 1.0  # 高價值項目的價值
    value_low: float = 0.1  # 低價值項目的價值
    p_surprise_test: float = 0.0  # 意外測試的概率 (僅限內部記憶)


# ======= 第一部分：個體後設認知參數生成 =======

class IndividualDifferencesGenerator:
    """生成有個體差異的後設認知參數"""
    
    def __init__(
        self,
        metacognitive_threshold_mean: float = 0.1,
        metacognitive_threshold_std: float = 0.05,
        metacognitive_bias_mean: float = 0.0,
        metacognitive_bias_std: float = 0.1,
        confidence_noise_mean: float = 0.05,
        confidence_noise_std: float = 0.02
    ):
        """
        初始化個體差異生成器
        
        Args:
            metacognitive_threshold_mean: 後設認知閾值的群體平均值
            metacognitive_threshold_std: 後設認知閾值的個體差異標準差
            metacognitive_bias_mean: 後設認知偏差的群體平均值
            metacognitive_bias_std: 後設認知偏差的個體差異標準差
            confidence_noise_mean: 信心噪音的群體平均值
            confidence_noise_std: 信心噪音的個體差異標準差
        """
        self.metacognitive_threshold_mean = metacognitive_threshold_mean
        self.metacognitive_threshold_std = metacognitive_threshold_std
        self.metacognitive_bias_mean = metacognitive_bias_mean
        self.metacognitive_bias_std = metacognitive_bias_std
        self.confidence_noise_mean = confidence_noise_mean
        self.confidence_noise_std = confidence_noise_std
    
    def sample_individuals(self, n_individuals: int) -> List[Dict[str, float]]:
        """
        采样指定数量的个体参数
        
        Args:
            n_individuals: 要生成的个体数量
            
        Returns:
            包含个体后设认知参数的字典列表
        """
        individuals = []
        for _ in range(n_individuals):
            # 采样后设认知阈值（有下限以避免负值）
            threshold = max(0.01, np.random.normal(
                self.metacognitive_threshold_mean, 
                self.metacognitive_threshold_std
            ))
            
            # 采样后设认知偏差
            bias = np.random.normal(
                self.metacognitive_bias_mean, 
                self.metacognitive_bias_std
            )
            
            # 采样信心噪声（有下限以避免负值）
            noise = max(0.01, np.random.normal(
                self.confidence_noise_mean, 
                self.confidence_noise_std
            ))
            
            individuals.append({
                'metacognitive_threshold': threshold,
                'metacognitive_bias': bias,
                'confidence_noise_std': noise
            })
        
        return individuals
    
    def create_population_with_demographics(
        self, 
        n_individuals: int,
        include_age: bool = True,
        include_education: bool = True
    ) -> pd.DataFrame:
        """
        创建包含人口统计学变量的个体样本
        
        Args:
            n_individuals: 要生成的个体数量
            include_age: 是否包含年龄变量
            include_education: 是否包含教育程度变量
            
        Returns:
            包含个体信息的数据框
        """
        individuals = self.sample_individuals(n_individuals)
        df = pd.DataFrame(individuals)
        
        # 添加年龄变量（18-80岁）
        if include_age:
            ages = np.random.randint(18, 81, size=n_individuals)
            df['age'] = ages
            
            # 基于年龄调整后设认知参数（示例：年龄越大，阈值越高，偏差越大）
            age_effect = (df['age'] - 18) / 62  # 归一化到0-1范围
            df['metacognitive_threshold'] += age_effect * 0.05
            df['metacognitive_bias'] += age_effect * 0.1
        
        # 添加教育程度变量（0-5，对应不同教育水平）
        if include_education:
            education_levels = np.random.randint(0, 6, size=n_individuals)
            df['education'] = education_levels
            
            # 基于教育程度调整后设认知参数（示例：教育程度越高，阈值越低，噪声越小）
            edu_effect = df['education'] / 5  # 归一化到0-1范围
            df['metacognitive_threshold'] -= edu_effect * 0.03
            df['confidence_noise_std'] -= edu_effect * 0.01
            
            # 确保参数在有效范围内
            df['metacognitive_threshold'] = df['metacognitive_threshold'].clip(0.01, 0.5)
            df['confidence_noise_std'] = df['confidence_noise_std'].clip(0.01, 0.2)
        
        return df


# ======= 第二部分：分層後設認知模型 =======

class MetacognitiveModel:
    """個體化後設認知模型"""
    
    def __init__(
        self, 
        base_params: ModelParameters,
        individual_params: Optional[Dict[str, float]] = None
    ):
        """
        初始化後設認知模型
        
        Args:
            base_params: 基础模型参数
            individual_params: 个体后设认知参数（如果为None则使用基础参数）
        """
        # 保存基础模型参数
        self.base_params = copy.deepcopy(base_params)
        
        # 设置个体后设认知参数
        if individual_params:
            self.metacognitive_threshold = individual_params.get(
                'metacognitive_threshold', base_params.metacognitive_threshold
            )
            self.metacognitive_bias = individual_params.get(
                'metacognitive_bias', base_params.metacognitive_bias
            )
            self.confidence_noise_std = individual_params.get(
                'confidence_noise_std', base_params.confidence_noise_std
            )
        else:
            self.metacognitive_threshold = base_params.metacognitive_threshold
            self.metacognitive_bias = base_params.metacognitive_bias
            self.confidence_noise_std = base_params.confidence_noise_std
    
    def get_perceived_accuracy(self, objective_accuracy: float) -> float:
        """
        计算个体的主观感知准确率
        
        Args:
            objective_accuracy: 客观准确率（0-1之间）
            
        Returns:
            主观感知的准确率（0-1之间）
        """
        # 基础感知成功率 = 客观准确率 + 后设认知偏差
        perceived_rate = objective_accuracy + self.metacognitive_bias
        
        # 加入噪音，模拟信心判断的随机性
        if self.confidence_noise_std > 0:
            perceived_rate += np.random.normal(0, self.confidence_noise_std)
        
        # 确保感知成功率在 0 和 1 之间
        return np.clip(perceived_rate, 0, 1)
    
    def decide_offload(self, confidence: float) -> bool:
        """
        决定是否卸载项目
        
        Args:
            confidence: 对内部记忆的信心（0-1之间）
            
        Returns:
            是否决定卸载（True/False）
        """
        return confidence < self.metacognitive_threshold
    
    def simulate_episode(
        self, 
        strategy: Tuple[bool, bool, bool, bool]
    ) -> Tuple[float, float, Dict[str, Any]]:
        """
        模拟一个情节的学习和记忆
        
        Args:
            strategy: 策略元组 (store_H_internal, store_L_internal, offload_H_allowed, offload_L_allowed)
            
        Returns:
            元组 (准确率, 卸载率, 详细统计信息)
        """
        params = self.base_params
        
        # 初始化统计数据
        n_total_items = params.n_high_items + params.n_low_items
        n_recalled = 0
        n_total_offloaded = 0
        n_high_offloaded = 0
        n_low_offloaded = 0
        
        # 处理高价值项目
        for _ in range(params.n_high_items):
            objective_accuracy = params.p_internal_actual_H
            perceived_accuracy = self.get_perceived_accuracy(objective_accuracy)
            
            # 后设认知决策
            decided_to_offload = False
            if strategy[2] and self.decide_offload(perceived_accuracy):
                decided_to_offload = True
                n_high_offloaded += 1
                n_total_offloaded += 1
            
            # 模拟回忆结果
            if decided_to_offload:
                # 使用外部存储
                if np.random.random() < params.p_external_actual:
                    n_recalled += 1
            else:
                # 使用内部记忆
                if np.random.random() < params.p_internal_actual_H:
                    n_recalled += 1
        
        # 处理低价值项目
        for _ in range(params.n_low_items):
            objective_accuracy = params.p_internal_actual_L
            perceived_accuracy = self.get_perceived_accuracy(objective_accuracy)
            
            # 后设认知决策
            decided_to_offload = False
            if strategy[3] and self.decide_offload(perceived_accuracy):
                decided_to_offload = True
                n_low_offloaded += 1
                n_total_offloaded += 1
            
            # 模拟回忆结果
            if decided_to_offload:
                # 使用外部存储
                if np.random.random() < params.p_external_actual:
                    n_recalled += 1
            else:
                # 使用内部记忆
                if np.random.random() < params.p_internal_actual_L:
                    n_recalled += 1
        
        # 计算准确率和卸载率
        accuracy = n_recalled / n_total_items
        offload_rate = n_total_offloaded / n_total_items
        
        # 返回详细统计信息
        stats = {
            'accuracy': accuracy,
            'offload_rate': offload_rate,
            'high_offload_rate': n_high_offloaded / params.n_high_items if params.n_high_items > 0 else 0,
            'low_offload_rate': n_low_offloaded / params.n_low_items if params.n_low_items > 0 else 0,
            'n_recalled': n_recalled,
            'n_total_offloaded': n_total_offloaded,
            'n_high_offloaded': n_high_offloaded,
            'n_low_offloaded': n_low_offloaded
        }
        
        return accuracy, offload_rate, stats
    
    def evaluate_strategy(
        self, 
        strategy: Tuple[bool, bool, bool, bool],
        n_episodes: int = None
    ) -> Dict[str, Any]:
        """
        评估一个策略在多个情节上的表现
        
        Args:
            strategy: 策略元组
            n_episodes: 情节数量（如果为None则使用base_params中的值）
            
        Returns:
            包含评估结果的字典
        """
        if n_episodes is None:
            n_episodes = self.base_params.n_episodes_per_strategy_eval
        
        # 初始化结果统计
        accuracies = []
        offload_rates = []
        high_offload_rates = []
        low_offload_rates = []
        
        # 模拟多个情节
        for _ in range(n_episodes):
            accuracy, offload_rate, stats = self.simulate_episode(strategy)
            accuracies.append(accuracy)
            offload_rates.append(offload_rate)
            high_offload_rates.append(stats['high_offload_rate'])
            low_offload_rates.append(stats['low_offload_rate'])
        
        # 计算平均值和标准差
        return {
            'strategy': strategy,
            'mean_accuracy': np.mean(accuracies),
            'std_accuracy': np.std(accuracies),
            'mean_offload_rate': np.mean(offload_rates),
            'std_offload_rate': np.std(offload_rates),
            'mean_high_offload_rate': np.mean(high_offload_rates),
            'mean_low_offload_rate': np.mean(low_offload_rates),
            'metacognitive_threshold': self.metacognitive_threshold,
            'metacognitive_bias': self.metacognitive_bias,
            'confidence_noise_std': self.confidence_noise_std
        }


# ======= 第三部分：參數校準和敏感性分析 =======

class MetacognitiveCalibration:
    """后设认知参数校准工具"""
    
    def __init__(self, base_params: ModelParameters):
        """
        初始化校准工具
        
        Args:
            base_params: 基础模型参数
        """
        self.base_params = base_params
    
    def calibrate_from_empirical_data(
        self, 
        empirical_data: pd.DataFrame
    ) -> Dict[str, float]:
        """
        根据经验数据校准后设认知参数
        
        Args:
            empirical_data: 包含真实人类行为数据的数据框
                应当包含列：'subjective_confidence'、'objective_accuracy'、'offloading_decision'
                
        Returns:
            校准后的参数字典
        """
        if empirical_data is None or len(empirical_data) == 0:
            print("警告：没有提供经验数据，无法进行校准")
            return {
                'metacognitive_threshold_mean': self.base_params.metacognitive_threshold,
                'metacognitive_threshold_std': 0.05,
                'metacognitive_bias_mean': self.base_params.metacognitive_bias,
                'metacognitive_bias_std': 0.1,
                'confidence_noise_mean': self.base_params.confidence_noise_std,
                'confidence_noise_std': 0.02
            }
        
        # 计算后设认知偏差：主观信心 - 客观准确率
        if 'subjective_confidence' in empirical_data and 'objective_accuracy' in empirical_data:
            biases = empirical_data['subjective_confidence'] - empirical_data['objective_accuracy']
            mean_bias = np.mean(biases)
            std_bias = np.std(biases)
        else:
            mean_bias = 0.0
            std_bias = 0.1
            print("警告：经验数据中缺少主观信心或客观准确率信息")
        
        # 估算后设认知阈值
        if 'subjective_confidence' in empirical_data and 'offloading_decision' in empirical_data:
            # 查找决定卸载的信心水平的分布
            offload_confidences = empirical_data.loc[
                empirical_data['offloading_decision'] == True, 
                'subjective_confidence'
            ]
            
            if len(offload_confidences) > 0:
                threshold_mean = np.mean(offload_confidences)
                threshold_std = np.std(offload_confidences)
            else:
                threshold_mean = 0.1
                threshold_std = 0.05
                print("警告：经验数据中没有足够的卸载决策")
        else:
            threshold_mean = 0.1
            threshold_std = 0.05
            print("警告：经验数据中缺少主观信心或卸载决策信息")
        
        # 估计信心噪声
        if 'subjective_confidence' in empirical_data and 'objective_accuracy' in empirical_data:
            # 根据主观信心与客观准确率的差异估计噪声
            residuals = empirical_data['subjective_confidence'] - (
                empirical_data['objective_accuracy'] + mean_bias
            )
            noise_mean = np.std(residuals)  # 使用残差标准差作为噪声估计
            noise_std = noise_mean / 2  # 简单假设
        else:
            noise_mean = 0.05
            noise_std = 0.02
        
        return {
            'metacognitive_threshold_mean': threshold_mean,
            'metacognitive_threshold_std': threshold_std,
            'metacognitive_bias_mean': mean_bias,
            'metacognitive_bias_std': std_bias,
            'confidence_noise_mean': noise_mean,
            'confidence_noise_std': noise_std
        }
    
    def sensitivity_analysis(
        self, 
        parameter_ranges: Dict[str, List[float]],
        strategy: Tuple[bool, bool, bool, bool],
        n_episodes: int = 100
    ) -> pd.DataFrame:
        """
        对后设认知参数进行敏感性分析
        
        Args:
            parameter_ranges: 包含参数范围的字典，例如：
                {
                    'metacognitive_threshold': [0.05, 0.1, 0.15, 0.2],
                    'metacognitive_bias': [-0.2, -0.1, 0.0, 0.1, 0.2],
                    'confidence_noise_std': [0.01, 0.05, 0.1, 0.2]
                }
            strategy: 要评估的策略
            n_episodes: 每个参数组合的模拟情节数
            
        Returns:
            包含敏感性分析结果的数据框
        """
        results = []
        
        # 确定参数范围
        threshold_range = parameter_ranges.get(
            'metacognitive_threshold', 
            [self.base_params.metacognitive_threshold]
        )
        bias_range = parameter_ranges.get(
            'metacognitive_bias', 
            [self.base_params.metacognitive_bias]
        )
        noise_range = parameter_ranges.get(
            'confidence_noise_std', 
            [self.base_params.confidence_noise_std]
        )
        
        # 遍历所有参数组合
        for threshold in threshold_range:
            for bias in bias_range:
                for noise in noise_range:
                    # 创建个体参数
                    individual_params = {
                        'metacognitive_threshold': threshold,
                        'metacognitive_bias': bias,
                        'confidence_noise_std': noise
                    }
                    
                    # 创建模型并评估策略
                    model = MetacognitiveModel(self.base_params, individual_params)
                    evaluation = model.evaluate_strategy(strategy, n_episodes)
                    
                    # 添加到结果
                    results.append({
                        'metacognitive_threshold': threshold,
                        'metacognitive_bias': bias,
                        'confidence_noise_std': noise,
                        'mean_accuracy': evaluation['mean_accuracy'],
                        'std_accuracy': evaluation['std_accuracy'],
                        'mean_offload_rate': evaluation['mean_offload_rate'],
                        'std_offload_rate': evaluation['std_offload_rate'],
                        'mean_high_offload_rate': evaluation['mean_high_offload_rate'],
                        'mean_low_offload_rate': evaluation['mean_low_offload_rate']
                    })
        
        # 转换为数据框并返回
        return pd.DataFrame(results)


# ======= 第四部分：個體差異可視化 =======

class MetacognitiveVisualizer:
    """后设认知个体差异可视化工具"""
    
    @staticmethod
    def visualize_individual_differences(
        results_df: pd.DataFrame,
        output_path: str = None,
        show_plot: bool = True
    ):
        """
        可视化个体差异对模型性能的影响
        
        Args:
            results_df: 包含不同个体模拟结果的数据框
            output_path: 图像保存路径（如果为None则不保存）
            show_plot: 是否显示图像
        """
        # 创建画布
        fig, axes = plt.subplots(2, 2, figsize=(14, 12))
        
        # 设置风格
        sns.set_style("whitegrid")
        
        # 1. 阈值与准确率关系图
        sns.scatterplot(
            data=results_df, 
            x='metacognitive_threshold', 
            y='mean_accuracy',
            hue='metacognitive_bias',
            palette='viridis',
            size='confidence_noise_std',
            sizes=(20, 200),
            alpha=0.7,
            ax=axes[0, 0]
        )
        axes[0, 0].set_title('閾值與準確率關係', fontsize=14)
        axes[0, 0].set_xlabel('後設認知閾值', fontsize=12)
        axes[0, 0].set_ylabel('平均準確率', fontsize=12)
        
        # 2. 偏差与卸载率关系图
        sns.scatterplot(
            data=results_df, 
            x='metacognitive_bias', 
            y='mean_offload_rate',
            hue='metacognitive_threshold',
            palette='plasma',
            size='confidence_noise_std',
            sizes=(20, 200),
            alpha=0.7,
            ax=axes[0, 1]
        )
        axes[0, 1].set_title('偏差與卸載率關係', fontsize=14)
        axes[0, 1].set_xlabel('後設認知偏差', fontsize=12)
        axes[0, 1].set_ylabel('平均卸載率', fontsize=12)
        
        # 3. 噪声与准确率标准差关系图
        sns.scatterplot(
            data=results_df, 
            x='confidence_noise_std', 
            y='std_accuracy',
            hue='metacognitive_threshold',
            palette='magma',
            size='metacognitive_bias',
            sizes=(20, 200),
            alpha=0.7,
            ax=axes[1, 0]
        )
        axes[1, 0].set_title('噪聲與準確率變異性關係', fontsize=14)
        axes[1, 0].set_xlabel('信心噪聲標準差', fontsize=12)
        axes[1, 0].set_ylabel('準確率標準差', fontsize=12)
        
        # 4. 高值与低值项目卸载率比较
        if 'mean_high_offload_rate' in results_df.columns and 'mean_low_offload_rate' in results_df.columns:
            # 准备数据
            plot_data = pd.melt(
                results_df, 
                id_vars=['metacognitive_threshold', 'metacognitive_bias', 'confidence_noise_std'],
                value_vars=['mean_high_offload_rate', 'mean_low_offload_rate'],
                var_name='item_type', 
                value_name='offload_rate'
            )
            plot_data['item_type'] = plot_data['item_type'].map({
                'mean_high_offload_rate': '高價值項目',
                'mean_low_offload_rate': '低價值項目'
            })
            
            # 绘制箱线图
            sns.boxplot(
                data=plot_data, 
                x='metacognitive_bias', 
                y='offload_rate',
                hue='item_type',
                palette='Set2',
                ax=axes[1, 1]
            )
            axes[1, 1].set_title('不同價值項目的卸載率比較', fontsize=14)
            axes[1, 1].set_xlabel('後設認知偏差', fontsize=12)
            axes[1, 1].set_ylabel('平均卸載率', fontsize=12)
        else:
            # 如果数据中没有高低值项目卸载率，绘制阈值与偏差的关系热图
            pivot_table = results_df.pivot_table(
                values='mean_offload_rate', 
                index='metacognitive_threshold',
                columns='metacognitive_bias'
            )
            sns.heatmap(
                pivot_table, 
                annot=True, 
                cmap='YlGnBu', 
                fmt='.2f',
                ax=axes[1, 1]
            )
            axes[1, 1].set_title('閾值與偏差對卸載率的影響', fontsize=14)
            axes[1, 1].set_xlabel('後設認知偏差', fontsize=12)
            axes[1, 1].set_ylabel('後設認知閾值', fontsize=12)
        
        # 添加总标题
        plt.suptitle('後設認知參數個體差異分析', fontsize=16, y=0.98)
        plt.tight_layout(rect=[0, 0, 1, 0.96])
        
        # 保存图像
        if output_path:
            plt.savefig(output_path, dpi=300, bbox_inches='tight')
        
        # 显示图像
        if show_plot:
            plt.show()
        else:
            plt.close()
    
    @staticmethod
    def plot_parameter_distributions(
        population_df: pd.DataFrame,
        output_path: str = None,
        show_plot: bool = True
    ):
        """
        可视化参数分布
        
        Args:
            population_df: 包含个体参数的数据框
            output_path: 图像保存路径
            show_plot: 是否显示图像
        """
        # 创建画布
        fig, axes = plt.subplots(1, 3, figsize=(15, 5))
        
        # 1. 后设认知阈值分布
        sns.histplot(
            population_df['metacognitive_threshold'], 
            kde=True, 
            ax=axes[0],
            color='skyblue'
        )
        axes[0].set_title('後設認知閾值分佈', fontsize=14)
        axes[0].set_xlabel('閾值', fontsize=12)
        axes[0].set_ylabel('頻率', fontsize=12)
        
        # 2. 后设认知偏差分布
        sns.histplot(
            population_df['metacognitive_bias'], 
            kde=True, 
            ax=axes[1],
            color='lightgreen'
        )
        axes[1].set_title('後設認知偏差分佈', fontsize=14)
        axes[1].set_xlabel('偏差', fontsize=12)
        axes[1].set_ylabel('頻率', fontsize=12)
        
        # 3. 信心噪声分布
        sns.histplot(
            population_df['confidence_noise_std'], 
            kde=True, 
            ax=axes[2],
            color='salmon'
        )
        axes[2].set_title('信心噪聲分佈', fontsize=14)
        axes[2].set_xlabel('噪聲標準差', fontsize=12)
        axes[2].set_ylabel('頻率', fontsize=12)
        
        # 添加总标题
        plt.suptitle('參數分佈可視化', fontsize=16)
        plt.tight_layout(rect=[0, 0, 1, 0.95])
        
        # 保存图像
        if output_path:
            plt.savefig(output_path, dpi=300, bbox_inches='tight')
        
        # 显示图像
        if show_plot:
            plt.show()
        else:
            plt.close()
    
    @staticmethod
    def plot_demographic_effects(
        population_df: pd.DataFrame,
        demographic_var: str,
        output_path: str = None,
        show_plot: bool = True
    ):
        """
        可视化人口统计学变量对后设认知参数的影响
        
        Args:
            population_df: 包含个体参数和人口统计学变量的数据框
            demographic_var: 人口统计学变量名（例如'age'或'education'）
            output_path: 图像保存路径
            show_plot: 是否显示图像
        """
        if demographic_var not in population_df.columns:
            print(f"警告：数据框中不存在变量 '{demographic_var}'")
            return
        
        # 创建画布
        fig, axes = plt.subplots(1, 3, figsize=(15, 5))
        
        # 1. 阈值与人口统计学变量的关系
        sns.regplot(
            data=population_df, 
            x=demographic_var, 
            y='metacognitive_threshold',
            ax=axes[0],
            scatter_kws={'alpha': 0.5},
            line_kws={'color': 'red'}
        )
        axes[0].set_title(f'{demographic_var} 與閾值關係', fontsize=14)
        axes[0].set_xlabel(demographic_var, fontsize=12)
        axes[0].set_ylabel('後設認知閾值', fontsize=12)
        
        # 2. 偏差与人口统计学变量的关系
        sns.regplot(
            data=population_df, 
            x=demographic_var, 
            y='metacognitive_bias',
            ax=axes[1],
            scatter_kws={'alpha': 0.5},
            line_kws={'color': 'red'}
        )
        axes[1].set_title(f'{demographic_var} 與偏差關係', fontsize=14)
        axes[1].set_xlabel(demographic_var, fontsize=12)
        axes[1].set_ylabel('後設認知偏差', fontsize=12)
        
        # 3. 噪声与人口统计学变量的关系
        sns.regplot(
            data=population_df, 
            x=demographic_var, 
            y='confidence_noise_std',
            ax=axes[2],
            scatter_kws={'alpha': 0.5},
            line_kws={'color': 'red'}
        )
        axes[2].set_title(f'{demographic_var} 與噪聲關係', fontsize=14)
        axes[2].set_xlabel(demographic_var, fontsize=12)
        axes[2].set_ylabel('信心噪聲標準差', fontsize=12)
        
        # 添加总标题
        plt.suptitle(f'{demographic_var} 對後設認知參數的影響', fontsize=16)
        plt.tight_layout(rect=[0, 0, 1, 0.95])
        
        # 保存图像
        if output_path:
            plt.savefig(output_path, dpi=300, bbox_inches='tight')
        
        # 显示图像
        if show_plot:
            plt.show()
        else:
            plt.close()


# ======= 第五部分：使用示例 =======

def run_example():
    """运行示例代码，展示主要功能"""
    print("=== 後設認知個體差異模型示例 ===")
    
    # 1. 创建基础模型参数
    base_params = ModelParameters()
    print(f"基礎參數: metacognitive_threshold={base_params.metacognitive_threshold}, "
          f"metacognitive_bias={base_params.metacognitive_bias}, "
          f"confidence_noise_std={base_params.confidence_noise_std}")
    
    # 2. 生成带有个体差异的参数
    generator = IndividualDifferencesGenerator()
    individuals = generator.sample_individuals(10)
    print(f"\n生成了 {len(individuals)} 個有個體差異的參數:")
    for i, params in enumerate(individuals[:3]):
        print(f"  個體 {i+1}: threshold={params['metacognitive_threshold']:.2f}, "
              f"bias={params['metacognitive_bias']:.2f}, "
              f"noise={params['confidence_noise_std']:.2f}")
    print("  ...")
    
    # 3. 创建包含人口统计学变量的样本
    population_df = generator.create_population_with_demographics(100)
    print(f"\n生成了 {len(population_df)} 個帶有人口統計學變量的個體樣本")
    
    # 4. 对一个策略评估多个个体
    print("\n評估不同個體對同一策略的表現...")
    strategy = (True, True, True, True)  # (store_H_internal, store_L_internal, offload_H_allowed, offload_L_allowed)
    
    # 存储每个个体的评估结果
    all_eval_results = []
    
    # 只评估前5个个体（为了示例简洁）
    for i, individual_params in enumerate(individuals[:5]):
        model = MetacognitiveModel(base_params, individual_params)
        eval_result = model.evaluate_strategy(strategy, n_episodes=50)
        
        all_eval_results.append(eval_result)
        
        print(f"  個體 {i+1}: accuracy={eval_result['mean_accuracy']:.3f}, "
              f"offload_rate={eval_result['mean_offload_rate']:.3f}")
    
    # 5. 进行敏感性分析
    print("\n進行敏感性分析...")
    calibration = MetacognitiveCalibration(base_params)
    sensitivity_params = {
        'metacognitive_threshold': [0.05, 0.1, 0.2],
        'metacognitive_bias': [-0.1, 0.0, 0.1],
        'confidence_noise_std': [0.02, 0.05, 0.1]
    }
    sensitivity_results = calibration.sensitivity_analysis(
        sensitivity_params, strategy, n_episodes=30
    )
    print(f"  完成敏感性分析，生成了 {len(sensitivity_results)} 個參數組合的結果")
    
    # 6. 可视化分析结果
    print("\n生成可視化圖表...")
    visualizer = MetacognitiveVisualizer()
    
    # 将评估结果转换为数据框
    eval_df = pd.DataFrame(all_eval_results)
    
    # 可视化个体差异对模型性能的影响
    visualizer.visualize_individual_differences(
        sensitivity_results, 
        output_path="metacognitive_individual_differences.png",
        show_plot=False
    )
    
    # 可视化参数分布
    visualizer.plot_parameter_distributions(
        population_df, 
        output_path="parameter_distributions.png",
        show_plot=False
    )
    
    # 可视化年龄效应（如果有年龄变量）
    if 'age' in population_df.columns:
        visualizer.plot_demographic_effects(
            population_df, 
            'age', 
            output_path="age_effects.png",
            show_plot=False
        )
    
    print("\n示例运行完成！生成的图表已保存为 PNG 文件。")
    
    # 返回生成的数据，以便进一步分析
    return {
        'base_params': base_params,
        'individuals': individuals,
        'population_df': population_df,
        'eval_results': all_eval_results,
        'sensitivity_results': sensitivity_results
    }


if __name__ == "__main__":
    # 运行示例代码
    results = run_example()
