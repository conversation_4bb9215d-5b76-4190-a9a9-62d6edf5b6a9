import numpy as np
import matplotlib.pyplot as plt
from dataclasses import dataclass
from typing import List, Tuple, Dict, Any
import itertools
from collections import defaultdict
import random


@dataclass
class ModelParameters:
    n_high_items: int = 3  
    n_low_items: int = 3   
    value_high: float = 8.0 
    value_low: float = 2.0   
    cost_offloading: float = 1 # 卸載成本，在 Fig.7改變
    
    # 內部記憶準確率，根據總內部記憶項目數
    accuracy_internal_1_item: float = 0.95  
    accuracy_internal_2_items: float = 0.925
    accuracy_internal_3_items: float = 0.90
    accuracy_internal_6_items: float = 0.75
    accuracy_offloaded: float = 0.7

    # 模擬控制參數
    n_episodes_per_strategy_eval: int = 50
    n_model_runs: int = 10000

# 策略表示: (store_H_internal, store_L_internal, offload_H, offload_L)
# 每個元素是 (True/False)
Strategy = Tuple[bool, bool, bool, bool]

# Fig. 8 策略表示: (encode, offload)
StrategyFig8 = Tuple[bool, bool]


def get_all_strategies(allow_offloading = True, offload_restriction = "none") -> List[Strategy]:
    """
    產生所有可能的策略 

    """
    options_sh = [True, False] # 可以選擇是否記憶高價值
    options_sl = [True, False] # 可以選擇是否記憶低價值

    if not allow_offloading:
        options_oh = [False] # 不允許卸載高價值
        options_ol = [False] # 不允許卸載低價值
    else:
        if offload_restriction == "high_only":
            options_oh = [True, False]
            options_ol = [False] # 低價值不允許卸載
        else: # "none" restriction or other unhandled
            options_oh = [True, False]
            options_ol = [True, False]
    return list(itertools.product(options_sh, options_sl, options_oh, options_ol))

def get_strategies_fig8() -> List[StrategyFig8]:
    # encode 可以是 True 或 False
    # offload 可以是 True 或 False
    return [
        (True, True),    # encode=True, offload=True
        (True, False),   # encode=True, offload=False
        (False, True),   # encode=False, offload=True
        (False, False)   # encode=False, offload=False
    ]


def get_internal_memory_accuracy(strategy: Strategy, params: ModelParameters) -> float:
    #初始化
    items_stored_internally = 0
    # 模型中只要選擇了store就假設所有相應類型的項目都會嘗試存儲
    if strategy[0]: # 選擇記憶高價值
        items_stored_internally += params.n_high_items #將後面的加到前面
    if strategy[1]: # 選擇記憶低價值
        items_stored_internally += params.n_low_items

    if items_stored_internally == 0:
        return 0.0
    # 這裡的邏輯是根據總內部項目數來決定準確率曲線
    elif items_stored_internally == 1:
        return params.accuracy_internal_1_item 
    elif items_stored_internally == 2:
        return params.accuracy_internal_2_items
    elif items_stored_internally == 3: 
        return params.accuracy_internal_3_items
    elif items_stored_internally == (params.n_high_items + params.n_low_items):
         return params.accuracy_internal_6_items

def simulate_trial(strategy: Strategy, params: ModelParameters) -> Tuple[float, int, int, int, int]:
    
    #模擬單次試驗並計算總獎勵和命中數。
    #返回: (總獎勵, 高價值正常命中數, 低價值正常命中數, 高價值意外命中數, 低價值意外命中數)
    
    # 初始化
    total_reward = 0.0
    high_value_hits_normal_count = 0
    low_value_hits_normal_count = 0
    high_value_hits_surprise_count = 0
    low_value_hits_surprise_count = 0
    
    # 獲取內部記憶的實際準確率，這取決於本次策略選擇後內部存儲的項目總數
    # 這個準確率是基於內存負荷的理論概率，用於隨機抽樣
    p_internal_actual = get_internal_memory_accuracy(strategy, params)

    ### 高價值項目
    p_H_internal_component = p_internal_actual if strategy[0] else 0.0
    p_H_offload_component = params.accuracy_offloaded if strategy[2] else 0.0

    for _ in range(params.n_high_items):
        # 模擬內部記憶回憶嘗試 (只有在策略允許時才進行)
        internal_recalled_H = False
        if strategy[0]: # 如果策略選擇了內部記憶高價值
             if np.random.rand() < p_H_internal_component:  #隨機表示模擬人類記憶的不確定性，準確率0.9會變成記住90%的東西，小於代表成功
                 internal_recalled_H = True

        # 模擬外部卸載回憶嘗試 (只有在策略允許時才進行)
        offload_recalled_H = False
        if strategy[2]: # 如果策略選擇了外部卸載高價值
            if np.random.rand() < p_H_offload_component:
                offload_recalled_H = True

        # 正常回憶成功 (內部或外部任一成功)
        item_recalled_H_normal = internal_recalled_H or offload_recalled_H
        # 意外測試回憶成功 (僅內部記憶成功)
        item_recalled_H_surprise = internal_recalled_H

        if item_recalled_H_normal:
            high_value_hits_normal_count += 1
            # 正常回憶成功獲得獎勵
            total_reward += params.value_high
            #獎勵已經被加過了
        if item_recalled_H_surprise:
             high_value_hits_surprise_count += 1

    # 計算高價值項目的卸載成本
    if strategy[2]: # 如果選擇卸載高價值項目
        total_reward -= params.n_high_items * params.cost_offloading

    ### 處理低價值項目 (邏輯同高價值項目)
    p_L_internal_component = p_internal_actual if strategy[1] else 0.0
    p_L_offload_component = params.accuracy_offloaded if strategy[3] else 0.0

    for _ in range(params.n_low_items):
        # 模擬內部記憶回憶嘗試 (只有在策略允許時才進行)
        internal_recalled_L = False
        if strategy[1]: # 如果策略選擇了內部記憶低價值
             if np.random.rand() < p_L_internal_component:
                 internal_recalled_L = True

        # 模擬外部卸載回憶嘗試 (只有在策略允許時才進行)
        offload_recalled_L = False
        if strategy[3]: # 如果策略選擇了外部卸載低價值
            if np.random.rand() < p_L_offload_component:
                offload_recalled_L = True

        # 正常回憶成功 (內部或外部任一成功)
        item_recalled_L_normal = internal_recalled_L or offload_recalled_L

        # 意外測試回憶成功 (僅內部記憶成功)
        item_recalled_L_surprise = internal_recalled_L

        if item_recalled_L_normal:
            low_value_hits_normal_count += 1
             # 正常回憶成功獲得獎勵
            total_reward += params.value_low
            
        if item_recalled_L_surprise:
             low_value_hits_surprise_count += 1

    # 計算低價值項目的卸載成本
    if strategy[3]: # 如果選擇卸載低價值項目
        total_reward -= params.n_low_items * params.cost_offloading
    return total_reward, high_value_hits_normal_count, low_value_hits_normal_count, high_value_hits_surprise_count, low_value_hits_surprise_count

def run_simulation(params, strategies) -> Dict[str, Any]:
    print(f"評估運行模擬 ({params.n_model_runs} runs, {params.n_episodes_per_strategy_eval} episodes per run)...")

    # 存儲所有 run 中，每個策略被選為最佳策略的次數
    overall_best_strategy_counts: Dict[Strategy, int] = {s: 0 for s in strategies}

    # 累計所有 run 中，最佳策略的經驗命中數和樣本數，用於計算最終平均經驗準確率
    overall_cumulative_hits_H_normal = 0
    overall_cumulative_hits_L_normal = 0
    overall_cumulative_hits_H_surprise = 0
    overall_cumulative_hits_L_surprise = 0

    # 累計成功完成模擬的 runs 數量 (避免除以零)
    successful_runs_count = 0

    # 遍歷所有的 runs
    for i_run in range(params.n_model_runs):
        if (i_run + 1) % (max(1, params.n_model_runs // 100)) == 0: # 調整打印頻率
            print(f"  運行中... {((i_run + 1) / params.n_model_runs) * 100:.0f}%")

        # 初始化本次 run 中每個策略的總獎勵、樣本計數和命中計數，專屬於本次 run，defaultdict 是 python 的標準庫，用於初始化字典
        total_reward_per_strategy_this_run: Dict[Strategy, float] = defaultdict(float)
        sample_count_per_strategy_this_run: Dict[Strategy, int] = defaultdict(int)
        total_hits_H_normal_per_strategy_this_run: Dict[Strategy, int] = defaultdict(int)
        total_hits_L_normal_per_strategy_this_run: Dict[Strategy, int] = defaultdict(int)
        total_hits_H_surprise_per_strategy_this_run: Dict[Strategy, int] = defaultdict(int)
        total_hits_L_surprise_per_strategy_this_run: Dict[Strategy, int] = defaultdict(int)

        # 獲取本次 run 中所有允許的策略列表 (這裡就是傳入的 strategies 列表)
        allowed_strategies_list_this_run = strategies

        # 在本次 run 中進行多次 episodes (試驗)
        for i_episode in range(params.n_episodes_per_strategy_eval):
            # 隨機選擇一個策略
            chosen_strategy = random.choice(allowed_strategies_list_this_run)
            chosen_strategy_tuple = tuple(chosen_strategy) # 確保是 tuple 作為字典鍵

            # 模擬一次試驗，，蒙地卡羅核心，跑50次
            reward, hits_H_norm, hits_L_norm, hits_H_surp, hits_L_surp = simulate_trial(chosen_strategy_tuple, params)

            # 將結果累加到本次 run 的統計中
            total_reward_per_strategy_this_run[chosen_strategy_tuple] += reward
            sample_count_per_strategy_this_run[chosen_strategy_tuple] += 1
            total_hits_H_normal_per_strategy_this_run[chosen_strategy_tuple] += hits_H_norm
            total_hits_L_normal_per_strategy_this_run[chosen_strategy_tuple] += hits_L_norm
            total_hits_H_surprise_per_strategy_this_run[chosen_strategy_tuple] += hits_H_surp
            total_hits_L_surprise_per_strategy_this_run[chosen_strategy_tuple] += hits_L_surp

        # 在本次 run 結束後，計算每個策略的平均獎勵
        mean_rewards_this_run: Dict[Strategy, float] = {} # 初始化字典，用於存儲每個策略的平均獎勵
        max_avg_reward_this_run = -float('inf') # 初始化最大平均獎勵為負無窮大，目的是為了找到最大值
        strategies_sampled_this_run = [s for s in strategies if sample_count_per_strategy_this_run[s] > 0]

        # if not strategies_sampled_this_run:
        #      # 如果本次 run 中沒有任何策略被 sampled，則沒有最佳策略，跳過本次 run 的統計
        #      continue

        for strategy in strategies_sampled_this_run:
             # 避免除以零，這是蒙地卡羅核心，價值函數
             if sample_count_per_strategy_this_run[strategy] > 0:
                mean_reward = total_reward_per_strategy_this_run[strategy] / sample_count_per_strategy_this_run[strategy]
                mean_rewards_this_run[strategy] = mean_reward # 將平均獎勵存儲到字典中
                if mean_reward > max_avg_reward_this_run:  # 如果平均獎勵大於最大平均獎勵，則更新最大平均獎勵
                    max_avg_reward_this_run = mean_reward

        # 找出本次 run 中平均獎勵最高的策略
        # 使用一個小範圍的容差來處理浮點數比較可能帶來的微小誤差
        #如果直接用==的話，會有誤差，所以需要用容差來處理
        tolerance = 1e-9 # 這裡定義了容差變數，重要!!!!!!!!!!!
        best_strategies_this_run = [
            s for s, avg_r in mean_rewards_this_run.items() if abs(avg_r - max_avg_reward_this_run) < tolerance # 注意這裡加上了 < tolerance
        ]
    

        # 如果有多個並列最佳，隨機選擇一個作為本次 run 的最佳策略
        best_strategy_for_this_run = random.choice(best_strategies_this_run)

        # 累加本次 run 的最佳策略統計
        overall_best_strategy_counts[best_strategy_for_this_run] += 1

        # 計算本次 run 的最佳策略的經驗命中率，並累加到總計中
        # 經驗命中率 = 總命中數 / (樣本數 * 項目數)
        # 確保分母不為零
        samples_best_strategy = sample_count_per_strategy_this_run[best_strategy_for_this_run]
        n_high = params.n_high_items
        n_low = params.n_low_items

        if samples_best_strategy > 0:  # 確保分母不為零，防錯機制
             overall_cumulative_hits_H_normal += total_hits_H_normal_per_strategy_this_run[best_strategy_for_this_run] / (samples_best_strategy * n_high) if n_high > 0 else 0.0
             overall_cumulative_hits_L_normal += total_hits_L_normal_per_strategy_this_run[best_strategy_for_this_run] / (samples_best_strategy * n_low) if n_low > 0 else 0.0
             overall_cumulative_hits_H_surprise += total_hits_H_surprise_per_strategy_this_run[best_strategy_for_this_run] / (samples_best_strategy * n_high) if n_high > 0 else 0.0
             overall_cumulative_hits_L_surprise += total_hits_L_surprise_per_strategy_this_run[best_strategy_for_this_run] / (samples_best_strategy * n_low) if n_low > 0 else 0.0
             successful_runs_count += 1


    # 在所有 runs 結束後，計算最終的平均結果(用每一種策略被選為最佳的次數除以總的runs數量得出百分比)-->策略被選為最佳的比例
    total_runs_considered = successful_runs_count # 使用實際成功計算的 runs 數量
    strategy_proportions: Dict[Strategy, float] = {
        s: overall_best_strategy_counts[s] / total_runs_considered if total_runs_considered > 0 else 0.0 for s in strategies
    }

    # 計算平均經驗命中率 (這是所有成功 run 的最佳策略的平均實際命中率)-->計算最佳策略的實際平均命中率
    mean_empirical_accuracy_H_normal = overall_cumulative_hits_H_normal / total_runs_considered if total_runs_considered > 0 else 0.0
    mean_empirical_accuracy_L_normal = overall_cumulative_hits_L_normal / total_runs_considered if total_runs_considered > 0 else 0.0
    mean_empirical_accuracy_H_surprise = overall_cumulative_hits_H_surprise / total_runs_considered if total_runs_considered > 0 else 0.0
    mean_empirical_accuracy_L_surprise = overall_cumulative_hits_L_surprise / total_runs_considered if total_runs_considered > 0 else 0.0


    # 每個策略被選為最佳的次數 (在所有 runs 中)
    print("\n最終策略選擇次數 (所有 Runs 中被選為最佳的次數):")
    sorted_overall_counts = sorted(overall_best_strategy_counts.items(), key=lambda item: item[1], reverse=True)
    for s, count in sorted_overall_counts:
         print(f"  策略 {s}: 被選為最佳次數 {count}")

    return {
        "strategy_proportions": strategy_proportions, # 每個策略在所有 run 中被選為最佳的比例
        "mean_accuracy_H": mean_empirical_accuracy_H_normal, # 所有 run 的最佳策略的平均經驗正常命中率
        "mean_accuracy_L": mean_empirical_accuracy_L_normal,
        "mean_accuracy_H_surprise": mean_empirical_accuracy_H_surprise, # 所有 run 的最佳策略的平均經驗意外命中率
        "mean_accuracy_L_surprise": mean_empirical_accuracy_L_surprise,
        "overall_best_strategy_counts": overall_best_strategy_counts # 用於調試
    }


# --- 模擬 Fig. 2: 不允許卸載 ---
def simulate_fig2(base_params: ModelParameters):
    print(f"\n開始模擬 Fig. 2 (不允許卸載)...")
    params_fig2 = ModelParameters(
        n_high_items=3, n_low_items=3,
        value_high=base_params.value_high, value_low=base_params.value_low,
        cost_offloading=base_params.cost_offloading,
        accuracy_internal_1_item=base_params.accuracy_internal_1_item,
        accuracy_internal_2_items=base_params.accuracy_internal_2_items,
        accuracy_internal_3_items=base_params.accuracy_internal_3_items,
        accuracy_internal_6_items=base_params.accuracy_internal_6_items,
        accuracy_offloaded=base_params.accuracy_offloaded,
        n_episodes_per_strategy_eval=base_params.n_episodes_per_strategy_eval,
        n_model_runs=base_params.n_model_runs
    )
    # Fig.2 不允許卸載
    strategies_fig2 = get_all_strategies(allow_offloading=False)
    results = run_simulation(params_fig2, strategies_fig2)
    
    # 匯總和打印結果
    prop_encode_low = sum(prop for s, prop in results["strategy_proportions"].items() if s[1])
    prop_encode_high = sum(prop for s, prop in results["strategy_proportions"].items() if s[0])
    acc_L_norm = results["mean_accuracy_L"]
    acc_H_norm = results["mean_accuracy_H"]

    print(f"Fig. 2  結果: ")
    print(f"  策略 - 編碼低價值比例: {prop_encode_low:.3f}")
    print(f"  策略 - 編碼高價值比例: {prop_encode_high:.3f}")
    print(f"  正常準確率 - 低價值: {acc_L_norm:.3f}")
    print(f"  正常準確率 - 高價值: {acc_H_norm:.3f}")

    # 繪圖
    fig, axs = plt.subplots(1, 2, figsize=(10, 4))
    fig.suptitle(f"Fig. 2 Simulation: No Offloading", fontsize=14)
    axs[0].bar(["Encode\nlow-value", "Encode\nhigh-value"], [prop_encode_low, prop_encode_high], color='cornflowerblue')
    axs[0].set_title("Strategy ")
    axs[0].set_ylabel("Proportion")
    axs[0].set_ylim(0, 1.05)
    axs[1].bar(["Low-value", "High-value"], [acc_L_norm, acc_H_norm], color='cornflowerblue')
    axs[1].set_title("Accuracy ")
    axs[1].set_ylabel("Mean Empirical Accuracy ")
    axs[1].set_ylim(0, 1.05)
    plt.tight_layout(rect=[0, 0, 1, 0.96])
    plt.show()

    return results

# --- 模擬 Fig. 3: 允許卸載 ---
def simulate_fig3(base_params: ModelParameters):
    print(f"\n開始模擬 Fig. 3 (允許卸載)...")
    params_fig3 = ModelParameters(
        n_high_items=3, n_low_items=3,
        value_high=base_params.value_high, value_low=base_params.value_low,
        cost_offloading=base_params.cost_offloading,
        accuracy_internal_1_item=base_params.accuracy_internal_1_item,
        accuracy_internal_2_items=base_params.accuracy_internal_2_items,
        accuracy_internal_3_items=base_params.accuracy_internal_3_items,
        accuracy_internal_6_items=base_params.accuracy_internal_6_items,
        accuracy_offloaded=base_params.accuracy_offloaded,
        n_episodes_per_strategy_eval=base_params.n_episodes_per_strategy_eval,
        n_model_runs=base_params.n_model_runs
    )
    # Fig.3 允許所有卸載
    strategies_fig3 = get_all_strategies(allow_offloading=True)
    
    # 使用 run_simulation 函數
    results = run_simulation(params_fig3, strategies_fig3)

    # 匯總和打印結果
    prop_encode_low = sum(prop for s, prop in results["strategy_proportions"].items() if s[1])
    prop_encode_high = sum(prop for s, prop in results["strategy_proportions"].items() if s[0])
    prop_offload_low = sum(prop for s, prop in results["strategy_proportions"].items() if s[3])
    prop_offload_high = sum(prop for s, prop in results["strategy_proportions"].items() if s[2])
    acc_L_norm = results["mean_accuracy_L"]
    acc_H_norm = results["mean_accuracy_H"]

    print(f"Fig. 3  結果: ")
    print(f"  策略 - 編碼低價值比例: {prop_encode_low:.3f}")
    print(f"  策略 - 編碼高價值比例: {prop_encode_high:.3f}")
    print(f"  策略 - 卸載低價值比例: {prop_offload_low:.3f}")
    print(f"  策略 - 卸載高價值比例: {prop_offload_high:.3f}")
    print(f"  正常準確率 - 低價值: {acc_L_norm:.3f}")
    print(f"  正常準確率 - 高價值: {acc_H_norm:.3f}")

    # 繪圖
    fig, axs = plt.subplots(1, 2, figsize=(12, 5))
    fig.suptitle(f"Fig. 3 Simulation: Offloading Allowed", fontsize=14)
    strategy_labels = ["Encode\nlow-value", "Encode\nhigh-value", "Offload\nlow-value", "Offload\nhigh-value"]
    strategy_values = [prop_encode_low, prop_encode_high, prop_offload_low, prop_offload_high]
    axs[0].bar(strategy_labels, strategy_values, color='cornflowerblue')
    axs[0].set_title("Strategy")
    axs[0].set_ylabel("Proportion")
    axs[0].set_ylim(0, 1.05)
    plt.setp(axs[0].get_xticklabels(), rotation=15, ha="right")
    axs[1].bar(["Low-value", "High-value"], [acc_L_norm, acc_H_norm], color='cornflowerblue')
    axs[1].set_title("Accuracy")
    axs[1].set_ylabel("Mean Empirical Accuracy")
    axs[1].set_ylim(0, 1.05)
    plt.tight_layout(rect=[0, 0, 1, 0.96]) # 調整圖片間距
    plt.show()

    return results

# --- 模擬 Fig. 4: 只允許卸載高價值項目 ---
def simulate_fig4(base_params: ModelParameters):
    print(f"\n開始模擬 Fig. 4 (只允許卸載高價值項目)...")
    params_fig4 = ModelParameters(
        n_high_items=3, n_low_items=3, 
        value_high=base_params.value_high, value_low=base_params.value_low,
        cost_offloading=base_params.cost_offloading,
        accuracy_internal_1_item=base_params.accuracy_internal_1_item,
        accuracy_internal_2_items=base_params.accuracy_internal_2_items,
        accuracy_internal_3_items=base_params.accuracy_internal_3_items,
        accuracy_internal_6_items=base_params.accuracy_internal_6_items,
        accuracy_offloaded=base_params.accuracy_offloaded,
        n_episodes_per_strategy_eval=base_params.n_episodes_per_strategy_eval,
        n_model_runs=base_params.n_model_runs
    )
    # 策略: 允許卸載，但僅限高價值 (即 offload_L 總是 False)
    strategies_fig4 = get_all_strategies(allow_offloading=True, offload_restriction="high_only")
    
    # 使用 run_simulation 函數
    results = run_simulation(params_fig4, strategies_fig4)

    # 匯總和打印結果
    prop_encode_low = sum(prop for s, prop in results["strategy_proportions"].items() if s[1])
    prop_encode_high = sum(prop for s, prop in results["strategy_proportions"].items() if s[0])
    prop_offload_low_check = sum(prop for s, prop in results["strategy_proportions"].items() if s[3])
    prop_offload_high = sum(prop for s, prop in results["strategy_proportions"].items() if s[2])
    acc_L_norm = results["mean_accuracy_L"]
    acc_H_norm = results["mean_accuracy_H"]
    
    print(f"Fig. 4  結果 (只允許卸載高價值): ")
    print(f"  策略 - 編碼低價值比例: {prop_encode_low:.3f}")
    print(f"  策略 - 編碼高價值比例: {prop_encode_high:.3f}")
    print(f"  策略 - 卸載低價值比例 (應為0): {prop_offload_low_check:.3f}")
    print(f"  策略 - 卸載高價值比例: {prop_offload_high:.3f}")
    print(f"  正常準確率 - 低價值: {acc_L_norm:.3f}")
    print(f"  正常準確率 - 高價值: {acc_H_norm:.3f}")
    
    # 繪圖
    fig, axs = plt.subplots(1, 2, figsize=(12, 5))
    fig.suptitle(f"Fig. 4 Simulation: Only High-Value Offloading Allowed", fontsize=14)

    strategy_labels = ["Encode\nlow-value", "Encode\nhigh-value", "Offload\nlow-value", "Offload\nhigh-value"]
    strategy_values = [prop_encode_low, prop_encode_high, prop_offload_low_check, prop_offload_high]
    axs[0].bar(strategy_labels, strategy_values, color='cornflowerblue')
    axs[0].set_title("Strategy")
    axs[0].set_ylabel("Proportion")
    axs[0].set_ylim(0, 1.05)
    plt.setp(axs[0].get_xticklabels(), rotation=15, ha="right")

    axs[1].bar(["Low-value", "High-value"], [acc_L_norm, acc_H_norm], color='cornflowerblue')
    axs[1].set_title("Accuracy")
    axs[1].set_ylabel("Mean Empirical Accuracy")
    axs[1].set_ylim(0, 1.05)
    
    plt.tight_layout(rect=[0, 0, 1, 0.96])
    plt.show()
    
    return results

# --- 新增: 模擬 Fig. 5: 意外記憶測試 ---
def simulate_fig5(base_params: ModelParameters):
    print(f"\n開始模擬 Fig. 5 (意外記憶測試)...")
    
    params_common = ModelParameters( 
        n_high_items=3, n_low_items=3,
        value_high=base_params.value_high, value_low=base_params.value_low,
        cost_offloading=base_params.cost_offloading,
        accuracy_internal_1_item=base_params.accuracy_internal_1_item,
        accuracy_internal_2_items=base_params.accuracy_internal_2_items,
        accuracy_internal_3_items=base_params.accuracy_internal_3_items,
        accuracy_internal_6_items=base_params.accuracy_internal_6_items,
        accuracy_offloaded=base_params.accuracy_offloaded,
        n_episodes_per_strategy_eval=base_params.n_episodes_per_strategy_eval,
        n_model_runs=base_params.n_model_runs
    )

    # 條件1: 不允許卸載
    print("  Fig. 5 條件1: 不允許卸載 (意外測試)")
    strategies_cond1 = get_all_strategies(allow_offloading=False)
    results_cond1 = run_simulation(params_common, strategies_cond1)

    # 條件2: 只允許卸載高價值項目
    print("  Fig. 5 條件2: 只允許卸載高價值 (意外測試)")
    strategies_cond2 = get_all_strategies(allow_offloading=True, offload_restriction="high_only")
    results_cond2 = run_simulation(params_common, strategies_cond2)

    # 匯總和打印結果
    acc_L_cond1 = results_cond1["mean_accuracy_L_surprise"]
    acc_H_cond1 = results_cond1["mean_accuracy_H_surprise"]
    acc_L_cond2 = results_cond2["mean_accuracy_L_surprise"]
    acc_H_cond2 = results_cond2["mean_accuracy_H_surprise"]

    print(f"    意外測試準確率 (R-style empirical, 不允許卸載) - 低價值: {acc_L_cond1:.3f}, 高價值: {acc_H_cond1:.3f}")
    print(f"    意外測試準確率 (R-style empirical, 只允許卸載高價值) - 低價值: {acc_L_cond2:.3f}, 高價值: {acc_H_cond2:.3f}")

    # 繪圖
    fig, axs = plt.subplots(1, 2, figsize=(10, 4), sharey=True)
    fig.suptitle(f"Fig. 5 Simulation: Surprise-Test Accuracy", fontsize=14)

    axs[0].bar(["Low-value", "High-value"], [acc_L_cond1, acc_H_cond1], color='cornflowerblue')
    axs[0].set_title("No Offloading Allowed")
    axs[0].set_ylabel("Mean Empirical Surprise-Test Accuracy")
    axs[0].set_ylim(0, 1.0)

    axs[1].bar(["Low-value", "High-value"], [acc_L_cond2, acc_H_cond2], color='lightcoral')
    axs[1].set_title("High-Value Offloading Allowed")
    axs[1].set_ylim(0, 1.0)
    
    plt.tight_layout(rect=[0, 0, 1, 0.94])
    plt.show()
    # 返回所有結果，方便比較
    return {"cond1": results_cond1, "cond2": results_cond2}

# --- 新增: 模擬 Fig. 6: 記憶負荷對卸載率的影響 ---
def simulate_fig6(base_params: ModelParameters):
    print(f"\n開始模擬 Fig. 6 (記憶負荷對卸載率的影響)...")

    # 條件1: 每種價值1個項目 (1 high, 1 low)
    print("  Fig. 6 條件1: 每種價值1個項目")
    params_load1 = ModelParameters(
        n_high_items=1, n_low_items=1, 
        value_high=base_params.value_high, value_low=base_params.value_low,
        cost_offloading=base_params.cost_offloading,
        accuracy_internal_1_item=base_params.accuracy_internal_1_item,
        accuracy_internal_2_items=base_params.accuracy_internal_2_items, # 1H+1L = 2 items load
        accuracy_internal_3_items=base_params.accuracy_internal_3_items,
        accuracy_internal_6_items=base_params.accuracy_internal_6_items,
        accuracy_offloaded=base_params.accuracy_offloaded,
        n_episodes_per_strategy_eval=base_params.n_episodes_per_strategy_eval,
        n_model_runs=base_params.n_model_runs
    )
    strategies_load1 = get_all_strategies(allow_offloading=True)
    results_load1 = run_simulation(params_load1, strategies_load1)

    # 匯總和打印結果
    offload_L_load1 = sum(prop for s, prop in results_load1["strategy_proportions"].items() if s[3])
    offload_H_load1 = sum(prop for s, prop in results_load1["strategy_proportions"].items() if s[2])
    print(f"    卸載率  - 低價值: {offload_L_load1:.3f}, 高價值: {offload_H_load1:.3f}")

    # 條件2: 每種價值3個項目 (標準情況)
    print("  Fig. 6 條件2: 每種價值3個項目 (標準負荷)")
    params_load3 = ModelParameters(
        n_high_items=3, n_low_items=3,
        value_high=base_params.value_high, value_low=base_params.value_low,
        cost_offloading=base_params.cost_offloading,
        accuracy_internal_1_item=base_params.accuracy_internal_1_item,
        accuracy_internal_2_items=base_params.accuracy_internal_2_items,
        accuracy_internal_3_items=base_params.accuracy_internal_3_items,
        accuracy_internal_6_items=base_params.accuracy_internal_6_items, # 3H+3L = 6 items load
        accuracy_offloaded=base_params.accuracy_offloaded,
        n_episodes_per_strategy_eval=base_params.n_episodes_per_strategy_eval,
        n_model_runs=base_params.n_model_runs
    )
    strategies_load3 = get_all_strategies(allow_offloading=True)
    results_load3 = run_simulation(params_load3, strategies_load3)

    # 匯總和打印結果
    offload_L_load3 = sum(prop for s, prop in results_load3["strategy_proportions"].items() if s[3])
    offload_H_load3 = sum(prop for s, prop in results_load3["strategy_proportions"].items() if s[2])
    print(f"    卸載率 (R-style empirical, 3H,3L) - 低價值: {offload_L_load3:.3f}, 高價值: {offload_H_load3:.3f}")

    # 繪圖
    fig, axs = plt.subplots(1, 2, figsize=(10, 4), sharey=True)
    fig.suptitle(f"Fig. 6 Simulation: Effect of Memory Load on Offloading Rate", fontsize=14)

    axs[0].bar(["Low-value", "High-value"], [offload_L_load1, offload_H_load1], color='cornflowerblue')
    axs[0].set_title("1 item at each value")
    axs[0].set_ylabel("Offloading rate")
    axs[0].set_ylim(0, 1.0)

    axs[1].bar(["Low-value", "High-value"], [offload_L_load3, offload_H_load3], color='lightcoral')
    axs[1].set_title("3 items at each value")
    axs[1].set_ylim(0, 1.0)
    
    plt.tight_layout(rect=[0, 0, 1, 0.94])
    plt.show()
    return {"load1": results_load1, "load3": results_load3}


# --- 模擬 Fig. 7: 卸載成本的影響 ---
def simulate_fig7(base_params: ModelParameters):
    print(f"\n開始模擬 Fig. 7 (卸載成本的影響)...")
    # 卸載成本從 0.0 到 2.0 分成 9 個點
    offloading_costs = np.linspace(0.0, 2.0, 9) 
    
    # 初始化結果存儲列表
    results_over_costs: Dict[str, List[Any]] = { # 使用 Any 來允許存儲不同類型的結果
        "costs": list(offloading_costs), 
        "memory_encoding_rate_low": [], "memory_encoding_rate_high": [],
        "offloading_rate_low": [], "offloading_rate_high": [],
        "mean_accuracy_H_normal": [], "mean_accuracy_L_normal": [], # 添加經驗準確率存儲
        "mean_accuracy_H_surprise": [], "mean_accuracy_L_surprise": [] # 添加經驗意外準確率存儲
    }
    
    # 所有策略 (允許所有卸載選項)
    strategies_fig7 = get_all_strategies(allow_offloading=True)

    # 針對每個卸載成本運行模擬
    for cost_idx, cost in enumerate(offloading_costs):
        print(f"  模擬卸載成本 ({cost_idx+1}/{len(offloading_costs)}): {cost:.2f}")
        
        # 為當前成本創建參數對象
        current_params = ModelParameters(
            n_high_items=3, n_low_items=3, 
            value_high=base_params.value_high, value_low=base_params.value_low,
            cost_offloading=cost, # 使用當前的卸載成本
            accuracy_internal_1_item=base_params.accuracy_internal_1_item,
            accuracy_internal_2_items=base_params.accuracy_internal_2_items,
            accuracy_internal_3_items=base_params.accuracy_internal_3_items,
            accuracy_internal_6_items=base_params.accuracy_internal_6_items,
            accuracy_offloaded=base_params.accuracy_offloaded,
            n_episodes_per_strategy_eval=base_params.n_episodes_per_strategy_eval, # 每個 run 的 episodes 數量
            n_model_runs=base_params.n_model_runs # 運行次數
        )
        
        # 使用 run_simulation 函數
        sim_results = run_simulation(current_params, strategies_fig7) # 移除 method 參數
        
        # 從結果中提取編碼率、卸載率和經驗準確率
        prop_encode_low = sum(prop for s, prop in sim_results["strategy_proportions"].items() if s[1])
        prop_encode_high = sum(prop for s, prop in sim_results["strategy_proportions"].items() if s[0])
        prop_offload_low = sum(prop for s, prop in sim_results["strategy_proportions"].items() if s[3])
        prop_offload_high = sum(prop for s, prop in sim_results["strategy_proportions"].items() if s[2])

        acc_H_norm = sim_results["mean_accuracy_H"] # 經驗正常準確率
        acc_L_norm = sim_results["mean_accuracy_L"] # 經驗正常準確率
        acc_H_surp = sim_results["mean_accuracy_H_surprise"] # 經驗意外準確率
        acc_L_surp = sim_results["mean_accuracy_L_surprise"] # 經驗意外準確率

        
        # 將結果添加到列表中
        results_over_costs["memory_encoding_rate_low"].append(prop_encode_low)
        results_over_costs["memory_encoding_rate_high"].append(prop_encode_high)
        results_over_costs["offloading_rate_low"].append(prop_offload_low)
        results_over_costs["offloading_rate_high"].append(prop_offload_high)
        results_over_costs["mean_accuracy_H_normal"].append(acc_H_norm)
        results_over_costs["mean_accuracy_L_normal"].append(acc_L_norm)
        results_over_costs["mean_accuracy_H_surprise"].append(acc_H_surp)
        results_over_costs["mean_accuracy_L_surprise"].append(acc_L_surp)

    print(f"Fig. 7 結果: ")
    for i, cost_val in enumerate(results_over_costs["costs"]):
        print(f"  成本={cost_val:.2f}: EncL={results_over_costs['memory_encoding_rate_low'][i]:.3f}, EncH={results_over_costs['memory_encoding_rate_high'][i]:.3f}, OffL={results_over_costs['offloading_rate_low'][i]:.3f}, OffH={results_over_costs['offloading_rate_high'][i]:.3f}, AccL_norm={results_over_costs['mean_accuracy_L_normal'][i]:.3f}, AccH_norm={results_over_costs['mean_accuracy_H_normal'][i]:.3f}, AccL_surp={results_over_costs['mean_accuracy_L_surprise'][i]:.3f}, AccH_surp={results_over_costs['mean_accuracy_H_surprise'][i]:.3f}")


    # 繪圖
    fig, axs = plt.subplots(1, 2, figsize=(12, 5))
    fig.suptitle(f"Fig. 7 Simulation: Effect of Offloading Cost", fontsize=14)
    axs[0].plot(results_over_costs["costs"], results_over_costs["memory_encoding_rate_low"], 'o-', label="Low-value", color='cornflowerblue')
    axs[0].plot(results_over_costs["costs"], results_over_costs["memory_encoding_rate_high"], 's-', label="High-value", color='brown')
    axs[0].set_title("Memory encoding")
    axs[0].set_xlabel("Cost of offloading")
    axs[0].set_ylabel("Memory encoding rate")
    axs[0].legend()
    axs[0].grid(True, linestyle='--', alpha=0.7)

    # 在右側子圖繪製卸載率和準確率
    # 移除 ax2 的創建和繪製準確率的部分
    # ax2 = axs[1].twinx() # 創建一個共用 x 軸的雙軸

    # 繪製卸載率 (使用左軸)
    axs[1].plot(results_over_costs["costs"], results_over_costs["offloading_rate_low"], 'o--', label="Offload Low", color='cornflowerblue')
    axs[1].plot(results_over_costs["costs"], results_over_costs["offloading_rate_high"], 's--', label="Offload High", color='brown')
    axs[1].set_ylabel("Offloading rate")
    axs[1].set_ylim(0, 1.05)


    axs[1].set_title("Offloading Rate") 
    axs[1].set_xlabel("Cost of offloading")
    # 合併圖例 - 移除合併，只保留 axs[1] 的圖例
    # lines, labels = axs[1].get_legend_handles_labels()
    # lines2, labels2 = ax2.get_legend_handles_labels()
    # ax2.legend(lines + lines2, labels + labels2, loc='best')
    axs[1].legend(loc='best') # 只顯示 axs[1] 的圖例


    axs[1].grid(True, linestyle='--', alpha=0.7) # 網格線
    plt.tight_layout(rect=[0, 0, 1, 0.96])
    plt.show()
    return results_over_costs

# --- Fig. 8 專用的 simulate_trial 函數 ---
def simulate_trial_fig8(strategy: StrategyFig8, params: ModelParameters, internal_accuracy_scalar: float) -> Tuple[float, bool, bool]:
    """
    模擬單次試驗並計算總獎勵和命中數。(適用於 Fig. 8)
    Fig. 8 只有一個項目。
    返回: (總獎勵, 正常命中, 意外命中)
    """
    encode_strategy = strategy[0]
    offload_strategy = strategy[1]

    total_reward = 0.0
    item_recalled_normal = False
    item_recalled_surprise = False

    # 計算卸載成本 (只應用一次)
    if offload_strategy:
        total_reward -= params.cost_offloading

    # 模擬內部記憶回憶嘗試
    internal_recalled = False
    if encode_strategy: # 如果策略選擇了內部記憶這個項目
        # Fig. 8 的內部記憶準確率是單個 scalar 值，且只記一個項目時 memory load 是 1
        # 在 R 程式碼中，它是直接使用傳入的 remember_internal scalar
        if np.random.rand() < internal_accuracy_scalar:
            internal_recalled = True
            item_recalled_surprise = True # 意外測試只考慮內部記憶

    # 模擬外部卸載回憶嘗試
    offload_recalled = False
    if offload_strategy: # 如果策略選擇了外部卸載這個項目
        if np.random.rand() < params.accuracy_offloaded:
            offload_recalled = True

    # 正常回憶成功 (內部或外部任一成功)
    if internal_recalled or offload_recalled:
        item_recalled_normal = True
        # 正常回憶成功獲得獎勵
        total_reward += params.value_high # Fig 8 initialize sets reward_value=8

    return total_reward, item_recalled_normal, item_recalled_surprise

# --- Fig. 8 專用的 run_simulation 函數 ---
def run_simulation_fig8(params: ModelParameters, internal_accuracy_scalar: float) -> Dict[str, float]:
    """
    運行 Fig. 8 R 風格的蒙地卡羅模擬評估策略 (單一項目)
    返回: { 'prop_encode': 選擇包含encode策略的比例, 'prop_offload': 選擇包含offload策略的比例 }
    """
    # Fig. 8 的策略只有 (encode, offload)
    strategies_fig8 = get_strategies_fig8()
    
    # 存儲所有 run 中，每個策略被選為最佳策略的次數
    # Fig. 8 只關心策略中是否包含 encode 或 offload 決策被選為最佳的比例
    # 初始化累計計數，長度為 2，索引 0 代表 encode，索引 1 代表 offload
    overall_best_strategy_decision_counts = [0, 0] 

    # 累計成功完成模擬的 runs 數量 (避免除以零)
    successful_runs_count = 0

    # 遍歷所有的 runs
    for i_run in range(params.n_model_runs):
        # 可以在這裡選擇打印進度，但 Fig 8 的 Runs 較少，可能不需要太頻繁
        # if (i_run + 1) % (max(1, params.n_model_runs // 100)) == 0:
        #     print(f"  運行中... Fig 8 Run {i_run + 1}/{params.n_model_runs}")


        # 初始化本次 run 中每個策略的總獎勵和樣本計數
        total_reward_per_strategy_this_run: Dict[StrategyFig8, float] = defaultdict(float)
        sample_count_per_strategy_this_run: Dict[StrategyFig8, int] = defaultdict(int)


        # 在本次 run 中進行多次 episodes (試驗)
        for i_episode in range(params.n_episodes_per_strategy_eval):
            # 隨機選擇一個策略
            chosen_strategy = random.choice(strategies_fig8)
            chosen_strategy_tuple = tuple(chosen_strategy) # 確保是 tuple 作為字典鍵

            # 模擬一次試驗，注意這裡調用的是 simulate_trial_fig8
            reward, _, _ = simulate_trial_fig8(chosen_strategy_tuple, params, internal_accuracy_scalar)

            # 將結果累加到本次 run 的統計中
            total_reward_per_strategy_this_run[chosen_strategy_tuple] += reward
            sample_count_per_strategy_this_run[chosen_strategy_tuple] += 1


        # 在本次 run 結束後，計算每個策略的平均獎勵
        mean_rewards_this_run: Dict[StrategyFig8, float] = {}
        max_avg_reward_this_run = -float('inf')
        strategies_sampled_this_run = [s for s in strategies_fig8 if sample_count_per_strategy_this_run[s] > 0]

        if not strategies_sampled_this_run:
             # 如果本次 run 中沒有任何策略被 sampled，則沒有最佳策略，跳過本次 run 的統計
             continue

        for strategy in strategies_sampled_this_run:
             if sample_count_per_strategy_this_run[strategy] > 0:
                mean_reward = total_reward_per_strategy_this_run[strategy] / sample_count_per_strategy_this_run[strategy]
                mean_rewards_this_run[strategy] = mean_reward
                if mean_reward > max_avg_reward_this_run:
                    max_avg_reward_this_run = mean_reward

        # 找出本次 run 中平均獎勵最高的策略
        tolerance = 1e-9
        best_strategies_this_run = [
            s for s, avg_r in mean_rewards_this_run.items() if abs(avg_r - max_avg_reward_this_run) < tolerance
        ]
    
        # 如果有多個並列最佳，隨機選擇一個作為本次 run 的最佳策略
        best_strategy_for_this_run = random.choice(best_strategies_this_run)

        # 累加本次 run 的最佳策略中包含 encode 或 offload 的計數
        # 在 R 程式碼中，這是通過將 best_policy 的二進位位加到 best_policy 向量中實現的
        # best_policy 向量長度為 2，索引 0 對應 encode，索引 1 對應 offload
        if best_strategy_for_this_run[0]: # if encode is True
            overall_best_strategy_decision_counts[0] += 1
        if best_strategy_for_this_run[1]: # if offload is True
            overall_best_strategy_decision_counts[1] += 1

        successful_runs_count += 1


    # 在所有 runs 結束後，計算包含 encode/offload 決策的比例
    total_runs_considered = successful_runs_count
    prop_encode = overall_best_strategy_decision_counts[0] / total_runs_considered if total_runs_considered > 0 else 0.0
    prop_offload = overall_best_strategy_decision_counts[1] / total_runs_considered if total_runs_considered > 0 else 0.0

    # Fig. 8 只需要返回 offload 比例，以及 encode 比例 (雖然圖上沒畫 encode)
    return {
        "prop_encode": prop_encode,
        "prop_offload": prop_offload
    }

# --- 新增: 模擬 Fig. 8: 內部記憶準確率對卸載率的影響 ---
def simulate_fig8(base_params: ModelParameters):
    print(f"\n開始模擬 Fig. 8 (內部記憶準確率的影響)...")

    # Fig. 8 變化的是內部記憶準確率 (internal accuracy)
    # R 程式碼中測試了從 0.55 到 0.95 共 9 個點
    internal_accuracies_to_test = np.linspace(0.55, 0.95, 9) 
    
    # 初始化結果存儲列表
    low_cost_offload_rates = []
    high_cost_offload_rates = []
    internal_acc_values_recorded = [] # 記錄實際使用的 internal accuracy 值

    # 遍歷不同的內部記憶準確率
    for int_acc in internal_accuracies_to_test:
        print(f"  模擬內部記憶準確率: {int_acc:.2f}")
        
        
        # Fig 8 模擬的是單一項目，價值為 base_params.value_high (8.0)
        # n_high_items=1, n_low_items=0
        # 內部記憶準確率由當前的 int_acc 值決定
        params_low_cost = ModelParameters(
            n_high_items=1, n_low_items=0, # 單一項目
            value_high=base_params.value_high, value_low=0, # 使用高價值，低價值不相關
            cost_offloading=1.0, # 低卸載成本
            accuracy_offloaded=base_params.accuracy_offloaded,
            n_episodes_per_strategy_eval=base_params.n_episodes_per_strategy_eval,
            n_model_runs=base_params.n_model_runs # 運行次數，R 程式碼 Fig 8 使用 1000 runs, Python 標準使用 10000
        )
        # 運行低卸載成本的模擬 (使用 Fig 8 專用的 run_simulation_fig8)
        # 注意將當前的 int_acc 傳入
        results_low_cost = run_simulation_fig8(params_low_cost, int_acc)
        low_cost_offload_rates.append(results_low_cost['prop_offload'])


        # 為高卸載成本條件創建參數對象
        params_high_cost = ModelParameters(
             n_high_items=1, n_low_items=0, # 單一項目
             value_high=base_params.value_high, value_low=0,
             cost_offloading=2.0, # 高卸載成本
             accuracy_offloaded=base_params.accuracy_offloaded,
             n_episodes_per_strategy_eval=base_params.n_episodes_per_strategy_eval,
             n_model_runs=base_params.n_model_runs # 運行次數，R 程式碼 Fig 8 使用 1000 runs, Python 標準使用 10000
        )
        
        # 運行高卸載成本的模擬
        results_high_cost = run_simulation_fig8(params_high_cost, int_acc)
        high_cost_offload_rates.append(results_high_cost['prop_offload'])
        
        # 記錄當前使用的 internal accuracy 值
        internal_acc_values_recorded.append(int_acc)

    # 計算成本敏感度 (低成本卸載率 - 高成本卸載率)
    cost_sensitivity = np.array(low_cost_offload_rates) - np.array(high_cost_offload_rates)

    print(f"Fig. 8  結果: ")
    for i, int_acc_val in enumerate(internal_acc_values_recorded):
        print(f"  內部準確率={int_acc_val:.2f}: 低成本卸載率={low_cost_offload_rates[i]:.3f}, 高成本卸載率={high_cost_offload_rates[i]:.3f}, 成本敏感度={cost_sensitivity[i]:.3f}")
        # 繪圖
    fig, axs = plt.subplots(1, 2, figsize=(12, 5))
    fig.suptitle(f"Fig. 8 Simulation: Effect of Internal Accuracy", fontsize=14)

    # 左側子圖：卸載率 vs 內部記憶準確率 (低/高成本)
    axs[0].plot(internal_acc_values_recorded, low_cost_offload_rates, 'o-', label="Low Cost (Cost=1)", color='blue')
    axs[0].plot(internal_acc_values_recorded, high_cost_offload_rates, 's-', label="High Cost (Cost=2)", color='darkred')
    axs[0].set_title("Offloading Rate")
    axs[0].set_xlabel("Internal Accuracy")
    axs[0].set_ylabel("Offloading Rate")
    axs[0].set_ylim(0, 1.05)
    axs[0].legend()
    axs[0].grid(True, linestyle='--', alpha=0.7)

    # 右側子圖：成本敏感度 vs 內部記憶準確率
    axs[1].plot(internal_acc_values_recorded, cost_sensitivity, '^-', label="Cost Sensitivity", color='green')
    axs[1].set_title("Cost Sensitivity")
    axs[1].set_xlabel("Internal Accuracy")
    axs[1].set_ylabel("Cost Sensitivity")
    axs[1].set_ylim(0, 0.55) # 根據 R 圖調整 Y 軸範圍
    axs[1].legend()
    axs[1].grid(True, linestyle='--', alpha=0.7)

    plt.tight_layout(rect=[0, 0, 1, 0.96])
    plt.show()
    
    return {"internal_accuracies": internal_acc_values_recorded, "low_cost_offload": low_cost_offload_rates, "high_cost_offload": high_cost_offload_rates, "cost_sensitivity": list(cost_sensitivity)}

# --- 主執行區塊 ---
if __name__ == '__main__':
    # 設定基礎參數
    # 為了演示，n_model_runs 和 n_episodes_per_strategy_eval 設為較小值。論文中 n_model_runs 為 1,000,000， n_episodes_per_strategy_eval 為 50。
    shared_base_params = ModelParameters(
        n_model_runs=10000, 
        n_episodes_per_strategy_eval=50 
    )
    print(f"--- 執行所有圖表模擬 ---")
    
    # 執行所有模擬圖表 
    results_fig2 = simulate_fig2(shared_base_params)
    results_fig3 = simulate_fig3(shared_base_params)
    results_fig4 = simulate_fig4(shared_base_params)

    # 比較低價值準確率 
    if results_fig2 and results_fig4:
        # 使用返回結果中的平均經驗準確率進行比較
        print(f"\n  比較低價值準確率 : Fig.2 (不卸載)={results_fig2['mean_accuracy_L']:.3f} vs Fig.4 (僅卸載高價值)={results_fig4['mean_accuracy_L']:.3f}")
    results_fig5 = simulate_fig5(shared_base_params)
    results_fig6 = simulate_fig6(shared_base_params)
    results_fig7 = simulate_fig7(shared_base_params)
    results_fig8 = simulate_fig8(shared_base_params)

    print(f"\n所有模擬完成！")

   
