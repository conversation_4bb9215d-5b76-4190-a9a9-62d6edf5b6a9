import numpy as np
import matplotlib.pyplot as plt
from matplotlib.animation import FuncAnimation 


fig = plt.figure(figsize=(5, 5))
ax = plt.gca() # get current axis
ax.set_xlim(0,3)
ax.set_ylim(0,3)

def ini_maze():
    plt.plot([1,1],[0,1],color = "red",linewidth = 2) # 1,0 to 1,1
    plt.plot([1,2],[2,2],color = "red",linewidth = 2) # 1,1 to 2,1
    plt.plot([2,2],[2,1],color = "red",linewidth = 2)
    plt.plot([2,3],[1,1],color = "red",linewidth = 2)

    plt.text(0.5,2.5,"S0",fontsize = 14, ha = "center", va = "center")
    plt.text(0.5, 2.3, "START", fontsize=10, ha="center", va="center")
    plt.text(1.5,2.5,"S1",fontsize = 14, ha = "center", va = "center")
    plt.text(2.5,2.5,"S2",fontsize = 14, ha = "center", va = "center")
    plt.text(0.5,1.5,"S3",fontsize = 14, ha = "center", va = "center")
    plt.text(1.5,1.5,"S4",fontsize = 14, ha = "center", va = "center")
    plt.text(2.5,1.5,"S5",fontsize = 14, ha = "center", va = "center")
    plt.text(0.5,0.5,"S6",fontsize = 14, ha = "center", va = "center")
    plt.text(1.5,0.5,"S7",fontsize = 14, ha = "center", va = "center")
    plt.text(2.5,0.5,"S8",fontsize = 14, ha = "center", va = "center")
    plt.text(2.5, 0.7, "FINISH", fontsize=10, ha="center", va="center")

ini_maze()
line,=ax.plot([0.5],[2.5],marker="o", markersize=50, color="lightblue", linewidth=2) 
# plt.show()

thete_0 = np.array([[np.nan,1,1,np.nan],
                    [np.nan,1,np.nan,1],
                    [np.nan,np.nan,1,1],
                    [1,1,1,np.nan],
                    [np.nan,np.nan,1,1],
                    [1,np.nan,np.nan,np.nan],
                    [1,np.nan,np.nan,np.nan],
                    [1,1,np.nan,np.nan]]) # 根據迷宮形狀定義哪些方向可以走

def softmax(theta):
    beta = 1.0
    [m,n]= theta.shape
    pi = np.zeros((m,n))
    exp_theta = np.exp(theta*beta)
    for i in range(0,m):
        sum_exp = np.nansum(exp_theta[i,:])
        pi[i,:] = exp_theta[i,:] / np.nansum(exp_theta[i,:])
        pi = np.nan_to_num(pi)
    return pi
# print(softmax(thete_0))



direction = np.array(["up","down","left","right"])
def get_action(s,pi):
    next_action = np.random.choice(direction, p=pi[s,:])
    if next_action == "up":
        action = 0
        s_next = s - 3
    elif next_action == "down":
        action = 1
        s_next = s + 3
    elif next_action == "left":
        action = 2
        s_next = s - 1
    elif next_action == "right":
        action = 3
        s_next = s + 1
    return [action,s_next]


def get_action_next_s(pi,s):
    next_direction = np.random.choice(direction, p=pi[s,:])
    if next_direction == "up":
        action = 0
        s_next = s - 3
    elif next_direction == "down":
        action = 2
        s_next = s + 3
    elif next_direction == "left":
        action = 3
        s_next = s - 1
    elif next_direction == "right":
        action = 1
        s_next = s + 1
    return [s_next,action]


def run_maze(pi):
    s = 0
    history_s = [0]
    history_a = [np.nan]
    while s != 8:
        [next_s,action]= get_action_next_s(pi,s)
        history_a[-1] = action
        history_s.append([next_s,np.nan])
        history_a.append(np.nan)
        s = next_s
    return [history_s,history_a]


state_history = run_maze(thete_0)


def update(i):
    state = state_history[i]
    x = (state%3) + 0.5  # x coordinate of the center of the grid
    y = 2.5 - int(state/3)  # y coordinate of the center of the grid
    plt.cla()
    ini_maze()
    ax.plot(x, y, marker="o", markersize=50, color="lightblue", linewidth=2)  # plot the current state
    anim = FuncAnimation(fig, update, frames=len(state_history), interval=200, repeat=False)
    plt.show()


def update_history(theta,pi,history):
    eta = 0.1
    T=len(history)
    [m,n]=theta.shape
    delta_theta = theta.copy()
    for i in range(0,m):
        SA_i = [SA for SA in history if SA[0] == i]
        N_i=  len(SA_i)
        for j in range(n):
            if not np.isnan(theta[i,j]):
                SA_ij = [SA for SA in history if SA[0] == i and SA[1] == j]
                N_ij = len(SA_ij)
                delta_theta[i,j] = (N_ij - pi[i,j]*N_i) / T
    new_theta = theta + eta * delta_theta
    return new_theta

theta = update_history(thete_0,pi_0,state_history)
print(theta)
