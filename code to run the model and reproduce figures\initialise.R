###########
#constants#
###########

LOWVAL=1  
HIGHVAL=2

ENCODE_LOWVAL=1
ENCODE_HIGHVAL=2
OFFLOAD_LOWVAL=3
OFFLOAD_HIGHVAL=4

############
#parameters#
############

runs=1000  #how many times to run the simulation
episodes=50 #how many episodes to simulate each time
reward_values=integer(2)
reward_values[LOWVAL]=2
reward_values[HIGHVAL]=8
nItems=3 #how many items of each colour?
offload_cost=1 #how many points are deducted for setting a reminder
remember_external=0.98 #probability of remembering if using a reminder
remember_internal=integer(nItems*2) #probability of remembering at each memory load (if both high&low are encoded, this is nItems*2)
remember_internal[1]=.95
remember_internal[2]=.925
remember_internal[3]=.9 #probability of remembering with 3 items (low- or high-value encoded but not both)
remember_internal[6]=.75 #probability of remembering with 6 items (all items encoded)
internal_memory_reduction=0 #how much to reduce internal memory performance, e.g. due to interruption
offloading_allowed=integer(2) #are participants allowed to offload?
offloading_allowed[LOWVAL]=TRUE
offloading_allowed[HIGHVAL]=TRUE

remember_internal = remember_internal - internal_memory_reduction