############
#initialise#
############

source('initialise.R')

#these are the cost values we will test
costs=seq(0,2,length.out=21)

#initialise variable to store output from the simulations
enc_lowval=NULL
enc_hival=NULL
off_lowval=NULL
off_hival=NULL

#####################
#run all simulations#
#####################

for (offload_cost in costs) {
  #display the current offload cost, to check progress
  print(offload_cost)

  #hit rates, stored separately for 1. low-value, 2. high-value
  hits=integer(2) 
  surprise_hits=integer(2) 
  
  source('run_simulation.R')
  
  best_policy=best_policy/runs
  
  #store output of simulation
  enc_lowval=c(enc_lowval,best_policy[ENCODE_LOWVAL])
  enc_hival=c(enc_hival,best_policy[ENCODE_HIGHVAL])
  off_lowval=c(off_lowval,best_policy[OFFLOAD_LOWVAL])
  off_hival=c(off_hival,best_policy[OFFLOAD_HIGHVAL])
}

##############
#plot results#
##############

library(ggplot2)
library(patchwork)

plot_data = data.frame(costs=costs, enc_lowval=enc_lowval, enc_hival=enc_hival, off_lowval=off_lowval, off_hival=off_hival)

leftplot = ggplot(plot_data, aes(x=costs)) +
  geom_line(aes(y = enc_lowval), color = "blue") +
  geom_line(aes(y = enc_hival), color="darkred") +
  ggtitle("Memory encoding") +
  scale_x_continuous(name="Cost of offloading") +
  scale_y_continuous(name="Memory encoding rate") +
  theme(panel.border = element_blank(),  
        # Remove panel grid lines
        panel.grid.major = element_blank(),
        panel.grid.minor = element_blank(),
        # Remove panel background
        panel.background = element_rect(fill = "white", colour = "black", size = 1, linetype = "solid"),
        # Style text
        text = element_text(size = 14),
        plot.title = element_text(hjust = 0.5))

rightplot = ggplot() +
  geom_line(data=plot_data,aes(y=off_lowval, x=costs, colour = "Low-value")) +
  geom_line(data=plot_data,aes(y=off_hival, x=costs, colour = "High-value")) +
  scale_color_manual(name = "", values = c("Low-value" = "blue", "High-value" = "darkred")) +
  ggtitle("Offloading") +
  scale_x_continuous(name="Cost of offloading") +
  scale_y_continuous(name="Offloading rate") +
  scale_fill_discrete(name="abc",
                      breaks=c("off_lowval", "off_hival"),
                      labels=c("Low-value", "High-value")) +
  theme(panel.border = element_blank(),  
        # Remove panel grid lines
        panel.grid.major = element_blank(),
        panel.grid.minor = element_blank(),
        # Remove panel background
        panel.background = element_rect(fill = "white", colour = "black", size = 1, linetype = "solid"),
        # legend
        legend.text = element_text(size = 14),
        legend.key = element_rect(colour = "transparent", fill = "white"),
        legend.position = "right",
        # Style text
        text = element_text(size = 14),
        plot.title = element_text(hjust = 0.5))

graph = leftplot + rightplot

pdf(file="fig.pdf", width = 10, height = 4)

print(graph)
dev.off()
