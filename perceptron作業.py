import random  
import numpy as np  
import matplotlib.pyplot as plt  


letter_A = np.array([[0, 0, 0, 0, 1, 1, 0, 0, 0, 0],
                      [0, 0, 0, 1, 1, 1, 1, 0, 0, 0],
                      [0, 0, 0, 1, 0, 0, 1, 0, 0, 0],
                      [0, 0, 1, 1, 0, 0, 1, 1, 0, 0],
                      [0, 0, 1, 1, 1, 1, 1, 1, 0, 0],
                      [0, 0, 1, 1, 1, 1, 1, 1, 0, 0],
                      [0, 1, 1, 1, 0, 0, 1, 1, 1, 0],
                      [0, 1, 1, 0, 0, 0, 0, 1, 1, 0],
                      [1, 1, 0, 0, 0, 0, 0, 0, 1, 1],
                      [1, 1, 0, 0, 0, 0, 0, 0, 1, 1]])
vector_A = np.ndarray.flatten(letter_A)  # 將字母A的矩陣展平為向量


letter_B = np.array([[1, 1, 1, 1, 1, 1, 0, 0, 0, 0],
                      [1, 1, 1, 1, 1, 1, 1, 0, 0, 0],
                      [1, 1, 0, 0, 0, 0, 1, 1, 0, 0],
                      [1, 1, 0, 0, 0, 0, 1, 1, 0, 0],
                      [1, 1, 1, 1, 1, 1, 1, 1, 0, 0],
                      [1, 1, 1, 1, 1, 1, 1, 0, 0, 0],
                      [1, 1, 0, 0, 0, 0, 1, 1, 0, 0],
                      [1, 1, 0, 0, 0, 0, 0, 1, 1, 0],
                      [1, 1, 0, 0, 0, 0, 0, 1, 1, 0],
                      [1, 1, 1, 1, 1, 1, 1, 1, 0, 0]])
vector_B = np.ndarray.flatten(letter_B)  # 將字母B的矩陣展平為向量

letter_C = np.array([[0, 0, 1, 1, 1, 1, 1, 1, 1, 0],
                      [0, 1, 1, 1, 0, 0, 0, 0, 0, 0],
                      [0, 1, 1, 0, 0, 0, 0, 0, 0, 0],
                      [0, 1, 1, 0, 0, 0, 0, 0, 0, 0],
                      [0, 1, 1, 0, 0, 0, 0, 0, 0, 0],
                      [0, 1, 1, 0, 0, 0, 0, 0, 0, 0],
                      [0, 1, 1, 0, 0, 0, 0, 0, 0, 0],
                      [0, 1, 1, 1, 0, 0, 0, 0, 0, 0],
                      [0, 0, 1, 1, 1, 1, 1, 1, 1, 0],
                      [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]])
vector_C = np.ndarray.flatten(letter_C)  # 將字母C的矩陣展平為向量

letter_D = np.array([[1, 1, 1, 1, 1, 1, 1, 0, 0, 0],
                      [1, 1, 1, 1, 1, 1, 1, 1, 0, 0],
                      [1, 1, 0, 0, 0, 0, 1, 1, 0, 0],
                      [1, 1, 0, 0, 0, 0, 0, 1, 1, 0],
                      [1, 1, 0, 0, 0, 0, 0, 1, 1, 0],
                      [1, 1, 0, 0, 0, 0, 0, 1, 1, 0],
                      [1, 1, 0, 0, 0, 0, 0, 1, 1, 0],
                      [1, 1, 0, 0, 0, 0, 1, 1, 0, 0],
                      [1, 1, 1, 1, 1, 1, 1, 1, 0, 0],
                      [1, 1, 1, 1, 1, 1, 1, 0, 0, 0]])
vector_D = np.ndarray.flatten(letter_D)  # 將字母D的矩陣展平為向量

letter_E = np.array([[1, 1, 1, 1, 1, 1, 1, 1, 1, 0],
                      [1, 1, 0, 0, 0, 0, 0, 0, 0, 0],
                      [1, 1, 0, 0, 0, 0, 0, 0, 0, 0],
                      [1, 1, 0, 0, 0, 0, 0, 0, 0, 0],
                      [1, 1, 1, 1, 1, 1, 1, 0, 0, 0],
                      [1, 1, 1, 1, 1, 1, 1, 0, 0, 0],
                      [1, 1, 0, 0, 0, 0, 0, 0, 0, 0],
                      [1, 1, 0, 0, 0, 0, 0, 0, 0, 0],
                      [1, 1, 0, 0, 0, 0, 0, 0, 0, 0],
                      [1, 1, 1, 1, 1, 1, 1, 1, 1, 0]])
vector_E = np.ndarray.flatten(letter_E)  # 將字母E的矩陣展平為向量

letter_F = np.array([[1, 1, 1, 1, 1, 1, 1, 1, 1, 0],
                      [1, 1, 0, 0, 0, 0, 0, 0, 0, 0],
                      [1, 1, 0, 0, 0, 0, 0, 0, 0, 0],
                      [1, 1, 0, 0, 0, 0, 0, 0, 0, 0],
                      [1, 1, 1, 1, 1, 1, 1, 0, 0, 0],
                      [1, 1, 1, 1, 1, 1, 1, 0, 0, 0],
                      [1, 1, 0, 0, 0, 0, 0, 0, 0, 0],
                      [1, 1, 0, 0, 0, 0, 0, 0, 0, 0],
                      [1, 1, 0, 0, 0, 0, 0, 0, 0, 0],
                      [1, 1, 0, 0, 0, 0, 0, 0, 0, 0]])
vector_F = np.ndarray.flatten(letter_F)  # 將字母F的矩陣展平為向量

letter_G = np.array([[0, 0, 1, 1, 1, 1, 1, 1, 0, 0],
                      [0, 1, 1, 0, 0, 0, 0, 0, 1, 0],
                      [1, 1, 0, 0, 0, 0, 0, 0, 0, 0],
                      [1, 1, 0, 0, 0, 0, 0, 0, 0, 0],
                      [1, 1, 0, 0, 0, 1, 1, 1, 1, 0],
                      [1, 1, 0, 0, 0, 1, 1, 1, 1, 0],
                      [1, 1, 0, 0, 0, 0, 0, 1, 1, 0],
                      [1, 1, 0, 0, 0, 0, 0, 1, 1, 0],
                      [0, 1, 1, 0, 0, 0, 1, 1, 0, 0],
                      [0, 0, 1, 1, 1, 1, 1, 0, 0, 0]])
vector_G = np.ndarray.flatten(letter_G)  # 將字母G的矩陣展平為向量

letter_H = np.array([[1, 1, 0, 0, 0, 0, 0, 0, 1, 1],
                      [1, 1, 0, 0, 0, 0, 0, 0, 1, 1],
                      [1, 1, 0, 0, 0, 0, 0, 0, 1, 1],
                      [1, 1, 0, 0, 0, 0, 0, 0, 1, 1],
                      [1, 1, 1, 1, 1, 1, 1, 1, 1, 1],
                      [1, 1, 1, 1, 1, 1, 1, 1, 1, 1],
                      [1, 1, 0, 0, 0, 0, 0, 0, 1, 1],
                      [1, 1, 0, 0, 0, 0, 0, 0, 1, 1],
                      [1, 1, 0, 0, 0, 0, 0, 0, 1, 1],
                      [1, 1, 0, 0, 0, 0, 0, 0, 1, 1]])
vector_H = np.ndarray.flatten(letter_H)  # 將字母H的矩陣展平為向量

letter_I = np.array([[0, 1, 1, 1, 1, 1, 1, 1, 1, 0],
                      [0, 0, 0, 0, 1, 1, 0, 0, 0, 0],
                      [0, 0, 0, 0, 1, 1, 0, 0, 0, 0],
                      [0, 0, 0, 0, 1, 1, 0, 0, 0, 0],
                      [0, 0, 0, 0, 1, 1, 0, 0, 0, 0],
                      [0, 0, 0, 0, 1, 1, 0, 0, 0, 0],
                      [0, 0, 0, 0, 1, 1, 0, 0, 0, 0],
                      [0, 0, 0, 0, 1, 1, 0, 0, 0, 0],
                      [0, 0, 0, 0, 1, 1, 0, 0, 0, 0],
                      [0, 1, 1, 1, 1, 1, 1, 1, 1, 0]])
vector_I = np.ndarray.flatten(letter_I)  # 將字母I的矩陣展平為向量

letter_J = np.array([[0, 0, 0, 0, 1, 1, 1, 1, 1, 0],
                      [0, 0, 0, 0, 1, 1, 1, 1, 1, 0],
                      [0, 0, 0, 0, 0, 0, 0, 1, 1, 0],
                      [0, 0, 0, 0, 0, 0, 0, 1, 1, 0],
                      [0, 0, 0, 0, 0, 0, 0, 1, 1, 0],
                      [0, 0, 0, 0, 0, 0, 0, 1, 1, 0],
                      [1, 1, 0, 0, 0, 0, 0, 1, 1, 0],
                      [1, 1, 0, 0, 0, 0, 1, 1, 0, 0],
                      [0, 1, 1, 0, 0, 1, 1, 0, 0, 0],
                      [0, 0, 1, 1, 1, 1, 0, 0, 0, 0]])
vector_J = np.ndarray.flatten(letter_J)  # 將字母J的矩陣展平為向量

letter_K = np.array([[1, 1, 0, 0, 0, 0, 1, 1, 1, 0],
                      [1, 1, 0, 0, 0, 1, 1, 1, 0, 0],
                      [1, 1, 0, 0, 1, 1, 1, 0, 0, 0],
                      [1, 1, 0, 1, 1, 1, 0, 0, 0, 0],
                      [1, 1, 1, 1, 1, 0, 0, 0, 0, 0],
                      [1, 1, 1, 1, 1, 0, 0, 0, 0, 0],
                      [1, 1, 0, 0, 1, 1, 1, 0, 0, 0],
                      [1, 1, 0, 0, 0, 1, 1, 1, 0, 0],
                      [1, 1, 0, 0, 0, 0, 1, 1, 1, 0],
                      [1, 1, 0, 0, 0, 0, 0, 1, 1, 1]])
vector_K = np.ndarray.flatten(letter_K)  # 將字母K的矩陣展平為向量
letter_L = np.array([[1, 1, 0, 0, 0, 0, 0, 0, 0, 0],
                      [1, 1, 0, 0, 0, 0, 0, 0, 0, 0],
                      [1, 1, 0, 0, 0, 0, 0, 0, 0, 0],
                      [1, 1, 0, 0, 0, 0, 0, 0, 0, 0],
                      [1, 1, 0, 0, 0, 0, 0, 0, 0, 0],
                      [1, 1, 0, 0, 0, 0, 0, 0, 0, 0],
                      [1, 1, 0, 0, 0, 0, 0, 0, 0, 0],
                      [1, 1, 0, 0, 0, 0, 0, 0, 0, 0],
                      [1, 1, 1, 1, 1, 1, 1, 1, 1, 1],
                      [1, 1, 1, 1, 1, 1, 1, 1, 1, 1]])
vector_L = np.ndarray.flatten(letter_L)  # 將字母L的矩陣展平為向量

letter_M = np.array([[1, 1, 0, 0, 0, 0, 0, 0, 1, 1],
                      [1, 1, 1, 0, 0, 0, 0, 1, 1, 1],
                      [1, 1, 1, 1, 0, 0, 1, 1, 1, 1],
                      [1, 1, 1, 1, 1, 1, 1, 1, 1, 1],
                      [1, 1, 1, 1, 1, 1, 1, 1, 1, 1],
                      [1, 1, 0, 1, 1, 1, 1, 0, 1, 1],
                      [1, 1, 0, 0, 1, 1, 0, 0, 1, 1],
                      [1, 1, 0, 0, 0, 0, 0, 0, 1, 1],
                      [1, 1, 0, 0, 0, 0, 0, 0, 1, 1],
                      [1, 1, 0, 0, 0, 0, 0, 0, 1, 1]])
vector_M = np.ndarray.flatten(letter_M)  # 將字母M的矩陣展平為向量

letter_N = np.array([[1, 1, 0, 0, 0, 0, 0, 0, 1, 1],
                      [1, 1, 1, 0, 0, 0, 0, 0, 1, 1],
                      [1, 1, 1, 1, 0, 0, 0, 0, 1, 1],
                      [1, 1, 1, 1, 1, 0, 0, 0, 1, 1],
                      [1, 1, 0, 1, 1, 1, 0, 0, 1, 1],
                      [1, 1, 0, 0, 1, 1, 1, 0, 1, 1],
                      [1, 1, 0, 0, 0, 1, 1, 1, 1, 1],
                      [1, 1, 0, 0, 0, 0, 1, 1, 1, 1],
                      [1, 1, 0, 0, 0, 0, 0, 1, 1, 1],
                      [1, 1, 0, 0, 0, 0, 0, 0, 1, 1]])
vector_N = np.ndarray.flatten(letter_N)

letter_O = np.array([[0, 0, 1, 1, 1, 1, 1, 1, 0, 0],
                      [0, 1, 1, 1, 1, 1, 1, 1, 1, 0],
                      [1, 1, 1, 0, 0, 0, 0, 1, 1, 1],
                      [1, 1, 0, 0, 0, 0, 0, 0, 1, 1],
                      [1, 1, 0, 0, 0, 0, 0, 0, 1, 1],
                      [1, 1, 0, 0, 0, 0, 0, 0, 1, 1],
                      [1, 1, 0, 0, 0, 0, 0, 0, 1, 1],
                      [1, 1, 1, 0, 0, 0, 0, 1, 1, 1],
                      [0, 1, 1, 1, 1, 1, 1, 1, 1, 0],
                      [0, 0, 1, 1, 1, 1, 1, 1, 0, 0]])
vector_O = np.ndarray.flatten(letter_O)

letter_P = np.array([[1, 1, 1, 1, 1, 1, 1, 1, 0, 0],
                      [1, 1, 0, 0, 0, 0, 0, 1, 1, 0],
                      [1, 1, 0, 0, 0, 0, 0, 1, 1, 0],
                      [1, 1, 0, 0, 0, 0, 0, 1, 1, 0],
                      [1, 1, 1, 1, 1, 1, 1, 1, 0, 0],
                      [1, 1, 0, 0, 0, 0, 0, 0, 0, 0],
                      [1, 1, 0, 0, 0, 0, 0, 0, 0, 0],
                      [1, 1, 0, 0, 0, 0, 0, 0, 0, 0],
                      [1, 1, 0, 0, 0, 0, 0, 0, 0, 0],
                      [1, 1, 0, 0, 0, 0, 0, 0, 0, 0]])
vector_P = np.ndarray.flatten(letter_P)

letter_Q = np.array([[0, 0, 1, 1, 1, 1, 1, 1, 0, 0],
                      [0, 1, 1, 1, 1, 1, 1, 1, 1, 0],
                      [1, 1, 1, 0, 0, 0, 0, 1, 1, 1],
                      [1, 1, 0, 0, 0, 0, 0, 0, 1, 1],
                      [1, 1, 0, 0, 0, 0, 0, 0, 1, 1],
                      [1, 1, 0, 0, 0, 1, 0, 0, 1, 1],
                      [1, 1, 0, 0, 0, 1, 1, 0, 1, 1],
                      [1, 1, 1, 0, 0, 0, 1, 1, 1, 1],
                      [0, 1, 1, 1, 1, 1, 1, 1, 1, 1],
                      [0, 0, 1, 1, 1, 1, 1, 1, 0, 1]])
vector_Q = np.ndarray.flatten(letter_Q)

letter_R = np.array([[1, 1, 1, 1, 1, 1, 1, 1, 0, 0],
                      [1, 1, 0, 0, 0, 0, 0, 1, 1, 0],
                      [1, 1, 0, 0, 0, 0, 0, 1, 1, 0],
                      [1, 1, 0, 0, 0, 0, 0, 1, 1, 0],
                      [1, 1, 1, 1, 1, 1, 1, 1, 0, 0],
                      [1, 1, 0, 1, 1, 1, 0, 0, 0, 0],
                      [1, 1, 0, 0, 1, 1, 1, 0, 0, 0],
                      [1, 1, 0, 0, 0, 1, 1, 1, 0, 0],
                      [1, 1, 0, 0, 0, 0, 1, 1, 1, 0],
                      [1, 1, 0, 0, 0, 0, 0, 1, 1, 1]])
vector_R = np.ndarray.flatten(letter_R)

letter_S = np.array([[0, 1, 1, 1, 1, 1, 1, 1, 0, 0],
                      [1, 1, 1, 0, 0, 0, 0, 1, 1, 0],
                      [1, 1, 0, 0, 0, 0, 0, 0, 0, 0],
                      [1, 1, 1, 0, 0, 0, 0, 0, 0, 0],
                      [0, 1, 1, 1, 1, 1, 1, 0, 0, 0],
                      [0, 0, 0, 1, 1, 1, 1, 1, 0, 0],
                      [0, 0, 0, 0, 0, 0, 1, 1, 1, 0],
                      [0, 0, 0, 0, 0, 0, 0, 1, 1, 0],
                      [1, 1, 0, 0, 0, 0, 1, 1, 1, 0],
                      [0, 1, 1, 1, 1, 1, 1, 1, 0, 0]])
vector_S = np.ndarray.flatten(letter_S)

letter_T = np.array([[1, 1, 1, 1, 1, 1, 1, 1, 1, 1],
                      [1, 1, 1, 1, 1, 1, 1, 1, 1, 1],
                      [0, 0, 0, 0, 1, 1, 0, 0, 0, 0],
                      [0, 0, 0, 0, 1, 1, 0, 0, 0, 0],
                      [0, 0, 0, 0, 1, 1, 0, 0, 0, 0],
                      [0, 0, 0, 0, 1, 1, 0, 0, 0, 0],
                      [0, 0, 0, 0, 1, 1, 0, 0, 0, 0],
                      [0, 0, 0, 0, 1, 1, 0, 0, 0, 0],
                      [0, 0, 0, 0, 1, 1, 0, 0, 0, 0],
                      [0, 0, 0, 0, 1, 1, 0, 0, 0, 0]])
vector_T = np.ndarray.flatten(letter_T)

letter_U = np.array([[1, 1, 0, 0, 0, 0, 0, 0, 1, 1],
                      [1, 1, 0, 0, 0, 0, 0, 0, 1, 1],
                      [1, 1, 0, 0, 0, 0, 0, 0, 1, 1],
                      [1, 1, 0, 0, 0, 0, 0, 0, 1, 1],
                      [1, 1, 0, 0, 0, 0, 0, 0, 1, 1],
                      [1, 1, 0, 0, 0, 0, 0, 0, 1, 1],
                      [1, 1, 0, 0, 0, 0, 0, 0, 1, 1],
                      [1, 1, 0, 0, 0, 0, 0, 0, 1, 1],
                      [0, 1, 1, 1, 1, 1, 1, 1, 1, 0],
                      [0, 0, 1, 1, 1, 1, 1, 1, 0, 0]])
vector_U = np.ndarray.flatten(letter_U)

letter_V = np.array([[1, 1, 0, 0, 0, 0, 0, 0, 1, 1],
                      [1, 1, 0, 0, 0, 0, 0, 0, 1, 1],
                      [1, 1, 0, 0, 0, 0, 0, 0, 1, 1],
                      [1, 1, 0, 0, 0, 0, 0, 0, 1, 1],
                      [0, 1, 1, 0, 0, 0, 0, 1, 1, 0],
                      [0, 1, 1, 0, 0, 0, 0, 1, 1, 0],
                      [0, 0, 1, 1, 0, 0, 1, 1, 0, 0],
                      [0, 0, 1, 1, 0, 0, 1, 1, 0, 0],
                      [0, 0, 0, 1, 1, 1, 1, 0, 0, 0],
                      [0, 0, 0, 0, 1, 1, 0, 0, 0, 0]])
vector_V = np.ndarray.flatten(letter_V)

letter_W = np.array([[1, 1, 0, 0, 0, 0, 0, 0, 1, 1],
                      [1, 1, 0, 0, 0, 0, 0, 0, 1, 1],
                      [1, 1, 0, 0, 0, 0, 0, 0, 1, 1],
                      [1, 1, 0, 0, 0, 0, 0, 0, 1, 1],
                      [1, 1, 0, 0, 1, 1, 0, 0, 1, 1],
                      [1, 1, 0, 1, 1, 1, 1, 0, 1, 1],
                      [1, 1, 1, 1, 0, 0, 1, 1, 1, 1],
                      [1, 1, 1, 0, 0, 0, 0, 1, 1, 1],
                      [1, 1, 0, 0, 0, 0, 0, 0, 1, 1],
                      [1, 1, 0, 0, 0, 0, 0, 0, 1, 1]])
vector_W = np.ndarray.flatten(letter_W)

letter_X = np.array([[1, 1, 0, 0, 0, 0, 0, 0, 1, 1],
                      [0, 1, 1, 0, 0, 0, 0, 1, 1, 0],
                      [0, 0, 1, 1, 0, 0, 1, 1, 0, 0],
                      [0, 0, 0, 1, 1, 1, 1, 0, 0, 0],
                      [0, 0, 0, 0, 1, 1, 0, 0, 0, 0],
                      [0, 0, 0, 0, 1, 1, 0, 0, 0, 0],
                      [0, 0, 0, 1, 1, 1, 1, 0, 0, 0],
                      [0, 0, 1, 1, 0, 0, 1, 1, 0, 0],
                      [0, 1, 1, 0, 0, 0, 0, 1, 1, 0],
                      [1, 1, 0, 0, 0, 0, 0, 0, 1, 1]])
vector_X = np.ndarray.flatten(letter_X)

letter_Y = np.array([[1, 1, 0, 0, 0, 0, 0, 0, 1, 1],
                      [0, 1, 1, 0, 0, 0, 0, 1, 1, 0],
                      [0, 0, 1, 1, 0, 0, 1, 1, 0, 0],
                      [0, 0, 0, 1, 1, 1, 1, 0, 0, 0],
                      [0, 0, 0, 0, 1, 1, 0, 0, 0, 0],
                      [0, 0, 0, 0, 1, 1, 0, 0, 0, 0],
                      [0, 0, 0, 0, 1, 1, 0, 0, 0, 0],
                      [0, 0, 0, 0, 1, 1, 0, 0, 0, 0],
                      [0, 0, 0, 0, 1, 1, 0, 0, 0, 0],
                      [0, 0, 0, 0, 1, 1, 0, 0, 0, 0]])
vector_Y = np.ndarray.flatten(letter_Y)

letter_Z = np.array([[1, 1, 1, 1, 1, 1, 1, 1, 1, 1],
                      [0, 0, 0, 0, 0, 0, 0, 1, 1, 0],
                      [0, 0, 0, 0, 0, 0, 1, 1, 0, 0],
                      [0, 0, 0, 0, 0, 1, 1, 0, 0, 0],
                      [0, 0, 0, 0, 1, 1, 0, 0, 0, 0],
                      [0, 0, 0, 1, 1, 0, 0, 0, 0, 0],
                      [0, 0, 1, 1, 0, 0, 0, 0, 0, 0],
                      [0, 1, 1, 0, 0, 0, 0, 0, 0, 0],
                      [1, 1, 0, 0, 0, 0, 0, 0, 0, 0],
                      [1, 1, 1, 1, 1, 1, 1, 1, 1, 1]])
vector_Z = np.ndarray.flatten(letter_Z)

training_data = np.array([vector_A, vector_B, vector_C, vector_D, vector_E, vector_F, vector_G, vector_H, vector_I, vector_J, vector_K, vector_L, vector_M, vector_N, vector_O, vector_P, vector_Q, vector_R, vector_S, vector_T, vector_U, vector_V, vector_W, vector_X, vector_Y, vector_Z])

np.random.seed()
w1 = np.random.normal(loc=0, scale=0.1, size=(100,80)) # 100個輸入，20個隱藏節點
w2 = np.random.normal(loc=0, scale=0.1, size=(80,26))   # 20個隱藏節點，26個輸出

targets = np.eye(26)
a = 1 #控制陡峭程度
bias = 0.7 #控制曲線左右移動
al=0.01 #學習速率
def sigmoid(m):
    return 1/(1+np.exp(-a*(m-bias)))

#caculate output 向前傳播
def get_output(inp, wei):
    memP = np.dot(inp, wei)
    out =sigmoid(memP)
    return out
 
# x = np.array([[0,0],[1,0],[0,1],[1,1]])
# t = np.array([0,1,1,1]) #產生目標 #or gate
# # o1 = get_output(x[1,:], w1) 


#learning
def delta_rule_output(target,outp):
    #修改delta變成oup-target-->這樣會差一個負號
    delta = outp-target
    delt= delta*a*outp*(1-outp)
    return delt

def delta_rule_hidden(delta_p, wei, outp):
    delta = np.dot(delta_p, wei.T)    
    delt = delta*outp*(1-outp)
    return delt

# o1 = get_output(x[1,:],w1)
# o2 = get_output(o1,w2)
# del2=delta_rule_output(t[1], o2)
# print(del2)

# del1=delta_rule_hidden(del2,w2,o1)
# print(del1)

#caculate output before training
def get_all_output():
    ov = np.zeros((26,26))
    for i in range(26):
        o1 = get_output(training_data[i,:],w1)
        o2 = get_output(o1,w2)
        ov[i] =o2
    return ov

out = get_all_output()
print("學習前")
print(out)


for rpt in range(5000): 
    for i in range(26):
        o1 = get_output(training_data[i,:],w1)
        o2=get_output(o1,w2)
        del2 = delta_rule_output(targets[i], o2) #為了得到2*3矩陣，要用outer外積，反向傳播回去
        del1 = delta_rule_hidden(del2,w2,o1)
        w2 = w2 -al * np.outer(o1, del2)  # 使用 outer product
        w1 = w1-al*np.outer(training_data[i,:],del1)


out = get_all_output()
print("學習後")
print(out)

# 在訓練完成後添加以下代碼
print("\n最終識別結果：")
correct = 0 #計算正確數量

for i in range(26):
    # 獲取預測結果
    o1 = get_output(training_data[i], w1) #使用get_output函數計算隱藏層的輸出，而不是使用get_all_output
    o2 = get_output(o1, w2)
    pred = np.argmax(o2) #使用np.argmax函數找到輸出層中最大值的索引，這個索引對應於預測的字母
    
    # 計算正確數量
    if pred == i:
        correct += 1
    
    # 輸出結果
    print(f"當輸入層為字母為{chr(65+i)}，經過學習後辨認出這個字母為{chr(65+pred)}") #chr(65+i) 將數字轉換為對應的字母，65是字母A的ASCII碼

# 顯示總準確率
accuracy = (correct / 26) * 100
print(f"\n模型準確率: {accuracy:.1f}%") 



