#!/usr/bin/env python3
"""
二階後設認知模型演示
展示如何將論文中的數學概念轉化為可運算的模型
"""

import sys
import importlib.util
import matplotlib.pyplot as plt
import numpy as np

# 導入主模組
spec = importlib.util.spec_from_file_location('foj_module', '(FOJ)神經網路期末.py')
foj_module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(foj_module)

def demonstrate_two_stage_metacognition():
    """演示二階後設認知的數學實現"""
    print("=== 二階後設認知數學模型演示 ===")
    print("基於論文: The influence of second-order metacognitive judgments on cognitive offloading")
    print()
    
    # 創建參數
    params = foj_module.ModelParameters()
    
    print("1. 二階後設認知的數學化:")
    print("   FOJ (First-Order Judgment): 我覺得自己記不記得？ (0/1)")
    print("   SOJ (Second-Order Judgment): 我對那個感覺有多確定？ (0-1)")
    print("   GLMM: logit(P(卸載)) = β₀ + β₁·FOJ + β₂·SOJ + β₃·(FOJ×SOJ)")
    print()
    
    # 測試不同證據強度下的後設認知
    evidence_levels = np.linspace(0.2, 0.9, 8)
    results = []
    
    print("2. 不同記憶證據強度下的後設認知反應:")
    print("   證據強度  FOJ  SOJ    卸載機率")
    print("   --------  ---  -----  --------")
    
    for evidence in evidence_levels:
        foj, soj, offload_prob = foj_module.compute_two_stage_metacognition(evidence, params)
        results.append((evidence, foj, soj, offload_prob))
        print(f"   {evidence:.1f}       {foj}    {soj:.3f}  {offload_prob:.3f}")
    
    print()
    print("3. GLMM 係數解釋:")
    print(f"   β₁ (FOJ主效應): {params.beta_foj:.2f} → FOJ=1時降低卸載機率")
    print(f"   β₂ (SOJ主效應): {params.beta_soj:.2f} → 信心越高越可能卸載")
    print(f"   β₃ (交互作用): {params.beta_interaction:.2f} → 高信心時FOJ效果更強")
    
    return results

def demonstrate_monitoring_accuracy():
    """演示監控精度的計算"""
    print("\n=== 監控精度指標演示 ===")
    
    # 模擬一些後設認知數據
    np.random.seed(42)
    n_trials = 50
    
    # 生成模擬數據
    actual_performance = np.random.binomial(1, 0.7, n_trials)
    foj_judgments = []
    soj_confidence = []
    
    for performance in actual_performance:
        # FOJ 與實際表現有關但有噪音
        foj_prob = 0.8 if performance == 1 else 0.3
        foj = np.random.binomial(1, foj_prob)
        foj_judgments.append(foj)
        
        # SOJ 基於 FOJ 但有額外變異
        soj = 0.7 + 0.2 * foj + np.random.normal(0, 0.1)
        soj = np.clip(soj, 0, 1)
        soj_confidence.append(soj)
    
    # 計算監控精度
    accuracy_metrics = foj_module.calculate_monitoring_accuracy(
        foj_judgments, soj_confidence, actual_performance
    )
    
    print("監控精度指標:")
    print(f"  Goodman-Kruskal γ: {accuracy_metrics['gamma_foj_performance']:.3f}")
    print(f"  Spearman ρ (FOJ): {accuracy_metrics['rho_foj_performance']:.3f}")
    print(f"  Spearman ρ (SOJ): {accuracy_metrics['rho_soj_performance']:.3f}")
    print(f"  平均 SOJ: {accuracy_metrics['mean_soj']:.3f}")
    
    # 一致性曲線分析
    offload_decisions = [np.random.binomial(1, 0.5) for _ in range(n_trials)]
    soj_bins, consistency_rates = foj_module.analyze_consistency_curve(
        foj_judgments, soj_confidence, offload_decisions
    )
    
    print(f"\n一致性曲線分析:")
    print(f"  SOJ 區間數: {len(soj_bins)}")
    print(f"  一致性率範圍: {min(consistency_rates):.3f} - {max(consistency_rates):.3f}")
    
    return accuracy_metrics, (soj_bins, consistency_rates)

def demonstrate_comparison():
    """演示傳統方法與二階後設認知方法的比較"""
    print("\n=== 方法比較演示 ===")
    
    # 使用小參數進行快速演示
    params = foj_module.ModelParameters(
        n_high_items=2, n_low_items=2,
        n_episodes_per_strategy_eval=10,
        n_model_runs=20
    )
    
    print("運行比較模擬...")
    comparison_results = foj_module.simulate_metacognitive_comparison(params)
    
    traditional = comparison_results['traditional']
    metacognitive = comparison_results['metacognitive']
    
    print("\n比較結果:")
    print(f"  方法           高價值準確率  低價值準確率")
    print(f"  -----------    -----------  -----------")
    print(f"  傳統方法       {traditional['mean_accuracy_H']:.3f}        {traditional['mean_accuracy_L']:.3f}")
    print(f"  二階後設認知   {metacognitive['mean_accuracy_H']:.3f}        {metacognitive['mean_accuracy_L']:.3f}")
    
    if 'metacognitive_analysis' in metacognitive and metacognitive['metacognitive_analysis']:
        analysis = metacognitive['metacognitive_analysis']
        print(f"\n二階後設認知分析:")
        print(f"  監控精度 γ: {analysis.get('gamma_foj_performance', 0):.3f}")
        print(f"  平均 SOJ: {analysis.get('mean_soj', 0):.3f}")
    
    return comparison_results

def main():
    """主演示函數"""
    print("🧠 二階後設認知模型：從論文到程式碼")
    print("=" * 50)
    
    try:
        # 演示二階後設認知
        metacog_results = demonstrate_two_stage_metacognition()
        
        # 演示監控精度
        accuracy_results, consistency_data = demonstrate_monitoring_accuracy()
        
        # 演示方法比較
        comparison_results = demonstrate_comparison()
        
        print("\n" + "=" * 50)
        print("🎉 演示完成！")
        print("\n主要成果:")
        print("✓ 成功將論文中的 FOJ/SOJ 概念數學化")
        print("✓ 實現了 GLMM 卸載決策模型")
        print("✓ 計算了 Goodman-Kruskal γ 監控精度")
        print("✓ 分析了一致性曲線")
        print("✓ 比較了傳統方法與二階後設認知方法")
        
        print("\n這個實現展示了如何將心理學研究中的")
        print("抽象概念轉化為可運算的數學模型！")
        
    except Exception as e:
        print(f"❌ 演示過程中發生錯誤: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
