import numpy as np
import matplotlib.pyplot as plt
from matplotlib.animation import FuncAnimation

## 繪製迷宮
fig = plt.figure(figsize=(8,8))
ax = plt.gca() # 獲取當前座標軸
def ini_maze():
    # 繪製牆壁
    # 設置一些水平和垂直牆壁，但確保有路徑從起點到終點
    # 水平牆壁 (x1,x2,y,y)
    plt.plot([1,2],[1,1], color='red', linewidth=2)
    plt.plot([3,4],[1,1], color='red', linewidth=2)
    plt.plot([0,1],[2,2], color='red', linewidth=2)
    plt.plot([3,4],[2,2], color='red', linewidth=2)
    plt.plot([1,2],[3,3], color='red', linewidth=2)
    plt.plot([3,4],[3,3], color='red', linewidth=2)
    plt.plot([0,1],[4,4], color='red', linewidth=2)
    plt.plot([2,3],[4,4], color='red', linewidth=2)
    
    # 垂直牆壁 (x,x,y1,y2)
    plt.plot([1,1],[0,1], color='red', linewidth=2)
    plt.plot([2,2],[1,2], color='red', linewidth=2)
    plt.plot([4,4],[0,1], color='red', linewidth=2)
    plt.plot([2,2],[2,3], color='red', linewidth=2)
    plt.plot([3,3],[3,4], color='red', linewidth=2)
    plt.plot([4,4],[3,4], color='red', linewidth=2)
    
    # 繪製文本 (25個狀態，5x5)
    for i in range(5):
        for j in range(5):
            state_num = i * 5 + j
            plt.text(j+0.5, 4.5-i, f'S{state_num}', size=10, ha='center')
    
    plt.text(0.5, 4.3, 'START', ha='center', size=8)
    plt.text(4.5, 0.3, 'GOAL', ha='center', size=8)
    
    # 設置圖形大小
    ax.set_xlim(0,5)
    ax.set_ylim(0,5)
    plt.tick_params(axis='both', which='both', bottom=False, top=False, labelbottom=False, right=False, left=False, labelleft=False)

# 初始化theta (現在有25個狀態，每個狀態4個動作)
# 初始值都是1，但邊界和牆壁處設置為NaN
theta_0 = np.ones((25, 4))



# 初始化theta (25個狀態，每個動作的初始偏好)
theta_0 = np.array([
    # S0 (起點)
    [np.nan, 1, np.nan, np.nan],
    # S1
    [np.nan, 1, 1, 1],
    # S2
    [np.nan, 1, np.nan, 1],
    # S3
    [np.nan, 1, 1, 1],
    # S4 
    [np.nan, np.nan, 1, 1],
    
    # S5
    [np.nan, 1, 1, np.nan],
    # S6
    [1, 1, np.nan, 1],
    # S7
    [np.nan, np.nan, 1, 1],
    # S8
    [1, np.nan, np.nan, np.nan],
    # S9 
    [1, np.nan, 1, np.nan],
    
    # S10
    [1, 1, np.nan, np.nan],
    # S11
    [np.nan, np.nan, 1, 1],
    # S12
    [1, 1, 1, np.nan],
    # S13
    [np.nan, 1, np.nan, 1],
    # S14 
    [1, np.nan, 1, 1],
    
    # S15
    [np.nan, 1, 1, np.nan],
    # S16
    [1, np.nan, np.nan, 1],
    # S17
    [1, 1, 1, np.nan],
    # S18
    [np.nan, 1, np.nan, 1],
    # S19 
    [1, np.nan, 1, 1],
    
    # S20 
    [1, np.nan, np.nan, np.nan],
    # S21
    [np.nan, 1, np.nan, np.nan],
    # S22
    [1, 1, np.nan, 1],
    # S23
    [np.nan, np.nan, np.nan, 1],
    # S24 
    [1, np.nan, np.nan, np.nan]
])
ini_maze()
line, = ax.plot([0.5],[4.5], marker='o', color='lightgreen', markersize=60)
# plt.show()
# initial theta
# convert theta to pi
def simple_theta_to_pi(theta):
    [m, n]=theta.shape
    pi=np.zeros((m, n))
    for i in range(0,m):
        pi[i,:]=theta[i,:]/np.nansum(theta[i,:])
    pi=np.nan_to_num(pi)
    return pi
def softmax_pi_from_theta(theta):
    beta=2
    [m,n]=theta.shape
    pi=np.zeros((m,n))
    exp_theta=np.exp(beta*theta)
    for i in range(0,m):
        sum_exp=np.nansum(exp_theta[i,:])
        pi[i,:]=exp_theta[i,:]/sum_exp
    pi=np.nan_to_num(pi)
    return pi
pi_0=softmax_pi_from_theta(theta_0)

direction = ["up", "right", "down", "left"]
def get_next_s(pi, s):
    next_direct = np.random.choice(direction, p=pi[s,:])
    if next_direct == "up":
        s_next=s-5
    elif next_direct == "right":
        s_next=s+1
    elif next_direct == "down":
        s_next=s+5
    elif next_direct == "left":
        s_next=s-1
    return s_next

def get_action_next_s(pi, s):
    next_direction=np.random.choice(direction, p=pi[s,:])
    if next_direction=="up":
        s_next=s-5
        action=0
    elif next_direction=="right":
        s_next=s+1
        action=1
    elif next_direction=="down":
        s_next=s+5
        action=2
    else:
        s_next=s-1
        action=3
    return [s_next, action]


def update_theta(theta, pi, history_s, history_a):
    eta = 0.5
    TotalN = len(history_s)
    [m, n] = theta.shape
    delta_theta = theta.copy()
    N_i = np.zeros(m)
    N_ij = np.zeros((m, n))
    for t in range(0, TotalN-1):
        i=history_s[t]
        j=history_a[t]
        N_i[i]=N_i[i]+1
        N_ij[i,j]=N_ij[i,j]+1
    for i in range(0, m):
        for j in range(0, n):
            delta_theta[i, j] = (N_ij[i,j] - pi[i, j] * N_i[i]) / TotalN
    new_theta = theta + eta * delta_theta
    return new_theta

def run_maze(pi):
    s=0 # starting state
    history_s=[0] # records of states
    history_a=[np.nan] # records of action
    while(s!=24):
        [next_s, action]=get_action_next_s(pi, s)
        history_a[-1]=action
        history_s.append(next_s)
        history_a.append(np.nan)
        s=next_s
    return (history_s, history_a)

theta_run=theta_0
step_history=[]
for i in range(0,100):
    pi_run= softmax_pi_from_theta(theta_run)
    state_history, action_history = run_maze(pi_run)
    step_history.append(len(state_history))
    theta_run = update_theta(theta_run, pi_run, state_history, action_history)

print(step_history)
pi_run= softmax_pi_from_theta(theta_run)
state_history, action_history = run_maze(pi_run)
print(state_history)
print(action_history)
print(theta_0)
print(theta_run)

def update(i):
    state = state_history[i]
    x = (state % 5) + 0.5
    y = 4.5 - int(state / 5)
    plt.cla()
    ini_maze()
    ax.plot(x, y, marker='o', color='green', markersize=60)
    
    plt.text(0.5, 5.2, f'WALKED: {i}/{len(state_history)-1}', ha='center', fontsize=12)
anim=FuncAnimation(fig=fig, func=update, frames=len(state_history), interval=100, repeat=False)
plt.show()
plt.figure(figsize=(10, 6))
plt.plot(step_history)
plt.title('Theta Update: Steps per Iteration')
plt.xlabel('Iteration Number')
plt.ylabel('Steps')
plt.grid(True)
plt.show()
