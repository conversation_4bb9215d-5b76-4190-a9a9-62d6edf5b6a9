The code to run the model is split into three files:

initialise.R

This initialises all variables with default parameter settings

run_simulation.R

This runs the actual simulations, based on the parameter settings contained within initialise.R

summarise_results.R

This outputs a summary of results to the console, prints a graph, and also saves it as an image to a file called fig.pdf

NB the summarise_results script requires 'patchwork' and 'ggplot2' libraries.

The code to reproduce figures 2-8 of the article is found in the corresponding R scripts. Figure 3 runs the model with standard parameter settings. So to run the basic simulation, copy all files to a directory, set this to be your working directory in R, then run fig3.R

The figures included in the article are based on a large number of runs to ensure accurate estimation of results. However, this will be slow, so the initialise.R file sets the runs parameter to 1000. This should be more than sufficient to verify the qualitative pattern of results.
