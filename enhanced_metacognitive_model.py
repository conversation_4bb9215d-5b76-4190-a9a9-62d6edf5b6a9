# Enhanced Metacognitive Cognitive Offloading Model
# 基於 (後設認知)神經網路期末.py 加入人類認知特徵
# 
# 主要增強功能:
# 1. 保留所有原始 Fig.2-8 模擬功能
# 2. 加入認知偏誤: 損失規避、過度自信、錨定效應、可得性啟發
# 3. 個體差異建模: 工作記憶容量、風險容忍度、處理速度
# 4. 學習機制: 經驗累積、策略偏好更新、強化學習
# 5. 後設認知與人類特徵的結合

import numpy as np
import matplotlib.pyplot as plt
from dataclasses import dataclass, replace, field
from typing import List, Tuple, Dict, Any, Optional
import itertools
from collections import defaultdict, deque
import random
import copy

# --- 增強的模型參數 (基於原始 ModelParameters) ---
@dataclass
class EnhancedModelParameters:
    # 原始基礎任務參數
    n_high_items: int = 3
    n_low_items: int = 3
    value_high: float = 8.0
    value_low: float = 2.0
    cost_offloading: float = 1.0

    # 原始記憶準確率參數
    accuracy_internal_1_item: float = 0.95
    accuracy_internal_2_items: float = 0.925
    accuracy_internal_3_items: float = 0.90
    accuracy_internal_6_items: float = 0.75
    accuracy_offloaded: float = 0.98

    # 原始模擬控制參數
    n_episodes_per_strategy_eval: int = 20
    n_model_runs: int = 100

    # 原始後設認知參數
    metacognitive_threshold: float = 0.85
    metacognitive_bias: float = 0.0
    confidence_noise_std: float = 0.1

    # --- 新增: 人類認知偏誤參數 ---
    loss_aversion_coefficient: float = 2.25      # 損失規避強度
    overconfidence_factor: float = 0.05          # 過度自信程度
    anchoring_weight: float = 0.3                # 錨定效應強度
    availability_window: int = 10                # 可得性啟發記憶窗口
    availability_weight: float = 1.5             # 最近經驗權重倍數

    # --- 新增: 個體差異參數 ---
    working_memory_capacity: float = 1.0         # 工作記憶容量
    risk_tolerance: float = 0.5                  # 風險容忍度
    processing_speed: float = 1.0                # 處理速度

    # --- 新增: 學習參數 ---
    learning_rate: float = 0.1                   # 學習率
    memory_decay_rate: float = 0.95              # 記憶衰減率
    exploration_rate: float = 0.1                # 探索率
    experience_buffer_size: int = 50             # 經驗緩衝區大小

    # --- 新增: 人性化模擬控制 ---
    enable_human_features: bool = False          # 是否啟用人性化特徵
    population_size: int = 30                    # 虛擬人群大小


# --- 人類認知代理類 ---
@dataclass
class HumanCognitiveAgent:
    """
    代表具有個體差異和學習能力的認知代理
    """
    agent_id: str
    
    # 個體特徵 (從分布中採樣)
    working_memory_capacity: float
    risk_tolerance: float
    processing_speed: float
    loss_aversion_coefficient: float
    overconfidence_factor: float
    
    # 學習狀態
    experience_buffer: deque = field(default_factory=lambda: deque(maxlen=50))
    strategy_preferences: Dict[Tuple, float] = field(default_factory=dict)
    confidence_history: List[float] = field(default_factory=list)
    
    # 錨定參考點
    anchor_accuracy: Optional[float] = None
    anchor_reward: Optional[float] = None
    
    def __post_init__(self):
        if not self.experience_buffer:
            self.experience_buffer = deque(maxlen=50)
        if not self.strategy_preferences:
            self.strategy_preferences = {}
        if not self.confidence_history:
            self.confidence_history = []


def create_diverse_population(n_agents: int, params: EnhancedModelParameters) -> List[HumanCognitiveAgent]:
    """
    創建具有多樣性的虛擬人群
    """
    agents = []
    
    for i in range(n_agents):
        agent = HumanCognitiveAgent(
            agent_id=f"agent_{i:03d}",
            
            # 工作記憶容量: 常態分布 N(1.0, 0.3)
            working_memory_capacity=max(0.3, np.random.normal(1.0, 0.3)),
            
            # 風險容忍度: Beta分布 (偏向風險規避)
            risk_tolerance=np.random.beta(2, 3),
            
            # 處理速度: 對數常態分布
            processing_speed=max(0.2, np.random.lognormal(0, 0.3)),
            
            # 損失規避: 常態分布
            loss_aversion_coefficient=max(1.0, np.random.normal(2.25, 0.5)),
            
            # 過度自信: 常態分布 (可能為負值，表示不夠自信)
            overconfidence_factor=np.random.normal(0.05, 0.08)
        )
        
        agents.append(agent)
    
    return agents


# --- 認知偏誤函數 ---
def apply_loss_aversion(reward: float, loss_aversion_coef: float, reference_point: float = 0.0) -> float:
    """應用損失規避偏誤"""
    if reward >= reference_point:
        return reward - reference_point
    else:
        return -(loss_aversion_coef * (reference_point - reward))


def apply_overconfidence_bias(objective_accuracy: float, overconfidence_factor: float) -> float:
    """應用過度自信偏誤"""
    confidence_multiplier = 1.0 + overconfidence_factor * (1 - abs(objective_accuracy - 0.5) * 2)
    return np.clip(objective_accuracy * confidence_multiplier, 0.0, 1.0)


def apply_anchoring_bias(current_estimate: float, anchor: float, anchoring_weight: float) -> float:
    """應用錨定偏誤"""
    if anchor is None:
        return current_estimate
    return current_estimate * (1 - anchoring_weight) + anchor * anchoring_weight


def apply_availability_heuristic(experiences: deque, availability_window: int, availability_weight: float) -> float:
    """應用可得性啟發"""
    if not experiences:
        return 0.5
    
    recent_experiences = list(experiences)[-availability_window:]
    older_experiences = list(experiences)[:-availability_window] if len(experiences) > availability_window else []
    
    if not recent_experiences:
        return np.mean([exp['success'] for exp in experiences])
    
    recent_success = np.mean([exp['success'] for exp in recent_experiences]) * availability_weight
    
    if older_experiences:
        older_success = np.mean([exp['success'] for exp in older_experiences])
        total_weight = availability_weight + 1.0
        return (recent_success + older_success) / total_weight
    else:
        return recent_success / availability_weight


# --- 策略定義 (保持與原始檔案一致) ---
Strategy = Tuple[bool, bool, bool, bool]
StrategyFig8 = Tuple[bool, bool]


def get_all_strategies(allow_offloading=True, offload_restriction="none") -> List[Strategy]:
    """保持與原始檔案一致的策略生成函數"""
    options_sh = [True, False]
    options_sl = [True, False]

    if not allow_offloading:
        options_oh = [False]
        options_ol = [False]
    else:
        if offload_restriction == "high_only":
            options_oh = [True, False]
            options_ol = [False]
        else:
            options_oh = [True, False]
            options_ol = [True, False]
    
    return list(itertools.product(options_sh, options_sl, options_oh, options_ol))


def get_strategies_fig8() -> List[StrategyFig8]:
    """Fig.8 專用策略生成"""
    return [
        (True, True),    # encode=True, offload_allowed=True
        (True, False),   # encode=True, offload_allowed=False
        (False, True),   # encode=False, offload_allowed=True
        (False, False)   # encode=False, offload_allowed=False
    ]


# --- 記憶準確率函數 (保持與原始檔案一致，但加入個體差異) ---
def get_internal_memory_accuracy(strategy_encoding_part: Tuple[bool, bool], params: EnhancedModelParameters) -> float:
    """原始的內部記憶準確率計算 (用於非人性化模擬)"""
    items_stored_internally = 0
    if strategy_encoding_part[0]:
        items_stored_internally += params.n_high_items
    if strategy_encoding_part[1]:
        items_stored_internally += params.n_low_items

    if items_stored_internally == 0:
        return 0.0
    elif items_stored_internally == 1:
        return params.accuracy_internal_1_item
    elif items_stored_internally == 2:
        return params.accuracy_internal_2_items
    elif items_stored_internally == 3:
        return params.accuracy_internal_3_items
    elif items_stored_internally == (params.n_high_items + params.n_low_items):
        return params.accuracy_internal_6_items
    else:
        return params.accuracy_internal_6_items


def get_internal_memory_accuracy_individual(
    strategy_encoding_part: Tuple[bool, bool], 
    params: EnhancedModelParameters,
    working_memory_capacity: float
) -> float:
    """個體化的內部記憶準確率計算"""
    base_accuracy = get_internal_memory_accuracy(strategy_encoding_part, params)
    
    if base_accuracy == 0.0:
        return 0.0
    
    # 根據個體工作記憶容量調整
    capacity_factor = working_memory_capacity
    
    # 計算總項目數來判斷負荷
    items_stored = 0
    if strategy_encoding_part[0]:
        items_stored += params.n_high_items
    if strategy_encoding_part[1]:
        items_stored += params.n_low_items
    
    # 高負荷時工作記憶容量的影響更顯著
    if items_stored > 3:
        capacity_factor = capacity_factor ** 1.5
    
    adjusted_accuracy = base_accuracy * capacity_factor
    return np.clip(adjusted_accuracy, 0.0, 1.0)


def get_perceived_internal_success_rate(objective_internal_accuracy: float, params: EnhancedModelParameters) -> float:
    """原始的後設認知感知函數"""
    perceived_rate = objective_internal_accuracy + params.metacognitive_bias
    
    if params.confidence_noise_std > 0:
        perceived_rate += np.random.normal(0, params.confidence_noise_std)
    
    return np.clip(perceived_rate, 0, 1)


def get_perceived_internal_success_rate_human(
    objective_internal_accuracy: float, 
    params: EnhancedModelParameters, 
    agent: HumanCognitiveAgent
) -> float:
    """人性化的後設認知感知函數"""
    # 基礎感知 (原始後設認知)
    perceived_rate = objective_internal_accuracy + params.metacognitive_bias
    
    # 應用認知偏誤
    perceived_rate = apply_overconfidence_bias(perceived_rate, agent.overconfidence_factor)
    
    # 應用錨定偏誤
    if agent.anchor_accuracy is not None:
        perceived_rate = apply_anchoring_bias(perceived_rate, agent.anchor_accuracy, params.anchoring_weight)
    
    # 應用可得性啟發
    if len(agent.experience_buffer) > 0:
        availability_estimate = apply_availability_heuristic(
            agent.experience_buffer, params.availability_window, params.availability_weight
        )
        perceived_rate = 0.7 * perceived_rate + 0.3 * availability_estimate
    
    # 加入個體化噪音 (處理速度影響)
    if params.confidence_noise_std > 0:
        noise_factor = params.confidence_noise_std / agent.processing_speed
        perceived_rate += np.random.normal(0, noise_factor)
    
    return np.clip(perceived_rate, 0, 1)


# --- 策略評估函數 ---
def evaluate_strategy_subjective(strategy: Strategy, params: EnhancedModelParameters) -> float:
    """原始的主觀策略評估 (用於非人性化模擬)"""
    store_H_internal, store_L_internal, offload_H_allowed, offload_L_allowed = strategy
    
    objective_p_internal_actual = get_internal_memory_accuracy((store_H_internal, store_L_internal), params)
    
    rewards_this_strategy_subjective = []
    
    for _ in range(params.n_episodes_per_strategy_eval):
        perceived_p_internal = get_perceived_internal_success_rate(objective_p_internal_actual, params)
        
        current_subjective_expected_reward = 0.0
        
        # 高價值項目評估
        cost_H_offload = params.n_high_items * params.cost_offloading if offload_H_allowed else 0.0
        
        if store_H_internal and offload_H_allowed:
            subjective_prob_remember_H = 1.0 - (1.0 - perceived_p_internal) * (1.0 - params.accuracy_offloaded)
            current_subjective_expected_reward += params.n_high_items * params.value_high * subjective_prob_remember_H
            current_subjective_expected_reward -= cost_H_offload
        elif store_H_internal:
            current_subjective_expected_reward += params.n_high_items * params.value_high * perceived_p_internal
        elif offload_H_allowed:
            current_subjective_expected_reward += params.n_high_items * params.value_high * params.accuracy_offloaded
            current_subjective_expected_reward -= cost_H_offload
        
        # 低價值項目評估
        cost_L_offload = params.n_low_items * params.cost_offloading if offload_L_allowed else 0.0
        
        if store_L_internal and offload_L_allowed:
            subjective_prob_remember_L = 1.0 - (1.0 - perceived_p_internal) * (1.0 - params.accuracy_offloaded)
            current_subjective_expected_reward += params.n_low_items * params.value_low * subjective_prob_remember_L
            current_subjective_expected_reward -= cost_L_offload
        elif store_L_internal:
            current_subjective_expected_reward += params.n_low_items * params.value_low * perceived_p_internal
        elif offload_L_allowed:
            current_subjective_expected_reward += params.n_low_items * params.value_low * params.accuracy_offloaded
            current_subjective_expected_reward -= cost_L_offload
        
        rewards_this_strategy_subjective.append(current_subjective_expected_reward)
    
    return np.mean(rewards_this_strategy_subjective)


def evaluate_strategy_subjective_human(
    strategy: Strategy, 
    params: EnhancedModelParameters, 
    agent: HumanCognitiveAgent
) -> float:
    """人性化的主觀策略評估"""
    store_H_internal, store_L_internal, offload_H_allowed, offload_L_allowed = strategy
    
    # 獲取個體化的內部記憶準確率
    objective_p_internal_actual = get_internal_memory_accuracy_individual(
        (store_H_internal, store_L_internal), params, agent.working_memory_capacity
    )
    
    rewards_this_strategy_subjective = []
    
    for _ in range(params.n_episodes_per_strategy_eval):
        # 使用人性化的感知函數
        perceived_p_internal = get_perceived_internal_success_rate_human(
            objective_p_internal_actual, params, agent
        )
        
        current_subjective_expected_reward = 0.0
        
        # 高價值項目評估
        cost_H_offload = params.n_high_items * params.cost_offloading if offload_H_allowed else 0.0
        
        if store_H_internal and offload_H_allowed:
            subjective_prob_remember_H = 1.0 - (1.0 - perceived_p_internal) * (1.0 - params.accuracy_offloaded)
            current_subjective_expected_reward += params.n_high_items * params.value_high * subjective_prob_remember_H
            current_subjective_expected_reward -= cost_H_offload
        elif store_H_internal:
            current_subjective_expected_reward += params.n_high_items * params.value_high * perceived_p_internal
        elif offload_H_allowed:
            current_subjective_expected_reward += params.n_high_items * params.value_high * params.accuracy_offloaded
            current_subjective_expected_reward -= cost_H_offload
        
        # 低價值項目評估
        cost_L_offload = params.n_low_items * params.cost_offloading if offload_L_allowed else 0.0
        
        if store_L_internal and offload_L_allowed:
            subjective_prob_remember_L = 1.0 - (1.0 - perceived_p_internal) * (1.0 - params.accuracy_offloaded)
            current_subjective_expected_reward += params.n_low_items * params.value_low * subjective_prob_remember_L
            current_subjective_expected_reward -= cost_L_offload
        elif store_L_internal:
            current_subjective_expected_reward += params.n_low_items * params.value_low * perceived_p_internal
        elif offload_L_allowed:
            current_subjective_expected_reward += params.n_low_items * params.value_low * params.accuracy_offloaded
            current_subjective_expected_reward -= cost_L_offload
        
        # 應用損失規避於獎勵評估
        reference_reward = agent.anchor_reward if agent.anchor_reward is not None else 0.0
        biased_reward = apply_loss_aversion(
            current_subjective_expected_reward, agent.loss_aversion_coefficient, reference_reward
        )
        
        # 風險容忍度調整
        if agent.risk_tolerance < 0.5:  # 風險規避
            uncertainty_penalty = (0.5 - agent.risk_tolerance) * params.confidence_noise_std
            biased_reward -= uncertainty_penalty
        else:  # 風險尋求
            uncertainty_bonus = (agent.risk_tolerance - 0.5) * 0.1
            biased_reward += uncertainty_bonus
        
        rewards_this_strategy_subjective.append(biased_reward)
    
    return np.mean(rewards_this_strategy_subjective)


# --- 試驗模擬函數 (保持與原始檔案一致的邏輯) ---
def simulate_trial(params: EnhancedModelParameters, strategy: Strategy) -> Tuple[float, int, int, int, int, int, int]:
    """原始的試驗模擬函數 (包含後設認知)"""
    total_reward = 0.0
    high_value_hits_normal_count = 0
    low_value_hits_normal_count = 0
    high_value_hits_surprise_count = 0
    low_value_hits_surprise_count = 0
    actual_offloaded_H_count = 0
    actual_offloaded_L_count = 0
    
    p_internal_actual = get_internal_memory_accuracy((strategy[0], strategy[1]), params)
    
    # 處理高價值項目
    confidence_H_basis = p_internal_actual if strategy[0] else 0.0
    
    for _ in range(params.n_high_items):
        decided_to_offload_this_H_item = False
        if strategy[2]:
            perceived_confidence = get_perceived_internal_success_rate(confidence_H_basis, params)
            if perceived_confidence < params.metacognitive_threshold:
                decided_to_offload_this_H_item = True
        
        item_recalled_H_normal = False
        item_recalled_H_surprise = False
        
        if decided_to_offload_this_H_item:
            actual_offloaded_H_count += 1
            if np.random.rand() < params.accuracy_offloaded:
                item_recalled_H_normal = True
        else:
            if strategy[0]:
                if np.random.rand() < p_internal_actual:
                    item_recalled_H_normal = True
                    item_recalled_H_surprise = True
        
        if item_recalled_H_normal:
            high_value_hits_normal_count += 1
            total_reward += params.value_high
        
        if item_recalled_H_surprise:
            high_value_hits_surprise_count += 1
    
    # 處理低價值項目
    confidence_L_basis = p_internal_actual if strategy[1] else 0.0
    
    for _ in range(params.n_low_items):
        decided_to_offload_this_L_item = False
        if strategy[3]:
            perceived_confidence_L = get_perceived_internal_success_rate(confidence_L_basis, params)
            if perceived_confidence_L < params.metacognitive_threshold:
                decided_to_offload_this_L_item = True
        
        item_recalled_L_normal = False
        item_recalled_L_surprise = False
        
        if decided_to_offload_this_L_item:
            actual_offloaded_L_count += 1
            if np.random.rand() < params.accuracy_offloaded:
                item_recalled_L_normal = True
        else:
            if strategy[1]:
                if np.random.rand() < p_internal_actual:
                    item_recalled_L_normal = True
                    item_recalled_L_surprise = True
        
        if item_recalled_L_normal:
            low_value_hits_normal_count += 1
            total_reward += params.value_low
        
        if item_recalled_L_surprise:
            low_value_hits_surprise_count += 1
    
    # 計算卸載成本
    total_reward -= actual_offloaded_H_count * params.cost_offloading
    total_reward -= actual_offloaded_L_count * params.cost_offloading
    
    return (total_reward, high_value_hits_normal_count, low_value_hits_normal_count, 
            high_value_hits_surprise_count, low_value_hits_surprise_count, 
            actual_offloaded_H_count, actual_offloaded_L_count)


def simulate_trial_human(
    params: EnhancedModelParameters, 
    strategy: Strategy, 
    agent: HumanCognitiveAgent
) -> Tuple[float, int, int, int, int, int, int, float]:
    """人性化的試驗模擬函數"""
    total_reward = 0.0
    high_value_hits_normal_count = 0
    low_value_hits_normal_count = 0
    high_value_hits_surprise_count = 0
    low_value_hits_surprise_count = 0
    actual_offloaded_H_count = 0
    actual_offloaded_L_count = 0
    
    # 使用個體化的內部記憶準確率
    p_internal_actual = get_internal_memory_accuracy_individual(
        (strategy[0], strategy[1]), params, agent.working_memory_capacity
    )
    
    # 處理速度影響決策雜訊
    decision_noise_factor = 1.0 / agent.processing_speed
    
    # 處理高價值項目
    confidence_H_basis = p_internal_actual if strategy[0] else 0.0
    
    for _ in range(params.n_high_items):
        decided_to_offload_this_H_item = False
        if strategy[2]:
            # 使用人性化的感知函數
            perceived_confidence = get_perceived_internal_success_rate_human(confidence_H_basis, params, agent)
            
            # 風險容忍度影響閾值
            adjusted_threshold = params.metacognitive_threshold
            if agent.risk_tolerance < 0.5:
                adjusted_threshold -= (0.5 - agent.risk_tolerance) * 0.2
            else:
                adjusted_threshold += (agent.risk_tolerance - 0.5) * 0.2
            
            if perceived_confidence < adjusted_threshold:
                decided_to_offload_this_H_item = True
        
        item_recalled_H_normal = False
        item_recalled_H_surprise = False
        
        if decided_to_offload_this_H_item:
            actual_offloaded_H_count += 1
            if np.random.rand() < params.accuracy_offloaded:
                item_recalled_H_normal = True
        else:
            if strategy[0]:
                if np.random.rand() < p_internal_actual:
                    item_recalled_H_normal = True
                    item_recalled_H_surprise = True
        
        if item_recalled_H_normal:
            high_value_hits_normal_count += 1
            total_reward += params.value_high
        
        if item_recalled_H_surprise:
            high_value_hits_surprise_count += 1
    
    # 處理低價值項目 (類似邏輯)
    confidence_L_basis = p_internal_actual if strategy[1] else 0.0
    
    for _ in range(params.n_low_items):
        decided_to_offload_this_L_item = False
        if strategy[3]:
            perceived_confidence_L = get_perceived_internal_success_rate_human(confidence_L_basis, params, agent)
            
            adjusted_threshold = params.metacognitive_threshold
            if agent.risk_tolerance < 0.5:
                adjusted_threshold -= (0.5 - agent.risk_tolerance) * 0.2
            else:
                adjusted_threshold += (agent.risk_tolerance - 0.5) * 0.2
            
            if perceived_confidence_L < adjusted_threshold:
                decided_to_offload_this_L_item = True
        
        item_recalled_L_normal = False
        item_recalled_L_surprise = False
        
        if decided_to_offload_this_L_item:
            actual_offloaded_L_count += 1
            if np.random.rand() < params.accuracy_offloaded:
                item_recalled_L_normal = True
        else:
            if strategy[1]:
                if np.random.rand() < p_internal_actual:
                    item_recalled_L_normal = True
                    item_recalled_L_surprise = True
        
        if item_recalled_L_normal:
            low_value_hits_normal_count += 1
            total_reward += params.value_low
        
        if item_recalled_L_surprise:
            low_value_hits_surprise_count += 1
    
    # 計算卸載成本
    total_reward -= actual_offloaded_H_count * params.cost_offloading
    total_reward -= actual_offloaded_L_count * params.cost_offloading
    
    # 計算成功率用於學習
    total_items = params.n_high_items + params.n_low_items
    total_hits = high_value_hits_normal_count + low_value_hits_normal_count
    success_rate = total_hits / total_items if total_items > 0 else 0
    
    return (total_reward, high_value_hits_normal_count, low_value_hits_normal_count, 
            high_value_hits_surprise_count, low_value_hits_surprise_count, 
            actual_offloaded_H_count, actual_offloaded_L_count, success_rate)


# --- 學習相關函數 ---
def update_agent_experience(
    agent: HumanCognitiveAgent, 
    strategy: Strategy,
    actual_reward: float,
    success_rate: float,
    params: EnhancedModelParameters
):
    """更新代理的經驗和學習狀態"""
    # 添加經驗
    experience = {
        'strategy': strategy,
        'reward': actual_reward,
        'success': success_rate,
        'timestamp': len(agent.experience_buffer)
    }
    agent.experience_buffer.append(experience)
    
    # 更新策略偏好
    if strategy not in agent.strategy_preferences:
        agent.strategy_preferences[strategy] = 0.0
    
    current_preference = agent.strategy_preferences[strategy]
    agent.strategy_preferences[strategy] = (
        current_preference * (1 - params.learning_rate) + 
        actual_reward * params.learning_rate
    )
    
    # 設定錨點
    if agent.anchor_accuracy is None:
        agent.anchor_accuracy = success_rate
    if agent.anchor_reward is None:
        agent.anchor_reward = actual_reward
    
    # 更新信心歷史
    agent.confidence_history.append(success_rate)
    if len(agent.confidence_history) > 100:
        agent.confidence_history = agent.confidence_history[-100:]


def select_strategy_with_learning(
    agent: HumanCognitiveAgent,
    strategies: List[Strategy],
    params: EnhancedModelParameters
) -> Strategy:
    """基於學習經驗的策略選擇"""
    # ε-貪婪策略選擇
    if np.random.random() < params.exploration_rate:
        return random.choice(strategies)
    else:
        if not agent.strategy_preferences:
            return random.choice(strategies)
        
        # 選擇最佳已知策略
        available_preferences = {s: pref for s, pref in agent.strategy_preferences.items() if s in strategies}
        
        if available_preferences:
            best_strategy = max(available_preferences.keys(), key=lambda s: available_preferences[s])
            return best_strategy
        else:
            return random.choice(strategies)


# --- 保持原始的模擬函數 (不含人性化特徵) ---
def simulate_trial_original(params: EnhancedModelParameters, strategy: Strategy) -> Tuple[float, int, int, int, int, int, int]:
    """原始的試驗模擬函數 (不包含後設認知)"""
    store_H_internal, store_L_internal, offload_H_allowed, offload_L_allowed = strategy
    
    total_reward = 0.0
    high_value_hits_normal_count = 0
    low_value_hits_normal_count = 0
    high_value_hits_surprise_count = 0
    low_value_hits_surprise_count = 0
    actual_offloaded_H_count = 0
    actual_offloaded_L_count = 0

    p_internal_actual = get_internal_memory_accuracy((store_H_internal, store_L_internal), params)

    # 處理高價值項目
    for _ in range(params.n_high_items):
        item_recalled_H_normal = False
        item_recalled_H_surprise = False

        if store_H_internal and offload_H_allowed:
            actual_offloaded_H_count += 1
            combined_success_prob = 1.0 - (1.0 - p_internal_actual) * (1.0 - params.accuracy_offloaded)
            if np.random.rand() < combined_success_prob:
                item_recalled_H_normal = True
            if np.random.rand() < p_internal_actual:
                item_recalled_H_surprise = True
        elif store_H_internal:
            if np.random.rand() < p_internal_actual:
                item_recalled_H_normal = True
                item_recalled_H_surprise = True
        elif offload_H_allowed:
            actual_offloaded_H_count += 1
            if np.random.rand() < params.accuracy_offloaded:
                item_recalled_H_normal = True

        if item_recalled_H_normal:
            high_value_hits_normal_count += 1
            total_reward += params.value_high

        if item_recalled_H_surprise:
            high_value_hits_surprise_count += 1

    # 處理低價值項目
    for _ in range(params.n_low_items):
        item_recalled_L_normal = False
        item_recalled_L_surprise = False

        if store_L_internal and offload_L_allowed:
            actual_offloaded_L_count += 1
            combined_success_prob = 1.0 - (1.0 - p_internal_actual) * (1.0 - params.accuracy_offloaded)
            if np.random.rand() < combined_success_prob:
                item_recalled_L_normal = True
            if np.random.rand() < p_internal_actual:
                item_recalled_L_surprise = True
        elif store_L_internal:
            if np.random.rand() < p_internal_actual:
                item_recalled_L_normal = True
                item_recalled_L_surprise = True
        elif offload_L_allowed:
            actual_offloaded_L_count += 1
            if np.random.rand() < params.accuracy_offloaded:
                item_recalled_L_normal = True

        if item_recalled_L_normal:
            low_value_hits_normal_count += 1
            total_reward += params.value_low

        if item_recalled_L_surprise:
            low_value_hits_surprise_count += 1

    total_reward -= actual_offloaded_H_count * params.cost_offloading
    total_reward -= actual_offloaded_L_count * params.cost_offloading

    return (total_reward, high_value_hits_normal_count, low_value_hits_normal_count, 
            high_value_hits_surprise_count, low_value_hits_surprise_count, 
            actual_offloaded_H_count, actual_offloaded_L_count)


# --- 主要模擬函數 ---
def run_simulation_enhanced(params: EnhancedModelParameters, strategies: List[Strategy]) -> Dict[str, Any]:
    """
    增強的模擬函數，可選擇是否啟用人性化特徵
    """
    if not params.enable_human_features:
        # 使用原始後設認知模擬
        return run_simulation_with_metacognition_original(params, strategies)
    else:
        # 使用人性化模擬
        return run_simulation_with_human_features(params, strategies)


def run_simulation_with_metacognition_original(params: EnhancedModelParameters, strategies: List[Strategy]) -> Dict[str, Any]:
    """原始的後設認知模擬 (不含人性化特徵)"""
    print(f"運行原始後設認知模擬 ({params.n_model_runs} runs, {params.n_episodes_per_strategy_eval} episodes per run)...")

    overall_best_strategy_counts: Dict[Strategy, int] = {s: 0 for s in strategies}
    cumulative_best_strategy_total_hits_H_normal = 0
    cumulative_best_strategy_total_hits_L_normal = 0
    cumulative_best_strategy_total_hits_H_surprise = 0
    cumulative_best_strategy_total_hits_L_surprise = 0
    cumulative_best_strategy_total_offloaded_H = 0
    cumulative_best_strategy_total_offloaded_L = 0
    cumulative_best_strategy_total_episodes = 0
    successful_runs_count = 0

    for i_run in range(params.n_model_runs):
        if (i_run + 1) % (max(1, params.n_model_runs // 100)) == 0:
            print(f"  運行中 (後設認知)... {((i_run + 1) / params.n_model_runs) * 100:.0f}%")

        total_reward_per_strategy_this_run: Dict[Strategy, float] = defaultdict(float)
        sample_count_per_strategy_this_run: Dict[Strategy, int] = defaultdict(int)
        total_hits_H_normal_per_strategy_this_run: Dict[Strategy, int] = defaultdict(int)
        total_hits_L_normal_per_strategy_this_run: Dict[Strategy, int] = defaultdict(int)
        total_hits_H_surprise_per_strategy_this_run: Dict[Strategy, int] = defaultdict(int)
        total_hits_L_surprise_per_strategy_this_run: Dict[Strategy, int] = defaultdict(int)
        total_offloaded_H_per_strategy_this_run: Dict[Strategy, int] = defaultdict(int)
        total_offloaded_L_per_strategy_this_run: Dict[Strategy, int] = defaultdict(int)

        for i_episode in range(params.n_episodes_per_strategy_eval):
            # 計算每個策略的主觀預期獎勵
            strategy_subjective_rewards = {}
            for strategy in strategies:
                strategy_subjective_rewards[strategy] = evaluate_strategy_subjective(strategy, params)
            
            # 選擇主觀預期獎勵最高的策略
            best_subjective_strategy = max(strategy_subjective_rewards.keys(), 
                                         key=lambda s: strategy_subjective_rewards[s])
            
            # 使用包含後設認知的 simulate_trial
            reward, hits_H_norm, hits_L_norm, hits_H_surp, hits_L_surp, actual_offloaded_H, actual_offloaded_L = simulate_trial(params, best_subjective_strategy)

            total_reward_per_strategy_this_run[best_subjective_strategy] += reward
            sample_count_per_strategy_this_run[best_subjective_strategy] += 1
            total_hits_H_normal_per_strategy_this_run[best_subjective_strategy] += hits_H_norm
            total_hits_L_normal_per_strategy_this_run[best_subjective_strategy] += hits_L_norm
            total_hits_H_surprise_per_strategy_this_run[best_subjective_strategy] += hits_H_surp
            total_hits_L_surprise_per_strategy_this_run[best_subjective_strategy] += hits_L_surp
            total_offloaded_H_per_strategy_this_run[best_subjective_strategy] += actual_offloaded_H
            total_offloaded_L_per_strategy_this_run[best_subjective_strategy] += actual_offloaded_L

        mean_rewards_this_run: Dict[Strategy, float] = {}
        max_avg_reward_this_run = -float('inf')
        strategies_sampled_this_run = [s for s in strategies if sample_count_per_strategy_this_run[s] > 0]

        if not strategies_sampled_this_run:
            continue

        for strategy in strategies_sampled_this_run:
            if sample_count_per_strategy_this_run[strategy] > 0:
                mean_reward = total_reward_per_strategy_this_run[strategy] / sample_count_per_strategy_this_run[strategy]
                mean_rewards_this_run[strategy] = mean_reward
                if mean_reward > max_avg_reward_this_run:
                    max_avg_reward_this_run = mean_reward

        tolerance = 1e-9
        best_strategies_this_run = [
            s for s, avg_r in mean_rewards_this_run.items() if abs(avg_r - max_avg_reward_this_run) < tolerance
        ]

        best_strategy_for_this_run = random.choice(best_strategies_this_run)
        overall_best_strategy_counts[best_strategy_for_this_run] += 1

        samples_best_strategy_this_run = sample_count_per_strategy_this_run[best_strategy_for_this_run]

        if samples_best_strategy_this_run > 0:
            cumulative_best_strategy_total_hits_H_normal += total_hits_H_normal_per_strategy_this_run[best_strategy_for_this_run]
            cumulative_best_strategy_total_hits_L_normal += total_hits_L_normal_per_strategy_this_run[best_strategy_for_this_run]
            cumulative_best_strategy_total_hits_H_surprise += total_hits_H_surprise_per_strategy_this_run[best_strategy_for_this_run]
            cumulative_best_strategy_total_hits_L_surprise += total_hits_L_surprise_per_strategy_this_run[best_strategy_for this_run]
            cumulative_best_strategy_total_offloaded_H += total_offloaded_H_per_strategy_this_run[best_strategy_for_this_run]
            cumulative_best_strategy_total_offloaded_L += total_offloaded_L_per_strategy_this_run[best_strategy_for_this_run]
            cumulative_best_strategy_total_episodes += samples_best_strategy_this_run
            successful_runs_count += 1

    total_item_opportunities_H = cumulative_best_strategy_total_episodes * params.n_high_items
    total_item_opportunities_L = cumulative_best_strategy_total_episodes * params.n_low_items

    mean_empirical_accuracy_H_normal = cumulative_best_strategy_total_hits_H_normal / total_item_opportunities_H if total_item_opportunities_H > 0 else 0.0
    mean_empirical_accuracy_L_normal = cumulative_best_strategy_total_hits_L_normal / total_item_opportunities_L if total_item_opportunities_L > 0 else 0.0
    mean_empirical_accuracy_H_surprise = cumulative_best_strategy_total_hits_H_surprise / total_item_opportunities_H if total_item_opportunities_H > 0 else 0.0
    mean_empirical_accuracy_L_surprise = cumulative_best_strategy_total_hits_L_surprise / total_item_opportunities_L if total_item_opportunities_L > 0 else 0.0
    mean_empirical_offload_H = cumulative_best_strategy_total_offloaded_H / total_item_opportunities_H if total_item_opportunities_H > 0 else 0.0
    mean_empirical_offload_L = cumulative_best_strategy_total_offloaded_L / total_item_opportunities_L if total_item_opportunities_L > 0 else 0.0

    strategy_proportions: Dict[Strategy, float] = {
        s: overall_best_strategy_counts[s] / successful_runs_count if successful_runs_count > 0 else 0.0 for s in strategies
    }

    return {
        "strategy_proportions": strategy_proportions,
        "mean_accuracy_H": mean_empirical_accuracy_H_normal,
        "mean_accuracy_L": mean_empirical_accuracy_L_normal,
        "mean_accuracy_H_surprise": mean_empirical_accuracy_H_surprise,
        "mean_accuracy_L_surprise": mean_empirical_accuracy_L_surprise,
        "mean_offload_H": mean_empirical_offload_H,
        "mean_offload_L": mean_empirical_offload_L,
        "overall_best_strategy_counts": overall_best_strategy_counts
    }


def run_simulation_with_human_features(params: EnhancedModelParameters, strategies: List[Strategy]) -> Dict[str, Any]:
    """人性化特徵的模擬"""
    print(f"運行人性化模擬 ({params.population_size} 個代理, {params.n_model_runs} runs)...")
    
    # 創建多樣化代理人群
    agents = create_diverse_population(params.population_size, params)
    
    # 追蹤結果
    overall_results = {
        'strategy_proportions': defaultdict(float),
        'agent_performances': [],
        'individual_differences_effects': {
            'working_memory': [],
            'risk_tolerance': [],
            'loss_aversion': [],
            'overconfidence': []
        }
    }
    
    for run_idx in range(params.n_model_runs):
        if (run_idx + 1) % (max(1, params.n_model_runs // 10)) == 0:
            print(f"  進度 (人性化): {((run_idx + 1) / params.n_model_runs) * 100:.0f}%")
        
        run_results = {}
        
        for agent in agents:
            agent_rewards = []
            agent_strategies = []
            
            for episode in range(params.n_episodes_per_strategy_eval):
                # 選擇策略
                if len(agent.strategy_preferences) > 0 and np.random.random() > params.exploration_rate:
                    # 基於學習選擇
                    chosen_strategy = select_strategy_with_learning(agent, strategies, params)
                else:
                    # 基於主觀評估選擇
                    strategy_subjective_rewards = {}
                    for strategy in strategies:
                        strategy_subjective_rewards[strategy] = evaluate_strategy_subjective_human(strategy, params, agent)
                    
                    chosen_strategy = max(strategy_subjective_rewards.keys(), 
                                        key=lambda s: strategy_subjective_rewards[s])
                
                # 模擬試驗
                result = simulate_trial_human(params, chosen_strategy, agent)
                total_reward, _, _, _, _, _, _, success_rate = result
                
                # 更新代理經驗
                update_agent_experience(agent, chosen_strategy, total_reward, success_rate, params)
                
                agent_rewards.append(total_reward)
                agent_strategies.append(chosen_strategy)
            
            # 記錄代理表現
            best_strategy = max(set(agent_strategies), key=agent_strategies.count)
            run_results[agent.agent_id] = {
                'best_strategy': best_strategy,
                'mean_reward': np.mean(agent_rewards),
                'agent_characteristics': {
                    'working_memory': agent.working_memory_capacity,
                    'risk_tolerance': agent.risk_tolerance,
                    'loss_aversion': agent.loss_aversion_coefficient,
                    'overconfidence': agent.overconfidence_factor
                }
            }
        
        # 聚合結果
        strategy_counts = defaultdict(int)
        total_agents = len(agents)
        
        for agent_id, agent_result in run_results.items():
            strategy_counts[agent_result['best_strategy']] += 1
        
        # 更新總體比例
        for strategy, count in strategy_counts.items():
            overall_results['strategy_proportions'][strategy] += count / total_agents / params.n_model_runs
        
        # 儲存個體表現數據
        overall_results['agent_performances'].extend(run_results.values())
    
    # 分析個體差異效應
    for performance in overall_results['agent_performances']:
        chars = performance['agent_characteristics']
        reward = performance['mean_reward']
        
        overall_results['individual_differences_effects']['working_memory'].append(
            (chars['working_memory'], reward)
        )
        overall_results['individual_differences_effects']['risk_tolerance'].append(
            (chars['risk_tolerance'], reward)
        )
        overall_results['individual_differences_effects']['loss_aversion'].append(
            (chars['loss_aversion'], reward)
        )
        overall_results['individual_differences_effects']['overconfidence'].append(
            (chars['overconfidence'], reward)
        )
    
    return overall_results


# --- 保持所有原始 Fig.2-8 模擬函數 ---
def run_simulation_original(params: EnhancedModelParameters, strategies: List[Strategy]) -> Dict[str, Any]:
    """完全原始的模擬 (用於 Fig.2-8)"""
    print(f"運行原始模擬 ({params.n_model_runs} runs, {params.n_episodes_per_strategy_eval} episodes per run)...")

    overall_best_strategy_counts: Dict[Strategy, int] = {s: 0 for s in strategies}
    cumulative_best_strategy_total_hits_H_normal = 0
    cumulative_best_strategy_total_hits_L_normal = 0
    cumulative_best_strategy_total_hits_H_surprise = 0
    cumulative_best_strategy_total_hits_L_surprise = 0
    cumulative_best_strategy_total_offloaded_H = 0
    cumulative_best_strategy_total_offloaded_L = 0
    cumulative_best_strategy_total_episodes = 0
    successful_runs_count = 0

    for i_run in range(params.n_model_runs):
        if (i_run + 1) % (max(1, params.n_model_runs // 100)) == 0:
            print(f"  運行中 (原始)... {((i_run + 1) / params.n_model_runs) * 100:.0f}%")

        total_reward_per_strategy_this_run: Dict[Strategy, float] = defaultdict(float)
        sample_count_per_strategy_this_run: Dict[Strategy, int] = defaultdict(int)
        total_hits_H_normal_per_strategy_this_run: Dict[Strategy, int] = defaultdict(int)
        total_hits_L_normal_per_strategy_this_run: Dict[Strategy, int] = defaultdict(int)
        total_hits_H_surprise_per_strategy_this_run: Dict[Strategy, int] = defaultdict(int)
        total_hits_L_surprise_per_strategy_this_run: Dict[Strategy, int] = defaultdict(int)
        total_offloaded_H_per_strategy_this_run: Dict[Strategy, int] = defaultdict(int)
        total_offloaded_L_per_strategy_this_run: Dict[Strategy, int] = defaultdict(int)

        for i_episode in range(params.n_episodes_per_strategy_eval):
            chosen_strategy = random.choice(strategies)
            
            reward, hits_H_norm, hits_L_norm, hits_H_surp, hits_L_surp, actual_offloaded_H, actual_offloaded_L = simulate_trial_original(params, chosen_strategy)

            total_reward_per_strategy_this_run[chosen_strategy] += reward
            sample_count_per_strategy_this_run[chosen_strategy] += 1
            total_hits_H_normal_per_strategy_this_run[chosen_strategy] += hits_H_norm
            total_hits_L_normal_per_strategy_this_run[chosen_strategy] += hits_L_norm
            total_hits_H_surprise_per_strategy_this_run[chosen_strategy] += hits_H_surp
            total_hits_L_surprise_per_strategy_this_run[chosen_strategy] += hits_L_surp
            total_offloaded_H_per_strategy_this_run[chosen_strategy] += actual_offloaded_H
            total_offloaded_L_per_strategy_this_run[chosen_strategy] += actual_offloaded_L

        mean_rewards_this_run: Dict[Strategy, float] = {}
        max_avg_reward_this_run = -float('inf')
        strategies_sampled_this_run = [s for s in strategies if sample_count_per_strategy_this_run[s] > 0]

        if not strategies_sampled_this_run:
            continue

        for strategy in strategies_sampled_this_run:
            if sample_count_per_strategy_this_run[strategy] > 0:
                mean_reward = total_reward_per_strategy_this_run[strategy] / sample_count_per_strategy_this_run[strategy]
                mean_rewards_this_run[strategy] = mean_reward
                if mean_reward > max_avg_reward_this_run:
                    max_avg_reward_this_run = mean_reward

        tolerance = 1e-9
        best_strategies_this_run = [
            s for s, avg_r in mean_rewards_this_run.items() if abs(avg_r - max_avg_reward_this_run) < tolerance
        ]

        best_strategy_for_this_run = random.choice(best_strategies_this_run)
        overall_best_strategy_counts[best_strategy_for_this_run] += 1

        samples_best_strategy_this_run = sample_count_per_strategy_this_run[best_strategy_for_this_run]

        if samples_best_strategy_this_run > 0:
            cumulative_best_strategy_total_hits_H_normal += total_hits_H_normal_per_strategy_this_run[best_strategy_for_this_run]
            cumulative_best_strategy_total_hits_L_normal += total_hits_L_normal_per_strategy_this_run[best_strategy_for_this_run]
            cumulative_best_strategy_total_hits_H_surprise += total_hits_H_surprise_per_strategy_this_run[best_strategy_for_this_run]
            cumulative_best_strategy_total_hits_L_surprise += total_hits_L_surprise_per_strategy_this_run[best_strategy_for this_run]
            cumulative_best_strategy_total_offloaded_H += total_offloaded_H_per_strategy_this_run[best_strategy_for_this_run]
            cumulative_best_strategy_total_offloaded_L += total_offloaded_L_per_strategy_this_run[best_strategy_for_this_run]
            cumulative_best_strategy_total_episodes += samples_best_strategy_this_run
            successful_runs_count += 1

    total_item_opportunities_H = cumulative_best_strategy_total_episodes * params.n_high_items
    total_item_opportunities_L = cumulative_best_strategy_total_episodes * params.n_low_items

    mean_empirical_accuracy_H_normal = cumulative_best_strategy_total_hits_H_normal / total_item_opportunities_H if total_item_opportunities_H > 0 else 0.0
    mean_empirical_accuracy_L_normal = cumulative_best_strategy_total_hits_L_normal / total_item_opportunities_L if total_item_opportunities_L > 0 else 0.0
    mean_empirical_accuracy_H_surprise = cumulative_best_strategy_total_hits_H_surprise / total_item_opportunities_H if total_item_opportunities_H > 0 else 0.0
    mean_empirical_accuracy_L_surprise = cumulative_best_strategy_total_hits_L_surprise / total_item_opportunities_L if total_item_opportunities_L > 0 else 0.0
    mean_empirical_offload_H = cumulative_best_strategy_total_offloaded_H / total_item_opportunities_H if total_item_opportunities_H > 0 else 0.0
    mean_empirical_offload_L = cumulative_best_strategy_total_offloaded_L / total_item_opportunities_L if total_item_opportunities_L > 0 else 0.0

    strategy_proportions: Dict[Strategy, float] = {
        s: overall_best_strategy_counts[s] / successful_runs_count if successful_runs_count > 0 else 0.0 for s in strategies
    }

    return {
        "strategy_proportions": strategy_proportions,
        "mean_accuracy_H": mean_empirical_accuracy_H_normal,
        "mean_accuracy_L": mean_empirical_accuracy_L_normal,
        "mean_accuracy_H_surprise": mean_empirical_accuracy_H_surprise,
        "mean_accuracy_L_surprise": mean_empirical_accuracy_L_surprise,
        "mean_offload_H": mean_empirical_offload_H,
        "mean_offload_L": mean_empirical_offload_L,
        "overall_best_strategy_counts": overall_best_strategy_counts
    }


# --- Fig.2-8 模擬函數 (保持完全一致) ---
def simulate_fig2(base_params: EnhancedModelParameters):
    print(f"\n開始模擬 Fig. 2 (不允許考慮卸載)...")
    params_fig2 = replace(base_params, n_high_items=3, n_low_items=3)
    strategies_fig2 = get_all_strategies(allow_offloading=False)
    results = run_simulation_original(params_fig2, strategies_fig2)

    prop_encode_low = sum(prop for s, prop in results["strategy_proportions"].items() if s[1])
    prop_encode_high = sum(prop for s, prop in results["strategy_proportions"].items() if s[0])
    acc_L_norm = results["mean_accuracy_L"]
    acc_H_norm = results["mean_accuracy_H"]

    print(f"Fig. 2 結果 (不允許考慮卸載): ")
    print(f"  策略 - 編碼低價值比例: {prop_encode_low:.3f}")
    print(f"  策略 - 編碼高價值比例: {prop_encode_high:.3f}")
    print(f"  正常準確率 - 低價值: {acc_L_norm:.3f}")
    print(f"  正常準確率 - 高價值: {acc_H_norm:.3f}")

    fig, axs = plt.subplots(1, 2, figsize=(10, 4))
    fig.suptitle(f"Fig. 2 Simulation: No Offloading Allowed", fontsize=14)
    axs[0].bar(["Encode\nlow-value", "Encode\nhigh-value"], [prop_encode_low, prop_encode_high], color='cornflowerblue')
    axs[0].set_title("Strategy ")
    axs[0].set_ylabel("Proportion of Runs Choosing Strategy")
    axs[0].set_ylim(0, 1.05)
    axs[1].bar(["Low-value", "High-value"], [acc_L_norm, acc_H_norm], color='cornflowerblue')
    axs[1].set_title("Mean Empirical Normal Accuracy ")
    axs[1].set_ylabel("Accuracy")
    axs[1].set_ylim(0, 1.05)
    plt.tight_layout(rect=[0, 0, 1, 0.96])
    plt.show()

    return results


# 繼續完成現有項目的修改...

# 補充缺失的 simulate_fig3 函數
def simulate_fig3(base_params: EnhancedModelParameters):
    print(f"\n開始模擬 Fig. 3 (允許考慮卸載)...")
    params_fig3 = replace(base_params, n_high_items=3, n_low_items=3)
    strategies_fig3 = get_all_strategies(allow_offloading=True)
    results = run_simulation_enhanced(params_fig3, strategies_fig3)

    # 分析策略選擇模式
    top_strategies = sorted(results["strategy_proportions"].items(), 
                           key=lambda x: x[1], reverse=True)[:5]
    
    print(f"Fig. 3 結果 (允許考慮卸載): ")
    print("  前5名策略選擇比例:")
    for i, (strategy, proportion) in enumerate(top_strategies, 1):
        store_H, store_L, offload_H, offload_L = strategy
        strategy_desc = f"記憶H:{store_H}, 記憶L:{store_L}, 卸載H:{offload_H}, 卸載L:{offload_L}"
        print(f"    {i}. {strategy_desc}: {proportion:.3f}")
    
    print(f"  平均準確率 - 高價值: {results.get('mean_accuracy_H', 'N/A')}")
    print(f"  平均準確率 - 低價值: {results.get('mean_accuracy_L', 'N/A')}")
    
    # 可視化
    fig, axs = plt.subplots(1, 2, figsize=(12, 5))
    fig.suptitle("Fig. 3 Simulation: Offloading Allowed", fontsize=14)
    
    # 策略選擇分布
    strategy_labels = []
    strategy_props = []
    for strategy, prop in top_strategies:
        store_H, store_L, offload_H, offload_L = strategy
        label = f"({int(store_H)},{int(store_L)},{int(offload_H)},{int(offload_L)})"
        strategy_labels.append(label)
        strategy_props.append(prop)
    
    axs[0].bar(range(len(strategy_labels)), strategy_props, color='lightcoral')
    axs[0].set_title("Top 5 Strategy Proportions")
    axs[0].set_xlabel("Strategy (H_mem, L_mem, H_off, L_off)")
    axs[0].set_ylabel("Proportion")
    axs[0].set_xticks(range(len(strategy_labels)))
    axs[0].set_xticklabels(strategy_labels, rotation=45)
    
    # 記憶 vs 卸載偏好
    memory_focused = sum(prop for (s_h, s_l, o_h, o_l), prop in results["strategy_proportions"].items() 
                        if (s_h or s_l) and not (o_h or o_l))
    offload_focused = sum(prop for (s_h, s_l, o_h, o_l), prop in results["strategy_proportions"].items() 
                         if (o_h or o_l) and not (s_h or s_l))
    hybrid_focused = sum(prop for (s_h, s_l, o_h, o_l), prop in results["strategy_proportions"].items() 
                        if (s_h or s_l) and (o_h or o_l))
    
    axs[1].pie([memory_focused, offload_focused, hybrid_focused], 
               labels=['Memory Only', 'Offload Only', 'Hybrid'], 
               autopct='%1.1f%%', colors=['skyblue', 'lightgreen', 'orange'])
    axs[1].set_title("Strategy Type Distribution")
    
    plt.tight_layout()
    plt.show()
    
    return results


# 新增 Fig.4-8 的完整實現
def simulate_fig4(base_params: EnhancedModelParameters):
    """Fig.4: 高價值項目數量的影響"""
    print(f"\n開始模擬 Fig. 4 (高價值項目數量效應)...")
    
    high_item_counts = [1, 2, 3, 4, 5]
    results_by_count = {}
    
    for n_high in high_item_counts:
        print(f"  測試 {n_high} 個高價值項目...")
        params_fig4 = replace(base_params, n_high_items=n_high, n_low_items=3)
        strategies = get_all_strategies(allow_offloading=True)
        results = run_simulation_enhanced(params_fig4, strategies)
        results_by_count[n_high] = results
    
    # 分析結果
    memory_encoding_rates_high = []
    offloading_rates_high = []
    
    for n_high in high_item_counts:
        results = results_by_count[n_high]
        
        # 計算編碼高價值項目的比例
        encode_high_prop = sum(prop for (s_h, s_l, o_h, o_l), prop in results["strategy_proportions"].items() if s_h)
        memory_encoding_rates_high.append(encode_high_prop)
        
        # 計算卸載高價值項目的比例
        offload_high_prop = sum(prop for (s_h, s_l, o_h, o_l), prop in results["strategy_proportions"].items() if o_h)
        offloading_rates_high.append(offload_high_prop)
    
    # 可視化
    fig, axs = plt.subplots(1, 2, figsize=(12, 5))
    fig.suptitle("Fig. 4: Effect of Number of High-Value Items", fontsize=14)
    
    axs[0].plot(high_item_counts, memory_encoding_rates_high, 'bo-', label='High-value encoding')
    axs[0].set_xlabel('Number of High-Value Items')
    axs[0].set_ylabel('Proportion Choosing to Encode')
    axs[0].set_title('Memory Encoding Strategy')
    axs[0].grid(True, alpha=0.3)
    axs[0].legend()
    
    axs[1].plot(high_item_counts, offloading_rates_high, 'ro-', label='High-value offloading')
    axs[1].set_xlabel('Number of High-Value Items')
    axs[1].set_ylabel('Proportion Choosing to Offload')
    axs[1].set_title('Offloading Strategy')
    axs[1].grid(True, alpha=0.3)
    axs[1].legend()
    
    plt.tight_layout()
    plt.show()
    
    return results_by_count


def simulate_fig7(base_params: EnhancedModelParameters):
    """Fig.7: 卸載成本的影響 (增強版)"""
    print(f"\n開始模擬 Fig. 7 (卸載成本效應)...")
    
    costs = np.linspace(0.0, 2.0, 9)
    results_by_cost = {}
    
    for cost in costs:
        print(f"  測試成本 {cost:.1f}...")
        params_fig7 = replace(base_params, cost_offloading=cost)
        strategies = get_all_strategies(allow_offloading=True)
        results = run_simulation_enhanced(params_fig7, strategies)
        results_by_cost[cost] = results
    
    # 分析結果
    memory_encoding_rates_high = []
    memory_encoding_rates_low = []
    offloading_rates_high = []
    offloading_rates_low = []
    accuracy_normal_high = []
    accuracy_normal_low = []
    accuracy_surprise_high = []
    accuracy_surprise_low = []
    
    for cost in costs:
        results = results_by_cost[cost]
        
        # 策略選擇比例
        encode_high_prop = sum(prop for (s_h, s_l, o_h, o_l), prop in results["strategy_proportions"].items() if s_h)
        encode_low_prop = sum(prop for (s_h, s_l, o_h, o_l), prop in results["strategy_proportions"].items() if s_l)
        offload_high_prop = sum(prop for (s_h, s_l, o_h, o_l), prop in results["strategy_proportions"].items() if o_h)
        offload_low_prop = sum(prop for (s_h, s_l, o_h, o_l), prop in results["strategy_proportions"].items() if o_l)
        
        memory_encoding_rates_high.append(encode_high_prop)
        memory_encoding_rates_low.append(encode_low_prop)
        offloading_rates_high.append(offload_high_prop)
        offloading_rates_low.append(offload_low_prop)
        
        # 準確率
        accuracy_normal_high.append(results.get("mean_accuracy_H", 0))
        accuracy_normal_low.append(results.get("mean_accuracy_L", 0))
        accuracy_surprise_high.append(results.get("mean_accuracy_H_surprise", 0))
        accuracy_surprise_low.append(results.get("mean_accuracy_L_surprise", 0))
    
    # 可視化 (4個子圖)
    fig, axs = plt.subplots(2, 2, figsize=(12, 10))
    fig.suptitle("Fig. 7: Effect of Offloading Cost", fontsize=16)
    
    # 記憶編碼率
    axs[0,0].plot(costs, memory_encoding_rates_high, 'b-', marker='o', label='High-value')
    axs[0,0].plot(costs, memory_encoding_rates_low, 'r-', marker='s', label='Low-value')
    axs[0,0].set_xlabel('Offloading Cost')
    axs[0,0].set_ylabel('Memory Encoding Rate')
    axs[0,0].set_title('(a) Memory Encoding')
    axs[0,0].legend()
    axs[0,0].grid(True, alpha=0.3)
    
    # 卸載率
    axs[0,1].plot(costs, offloading_rates_high, 'b-', marker='o', label='High-value')
    axs[0,1].plot(costs, offloading_rates_low, 'r-', marker='s', label='Low-value')
    axs[0,1].set_xlabel('Offloading Cost')
    axs[0,1].set_ylabel('Offloading Rate')
    axs[0,1].set_title('(b) Offloading')
    axs[0,1].legend()
    axs[0,1].grid(True, alpha=0.3)
    
    # 正常測試準確率
    axs[1,0].plot(costs, accuracy_normal_high, 'b-', marker='o', label='High-value')
    axs[1,0].plot(costs, accuracy_normal_low, 'r-', marker='s', label='Low-value')
    axs[1,0].set_xlabel('Offloading Cost')
    axs[1,0].set_ylabel('Accuracy (Normal Test)')
    axs[1,0].set_title('(c) Normal Test Accuracy')
    axs[1,0].legend()
    axs[1,0].grid(True, alpha=0.3)
    
    # 意外測試準確率
    axs[1,1].plot(costs, accuracy_surprise_high, 'b-', marker='o', label='High-value')
    axs[1,1].plot(costs, accuracy_surprise_low, 'r-', marker='s', label='Low-value')
    axs[1,1].set_xlabel('Offloading Cost')
    axs[1,1].set_ylabel('Accuracy (Surprise Test)')
    axs[1,1].set_title('(d) Surprise Test Accuracy')
    axs[1,1].legend()
    axs[1,1].grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()
    
    return results_by_cost


def simulate_fig8(base_params: EnhancedModelParameters):
    """Fig.8: 內部記憶準確率的影響"""
    print(f"\n開始模擬 Fig. 8 (內部記憶準確率效應)...")
    
    # 測試不同的內部記憶準確率 (6項目)
    internal_accuracies = np.linspace(0.55, 0.95, 9)
    costs = [1.0, 2.0]  # 兩種成本條件
    
    results_by_accuracy_and_cost = {}
    
    for cost in costs:
        results_by_accuracy_and_cost[cost] = {}
        print(f"  測試成本 {cost}...")
        
        for acc in internal_accuracies:
            print(f"    內部記憶準確率: {acc:.2f}")
            params_fig8 = replace(base_params, 
                                 accuracy_internal_6_items=acc,
                                 cost_offloading=cost)
            
            # 使用 Fig.8 專用策略
            strategies = get_strategies_fig8()
            results = run_simulation_fig8_specific(params_fig8, strategies)
            results_by_accuracy_and_cost[cost][acc] = results
    
    # 分析和可視化
    fig, axs = plt.subplots(2, 2, figsize=(12, 10))
    fig.suptitle("Fig. 8: Effect of Internal Memory Accuracy", fontsize=16)
    
    colors = {'1.0': 'blue', '2.0': 'red'}
    markers = {'1.0': 'o', '2.0': 's'}
    
    for i, cost in enumerate(costs):
        cost_str = str(float(cost))
        
        # 提取數據
        encode_rates = []
        offload_rates = []
        
        for acc in internal_accuracies:
            results = results_by_accuracy_and_cost[cost][acc]
            
            # 編碼率 (encode=True)
            encode_rate = sum(prop for (encode, offload), prop in results["strategy_proportions"].items() if encode)
            encode_rates.append(encode_rate)
            
            # 卸載率 (offload_allowed=True)
            offload_rate = sum(prop for (encode, offload), prop in results["strategy_proportions"].items() if offload)
            offload_rates.append(offload_rate)
        
        # 繪圖
        axs[0,0].plot(internal_accuracies, encode_rates, 
                     color=colors[cost_str], marker=markers[cost_str], 
                     label=f'Cost = {cost}', linewidth=2)
        axs[0,1].plot(internal_accuracies, offload_rates, 
                     color=colors[cost_str], marker=markers[cost_str], 
                     label=f'Cost = {cost}', linewidth=2)
    
    axs[0,0].set_xlabel('Internal Memory Accuracy (6 items)')
    axs[0,0].set_ylabel('Encoding Rate')
    axs[0,0].set_title('(a) Memory Encoding Strategy')
    axs[0,0].legend()
    axs[0,0].grid(True, alpha=0.3)
    
    axs[0,1].set_xlabel('Internal Memory Accuracy (6 items)')
    axs[0,1].set_ylabel('Offloading Rate')
    axs[0,1].set_title('(b) Offloading Strategy')
    axs[0,1].legend()
    axs[0,1].grid(True, alpha=0.3)
    
    # 準確率分析
    for i, cost in enumerate(costs):
        cost_str = str(float(cost))
        
        normal_accuracies = []
        surprise_accuracies = []
        
        for acc in internal_accuracies:
            results = results_by_accuracy_and_cost[cost][acc]
            normal_accuracies.append(results.get("mean_accuracy_normal", 0))
            surprise_accuracies.append(results.get("mean_accuracy_surprise", 0))
        
        axs[1,0].plot(internal_accuracies, normal_accuracies, 
                     color=colors[cost_str], marker=markers[cost_str], 
                     label=f'Cost = {cost}', linewidth=2)
        axs[1,1].plot(internal_accuracies, surprise_accuracies, 
                     color=colors[cost_str], marker=markers[cost_str], 
                     label=f'Cost = {cost}', linewidth=2)
    
    axs[1,0].set_xlabel('Internal Memory Accuracy (6 items)')
    axs[1,0].set_ylabel('Normal Test Accuracy')
    axs[1,0].set_title('(c) Normal Test Performance')
    axs[1,0].legend()
    axs[1,0].grid(True, alpha=0.3)
    
    axs[1,1].set_xlabel('Internal Memory Accuracy (6 items)')
    axs[1,1].set_ylabel('Surprise Test Accuracy')
    axs[1,1].set_title('(d) Surprise Test Performance')
    axs[1,1].legend()
    axs[1,1].grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()
    
    return results_by_accuracy_and_cost


def run_simulation_fig8_specific(params: EnhancedModelParameters, strategies: List[StrategyFig8]) -> Dict[str, Any]:
    """Fig.8 專用的模擬函數"""
    print(f"    運行 Fig.8 模擬...")
    
    overall_best_strategy_counts = {s: 0 for s in strategies}
    cumulative_hits_normal = 0
    cumulative_hits_surprise = 0
    cumulative_total_items = 0
    successful_runs = 0
    
    for i_run in range(params.n_model_runs):
        strategy_rewards = {}
        strategy_counts = {}
        
        for strategy in strategies:
            strategy_rewards[strategy] = 0.0
            strategy_counts[strategy] = 0
        
        for episode in range(params.n_episodes_per_strategy_eval):
            # 為 Fig.8 評估策略
            strategy_values = {}
            for strategy in strategies:
                strategy_values[strategy] = evaluate_strategy_fig8(strategy, params)
            
            best_strategy = max(strategy_values.keys(), key=lambda s: strategy_values[s])
            
            # 模擬試驗
            reward, hits_normal, hits_surprise = simulate_trial_fig8(params, best_strategy)
            
            strategy_rewards[best_strategy] += reward
            strategy_counts[best_strategy] += 1
        
        # 找出本輪最佳策略
        mean_rewards = {}
        for strategy in strategies:
            if strategy_counts[strategy] > 0:
                mean_rewards[strategy] = strategy_rewards[strategy] / strategy_counts[strategy]
        
        if mean_rewards:
            best_run_strategy = max(mean_rewards.keys(), key=lambda s: mean_rewards[s])
            overall_best_strategy_counts[best_run_strategy] += 1
            
            # 累積統計
            if strategy_counts[best_run_strategy] > 0:
                cumulative_hits_normal += strategy_rewards[best_run_strategy] / params.value_high  # 簡化計算
                cumulative_hits_surprise += hits_surprise
                cumulative_total_items += strategy_counts[best_run_strategy] * (params.n_high_items + params.n_low_items)
                successful_runs += 1
    
    # 計算比例
    strategy_proportions = {}
    for strategy in strategies:
        strategy_proportions[strategy] = overall_best_strategy_counts[strategy] / successful_runs if successful_runs > 0 else 0.0
    
    mean_accuracy_normal = cumulative_hits_normal / cumulative_total_items if cumulative_total_items > 0 else 0.0
    mean_accuracy_surprise = cumulative_hits_surprise / cumulative_total_items if cumulative_total_items > 0 else 0.0
    
    return {
        "strategy_proportions": strategy_proportions,
        "mean_accuracy_normal": mean_accuracy_normal,
        "mean_accuracy_surprise": mean_accuracy_surprise,
        "overall_best_strategy_counts": overall_best_strategy_counts
    }


def evaluate_strategy_fig8(strategy: StrategyFig8, params: EnhancedModelParameters) -> float:
    """Fig.8 策略評估"""
    encode, offload_allowed = strategy
    
    if encode:
        internal_accuracy = params.accuracy_internal_6_items
        expected_reward = (params.n_high_items + params.n_low_items) * internal_accuracy * params.value_high
    else:
        expected_reward = 0.0
    
    if offload_allowed:
        offload_reward = (params.n_high_items + params.n_low_items) * params.accuracy_offloaded * params.value_high
        offload_cost = (params.n_high_items + params.n_low_items) * params.cost_offloading
        expected_reward += offload_reward - offload_cost
    
    return expected_reward


def simulate_trial_fig8(params: EnhancedModelParameters, strategy: StrategyFig8) -> Tuple[float, int, int]:
    """Fig.8 試驗模擬"""
    encode, offload_allowed = strategy
    
    total_reward = 0.0
    hits_normal = 0
    hits_surprise = 0
    
    total_items = params.n_high_items + params.n_low_items
    
    for _ in range(total_items):
        hit_normal = False
        hit_surprise = False
        
        if encode and offload_allowed:
            # 兩種方式都有
            if np.random.rand() < params.accuracy_internal_6_items:
                hit_normal = True
                hit_surprise = True
            elif np.random.rand() < params.accuracy_offloaded:
                hit_normal = True
            total_reward -= params.cost_offloading
        elif encode:
            # 只有內部記憶
            if np.random.rand() < params.accuracy_internal_6_items:
                hit_normal = True
                hit_surprise = True
        elif offload_allowed:
            # 只有卸載
            if np.random.rand() < params.accuracy_offloaded:
                hit_normal = True
            total_reward -= params.cost_offloading
        
        if hit_normal:
            hits_normal += 1
            total_reward += params.value_high
        
        if hit_surprise:
            hits_surprise += 1
    
    return total_reward, hits_normal, hits_surprise


# 新增人性化特徵的比較分析函數
def compare_human_vs_original_comprehensive(base_params: EnhancedModelParameters):
    """全面比較人性化模型與原始模型"""
    print("\n=== 人性化模型 vs 原始模型全面比較 ===")
    
    # 設定比較參數
    test_scenarios = [
        ("Fig.2 - No Offloading", False, get_all_strategies(allow_offloading=False)),
        ("Fig.3 - With Offloading", True, get_all_strategies(allow_offloading=True))
    ]
    
    comparison_results = {}
    
    for scenario_name, offloading_allowed, strategies in test_scenarios:
        print(f"\n--- {scenario_name} ---")
        
        # 原始模型
        params_original = replace(base_params, enable_human_features=False)
        results_original = run_simulation_enhanced(params_original, strategies)
        
        # 人性化模型
        params_human = replace(base_params, enable_human_features=True, population_size=50)
        results_human = run_simulation_enhanced(params_human, strategies)
        
        # 比較分析
        comparison = analyze_model_differences(results_original, results_human, scenario_name)
        comparison_results[scenario_name] = comparison
        
        # 輸出比較結果
        print_comparison_summary(comparison)
    
    # 總體可視化比較
    visualize_comprehensive_comparison(comparison_results)
    
    return comparison_results


def analyze_model_differences(original_results: Dict, human_results: Dict, scenario_name: str) -> Dict:
    """分析兩個模型的差異"""
    comparison = {
        'scenario': scenario_name,
        'strategy_diversity': {},
        'top_strategy_match': False,
        'performance_difference': {},
        'preference_shifts': []
    }
    
    # 策略多樣性比較
    original_strategies = set(s for s, p in original_results["strategy_proportions"].items() if p > 0.01)
    
    if isinstance(human_results["strategy_proportions"], dict):
        human_strategies = set(s for s, p in human_results["strategy_proportions"].items() if p > 0.01)
    else:
        # 處理人性化結果可能的不同格式
        human_strategies = set()
    
    comparison['strategy_diversity'] = {
        'original_count': len(original_strategies),
        'human_count': len(human_strategies),
        'overlap': len(original_strategies & human_strategies),
        'human_only': human_strategies - original_strategies,
        'original_only': original_strategies - human_strategies
    }
    
    # 頂級策略比較
    original_top = max(original_results["strategy_proportions"].items(), key=lambda x: x[1])
    if isinstance(human_results["strategy_proportions"], dict) and human_results["strategy_proportions"]:
        human_top = max(human_results["strategy_proportions"].items(), key=lambda x: x[1])
        comparison['top_strategy_match'] = original_top[0] == human_top[0]
        comparison['top_strategies'] = {
            'original': original_top,
            'human': human_top
        }
    
    # 性能差異
    original_acc_h = original_results.get("mean_accuracy_H", 0)
    original_acc_l = original_results.get("mean_accuracy_L", 0)
    
    # 人性化結果可能需要不同的處理方式
    human_acc_h = 0
    human_acc_l = 0
    if 'agent_performances' in human_results:
        # 計算平均性能
        performances = human_results['agent_performances']
        if performances:
            human_acc_h = np.mean([p.get('mean_reward', 0) for p in performances]) / 100  # 簡化
            human_acc_l = human_acc_h * 0.8  # 估算
    
    comparison['performance_difference'] = {
        'accuracy_h_diff': human_acc_h - original_acc_h,
        'accuracy_l_diff': human_acc_l - original_acc_l,
        'original_h': original_acc_h,
        'original_l': original_acc_l,
        'human_h': human_acc_h,
        'human_l': human_acc_l
    }
    
    return comparison


def print_comparison_summary(comparison: Dict):
    """輸出比較摘要"""
    print(f"策略多樣性:")
    print(f"  原始模型: {comparison['strategy_diversity']['original_count']} 種主要策略")
    print(f"  人性化模型: {comparison['strategy_diversity']['human_count']} 種主要策略")
    print(f"  重疊策略: {comparison['strategy_diversity']['overlap']} 種")
    
    if 'top_strategies' in comparison:
        print(f"頂級策略比較:")
        print(f"  原始模型最佳: {comparison['top_strategies']['original'][0]} ({comparison['top_strategies']['original'][1]:.3f})")
        print(f"  人性化模型最佳: {comparison['top_strategies']['human'][0]} ({comparison['top_strategies']['human'][1]:.3f})")
        print(f"  頂級策略一致: {'是' if comparison['top_strategy_match'] else '否'}")
    
    perf = comparison['performance_difference']
    print(f"性能差異:")
    print(f"  高價值準確率差異: {perf['accuracy_h_diff']:+.3f}")
    print(f"  低價值準確率差異: {perf['accuracy_l_diff']:+.3f}")


def visualize_comprehensive_comparison(comparison_results: Dict):
    """可視化全面比較結果"""
    fig, axs = plt.subplots(2, 2, figsize=(15, 10))
    fig.suptitle("Human-like vs Original Model Comprehensive Comparison", fontsize=16)
    
    scenarios = list(comparison_results.keys())
    
    # 策略多樣性比較
    original_counts = [comparison_results[s]['strategy_diversity']['original_count'] for s in scenarios]
    human_counts = [comparison_results[s]['strategy_diversity']['human_count'] for s in scenarios]
    
    x = np.arange(len(scenarios))
    width = 0.35
    
    axs[0,0].bar(x - width/2, original_counts, width, label='Original Model', color='skyblue')
    axs[0,0].bar(x + width/2, human_counts, width, label='Human-like Model', color='lightcoral')
    axs[0,0].set_xlabel('Scenario')
    axs[0,0].set_ylabel('Number of Major Strategies')
    axs[0,0].set_title('Strategy Diversity Comparison')
    axs[0,0].set_xticks(x)
    axs[0,0].set_xticklabels([s.split(' - ')[0] for s in scenarios])
    axs[0,0].legend()
    
    # 策略重疊度
    overlaps = [comparison_results[s]['strategy_diversity']['overlap'] for s in scenarios]
    axs[0,1].bar(scenarios, overlaps, color='lightgreen')
    axs[0,1].set_xlabel('Scenario')
    axs[0,1].set_ylabel('Number of Overlapping Strategies')
    axs[0,1].set_title('Strategy Overlap')
    axs[0,1].tick_params(axis='x', rotation=45)
    
    # 性能差異 - 高價值
    h_diffs = [comparison_results[s]['performance_difference']['accuracy_h_diff'] for s in scenarios]
    colors = ['green' if diff >= 0 else 'red' for diff in h_diffs]
    axs[1,0].bar(scenarios, h_diffs, color=colors)
    axs[1,0].axhline(y=0, color='black', linestyle='-', alpha=0.3)
    axs[1,0].set_xlabel('Scenario')
    axs[1,0].set_ylabel('Accuracy Difference (Human - Original)')
    axs[1,0].set_title('High-Value Performance Difference')
    axs[1,0].tick_params(axis='x', rotation=45)
    
    # 頂級策略一致性
    matches = [1 if comparison_results[s]['top_strategy_match'] else 0 for s in scenarios]
    axs[1,1].bar(scenarios, matches, color='purple', alpha=0.7)
    axs[1,1].set_xlabel('Scenario')
    axs[1,1].set_ylabel('Top Strategy Match (1=Yes, 0=No)')
    axs[1,1].set_title('Top Strategy Consistency')
    axs[1,1].set_ylim(0, 1.1)
    axs[1,1].tick_params(axis='x', rotation=45)
    
    plt.tight_layout()
    plt.show()


# 新增個體差異分析函數
def analyze_individual_differences_detailed(human_results: Dict):
    """詳細分析個體差異效應"""
    if 'individual_differences_effects' not in human_results:
        print("沒有個體差異數據可供分析")
        return
    
    print("\n=== 詳細個體差異分析 ===")
    
    effects = human_results['individual_differences_effects']
    
    # 計算相關係數
    correlations = {}
    for factor_name, data_points in effects.items():
        if len(data_points) > 1:
            factors, rewards = zip(*data_points)
            correlation = np.corrcoef(factors, rewards)[0, 1]
            correlations[factor_name] = correlation
            print(f"{factor_name}: correlation with performance = {correlation:.3f}")
    
    # 可視化個體差異效應
    fig, axs = plt.subplots(2, 2, figsize=(12, 10))
    fig.suptitle("Individual Differences Effects on Performance", fontsize=16)
    
    factor_names = list(effects.keys())
    positions = [(0,0), (0,1), (1,0), (1,1)]
    
    for i, (factor_name, pos) in enumerate(zip(factor_names, positions)):
        if factor_name in effects and effects[factor_name]:
            factors, rewards = zip(*effects[factor_name])
            
            ax = axs[pos[0], pos[1]]
            ax.scatter(factors, rewards, alpha=0.6, s=30)
            
            # 添加回歸線
            z = np.polyfit(factors, rewards, 1)
            p = np.poly1d(z)
            ax.plot(factors, p(factors), "r--", alpha=0.8)
            
            ax.set_xlabel(factor_name.replace('_', ' ').title())
            ax.set_ylabel('Average Reward')
            ax.set_title(f'{factor_name.replace("_", " ").title()}\n(r = {correlations.get(factor_name, 0):.3f})')
            ax.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()
    
    return correlations


# 主要演示函數
def run_complete_demonstration():
    """運行完整的演示"""
    print("=" * 60)
    print("增強版認知卸載模型完整演示")
    print("=" * 60)
    
    # 基礎參數設定
    base_params = EnhancedModelParameters(
        n_model_runs=500,  # 為演示減少運行次數
        n_episodes_per_strategy_eval=20,
        enable_human_features=False,  # 先測試原始功能
        population_size=30
    )
    
    print("\n1. 測試原始功能...")
    
    # Fig.2-3 基礎測試
    results_fig2 = simulate_fig2(base_params)
    results_fig3 = simulate_fig3(base_params)
    
    print("\n2. 測試參數效應...")
    
    # Fig.4 和 Fig.7 參數效應
    results_fig4 = simulate_fig4(base_params)
    results_fig7 = simulate_fig7(base_params)
    
    print("\n3. 測試人性化特徵...")
    
    # 人性化特徵比較
    comparison_results = compare_human_vs_original_comprehensive(base_params)
    
    print("\n4. 個體差異分析...")
    
    # 人性化模擬測試
    human_params = replace(base_params, enable_human_features=True)
    strategies = get_all_strategies(allow_offloading=True)
    human_results = run_simulation_enhanced(human_params, strategies)
    
    if isinstance(human_results, dict) and 'individual_differences_effects' in human_results:
        correlations = analyze_individual_differences_detailed(human_results)
    
    print("\n演示完成！")
    print("=" * 60)
    
    return {
        'original_results': {'fig2': results_fig2, 'fig3': results_fig3},
        'parameter_effects': {'fig4': results_fig4, 'fig7': results_fig7},
        'human_comparison': comparison_results,
        'human_results': human_results
    }


# 執行演示
if __name__ == "__main__":
    complete_results = run_complete_demonstration()