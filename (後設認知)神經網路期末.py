import numpy as np
from scipy import stats
from typing import Tuple, List, Dict, Any
from dataclasses import dataclass, replace
import itertools
from collections import defaultdict
import random
import matplotlib.pyplot as plt

# 在 ModelParameters 類中添加所有相關參數 (原始和 SDT)
@dataclass
class ModelParameters:
    n_high_items: int = 3  # 高價值項目數量
    n_low_items: int = 3   # 低價值項目數量
    value_high: float = 8.0  # 高價值項目的價值
    value_low: float = 2.0   # 低價值項目的價值
    cost_offloading: float = 1  # 卸載一個項目的成本 (預設值，會在模擬Fig.7和後設認知模擬時改變)

    # 內部記憶準確率
    accuracy_internal_1_item: float = 0.95  # Fig.6 需要
    accuracy_internal_2_items: float = 0.925 # Fig.6 需要
    accuracy_internal_3_items: float = 0.90  # 標準情況: 只記一種類型 (3個)
    accuracy_internal_6_items: float = 0.75  # 標準情況: 記兩種類型 (6個)
    accuracy_offloaded: float = 0.98         # 外部卸載項目的準確率

    # 模擬控制參數
    n_episodes_per_strategy_eval: int = 50
    n_model_runs: int = 1000 # 建議增加以獲得更穩定結果

    # --- 後設認知參數 (舊模型 - 基于感知内部记忆成功率) ---
    metacognitive_threshold: float = 0.7 # 後設認知閾值 (感知準確率低於此閾值則考慮卸載) # 已恢復
    metacognitive_bias: float = 0.0      # 後設認知偏差 (感知內部記憶成功率 = 實際成功率 + bias) # 已恢復
    # confidence_noise_std: float = 0.05   # 感知內部記憶成功率的噪音標準差 (使用 SDT 的 confidence_noise_std)
    load_dependent_confidence: bool = True # 感知內部記憶成功率是否受記憶負荷影響 # 已恢復
    load_confidence_penalty: float = 0.01 # 記憶負荷每增加一個項目，感知成功率降低的懲罰 # 已恢復
    # value_sensitive_metacognition: bool = True # 後設認知是否對高低價值有不同處理 (使用 SDT 的 value_sensitive_metacognition)
    # high_value_confidence_boost: float = 0.03  # 高價值項目在感知內部記憶成功率上的額外加成 (使用 SDT 的 high_value_confidence_boost)


    # --- SDT 框架的後設認知參數 ---
    # Type-1 任務表現參數
    d_prime: float = 1.5  # 任務敏感度 (d')，衡量底層認知能力
    type1_criterion: float = 0.0  # Type-1 決策標準 (beta)
    
    # Type-2 信心判斷參數
    metacognitive_efficiency: float = 0.8  # 後設認知效率 (0-1)
    confidence_criterion_bias: float = 0.0  # 信心判斷的決策標準偏移 (SDT)
    confidence_noise_std: float = 0.2  # 信心判斷的內部噪音 (SDT) # 使用單一名稱
    
    # 卸載決策閾值
    offload_confidence_threshold_high: float = 0.6  # 高價值項目的信心閾值 (SDT)
    offload_confidence_threshold_low: float = 0.4   # 低價值項目的信心閾值 (SDT)
    
    # 價值敏感性參數
    value_sensitive_metacognition: bool = True  # 是否根據項目價值調整後設認知 (使用單一名稱)
    high_value_confidence_boost: float = 0.05  # 高價值項目的信心加成 (使用單一名稱)


# 策略表示: (store_H_internal, store_L_internal, offload_H_allowed, offload_L_allowed)
# 每個元素是布林值 (True/False)
# offload_*_allowed 現在表示該類型的項目是否可以被考慮卸載 (取決於後設認知和閾值)
Strategy = Tuple[bool, bool, bool, bool]

# Fig. 8 策略表示: (encode, offload_allowed)
StrategyFig8 = Tuple[bool, bool]


def get_all_strategies(allow_offloading = True, offload_restriction = "none") -> List[Strategy]:
    options_sh = [True, False] # 可以選擇是否記憶高價值
    options_sl = [True, False] # 可以選擇是否記憶低價值

    if not allow_offloading:
        options_oh = [False] # 不允許考慮卸載高價值
        options_ol = [False] # 不允許考慮卸載低價值
    else:
        if offload_restriction == "high_only":
            options_oh = [True, False]
            options_ol = [False] # 低價值不允許考慮卸載
        else: # "none" restriction or other unhandled
            options_oh = [True, False]
            options_ol = [True, False]
    return list(itertools.product(options_sh, options_sl, options_oh, options_ol))

def get_strategies_fig8() -> List[StrategyFig8]:
    # encode 可以是 True 或 False
    # offload_allowed 可以是 True 或 False
    return [
        (True, True),    # encode=True, offload_allowed=True
        (True, False),   # encode=True, offload_allowed=False
        (False, True),   # encode=False, offload_allowed=True
        (False, False)   # encode=False, offload_allowed=False
    ]


def get_internal_memory_accuracy(strategy_encoding_part: Tuple[bool, bool], params: ModelParameters) -> float:
    """
    根據策略的編碼部分計算理論內部記憶準確率 (作為信心基礎)
    strategy_encoding_part: (store_H_internal, store_L_internal)
    """
    items_stored_internally = 0
    if strategy_encoding_part[0]: # 選擇記憶高價值
        items_stored_internally += params.n_high_items
    if strategy_encoding_part[1]: # 選擇記憶低價值
        items_stored_internally += params.n_low_items

    if items_stored_internally == 0:
        return 0.0
    # 邏輯是根據總內部項目數來決定準確率曲線
    elif items_stored_internally == 1:
        return params.accuracy_internal_1_item
    elif items_stored_internally == 2:
        return params.accuracy_internal_2_items
    elif items_stored_internally == 3:
        return params.accuracy_internal_3_items
    elif items_stored_internally == (params.n_high_items + params.n_low_items):
         return params.accuracy_internal_6_items
    else:
        # 如果項目數不是預設的 1, 2, 3, 6，提供一個簡單的 fallback 或根據需要調整
        # print(f"Warning: items_stored_internally count ({items_stored_internally}) not specifically defined in ModelParameters accuracies.")
        # 使用最低定義的準確率作為簡單回退（如果大於0）
        if items_stored_internally > 0:
            return params.accuracy_internal_6_items
        else:
            return 0.0 # 0 items means 0 accuracy


def calculate_accuracy_for_strategy(strategy: Tuple[bool, bool, bool, bool], params: ModelParameters, mode: str = "normal") -> Tuple[float, float]:
    """
    計算一個策略的理論客觀準確率 (不進行實際模擬)

    Args:
        strategy: (store_H_internal, store_L_internal, offload_H_allowed, offload_L_allowed)
        params: 模型參數
        mode: "normal" 或 "surprise_test"，surprise_test 只考慮內部記憶

    Returns:
        (高價值準確率, 低價值準確率)
    """
    store_H_internal, store_L_internal, offload_H_allowed, offload_L_allowed = strategy

    # 獲取客觀的內部記憶準確率
    objective_p_internal = get_internal_memory_accuracy((store_H_internal, store_L_internal), params)

    # 計算高價值項目的準確率
    if mode == "normal":
        # 正常測試：考慮內部記憶和卸載
        if store_H_internal and offload_H_allowed:
            # 如果同時使用內部記憶和卸載，成功率是兩者的聯合概率
            # P(成功) = 1 - P(兩者都失敗) = 1 - (1-P_int)(1-P_ext)
            acc_H = 1.0 - (1.0 - objective_p_internal) * (1.0 - params.accuracy_offloaded)
        elif store_H_internal:
            # 只使用內部記憶
            acc_H = objective_p_internal
        elif offload_H_allowed:
            # 只使用卸載
            acc_H = params.accuracy_offloaded
        else:
            # 既不使用內部記憶也不使用卸載
            acc_H = 0.0
    else:  # "surprise_test"
        # 意外測試：只考慮內部記憶
        acc_H = objective_p_internal if store_H_internal else 0.0

    # 計算低價值項目的準確率 (邏輯同上)
    if mode == "normal":
        if store_L_internal and offload_L_allowed:
            acc_L = 1.0 - (1.0 - objective_p_internal) * (1.0 - params.accuracy_offloaded)
        elif store_L_internal:
            acc_L = objective_p_internal
        elif offload_L_allowed:
            acc_L = params.accuracy_offloaded
        else:
            acc_L = 0.0
    else:  # "surprise_test"
        acc_L = objective_p_internal if store_L_internal else 0.0

    return acc_H, acc_L


def evaluate_strategy_subjective(strategy: Tuple[bool, bool, bool, bool], params: ModelParameters) -> float:
    """
    評估一個策略在多次試驗中的「主觀感知」平均獎勵，使用 SDT 後設認知模型。

    Args:
        strategy: (store_H_internal, store_L_internal, offload_H_allowed, offload_L_allowed)
        params: 模型參數

    Returns:
        策略的主觀預期獎勵
    """
    store_H_internal, store_L_internal, offload_H_allowed, offload_L_allowed = strategy

    # 在 SDT 框架下，主觀評估基於模擬 Type 1 結果和信心判斷
    rewards_this_strategy_subjective = []

    # Type 1 實際準確率 (用於生成 Type 1 結果)
    p_internal_actual_H = get_internal_memory_accuracy((store_H_internal, False), params) if store_H_internal else 0.0
    p_internal_actual_L = get_internal_memory_accuracy((False, store_L_internal), params) if store_L_internal else 0.0


    for _ in range(params.n_episodes_per_strategy_eval):
        current_subjective_expected_reward = 0.0
        offloaded_H_count_this_episode = 0
        offloaded_L_count_this_episode = 0

        # --- 模擬高價值項目 ---
        for _ in range(params.n_high_items):
            is_actually_correct_H = np.random.rand() < p_internal_actual_H # 模擬 Type 1 結果
            evidence_H = generate_evidence_signal(params.d_prime, is_actually_correct_H, params.type1_criterion)
            confidence_H = compute_confidence_from_evidence(evidence_H, params, is_high_value=True)

            decided_to_offload_H = False
            if offload_H_allowed:
                 # 使用 SDT 預期效用決策
                 decided_to_offload_H = make_offload_decision_sdt(confidence_H, params.value_high, params)

            if decided_to_offload_H:
                 offloaded_H_count_this_episode += 1
                 # 主觀預期獎勵：外部記憶準確率 * 價值
                 current_subjective_expected_reward += params.accuracy_offloaded * params.value_high
            else:
                 # 主觀預期獎勵：信心 * 價值
                 current_subjective_expected_reward += confidence_H * params.value_high

        # --- 模擬低價值項目 ---
        for _ in range(params.n_low_items):
            is_actually_correct_L = np.random.rand() < p_internal_actual_L # 模擬 Type 1 結果
            evidence_L = generate_evidence_signal(params.d_prime, is_actually_correct_L, params.type1_criterion)
            confidence_L = compute_confidence_from_evidence(evidence_L, params, is_high_value=False)

            decided_to_offload_L = False
            if offload_L_allowed:
                 # 使用 SDT 預期效用決策
                 decided_to_offload_L = make_offload_decision_sdt(confidence_L, params.value_low, params)

            if decided_to_offload_L:
                 offloaded_L_count_this_episode += 1
                 # 主觀預期獎勵：外部記憶準確率 * 價值
                 current_subjective_expected_reward += params.accuracy_offloaded * params.value_low
            else:
                 # 主觀預期獎勵：信心 * 價值
                 current_subjective_expected_reward += confidence_L * params.value_low

        # 減去本次試驗中實際發生的卸載成本
        current_subjective_expected_reward -= offloaded_H_count_this_episode * params.cost_offloading
        current_subjective_expected_reward -= offloaded_L_count_this_episode * params.cost_offloading


        rewards_this_strategy_subjective.append(current_subjective_expected_reward)

    # 返回平均主觀預期獎勵
    return np.mean(rewards_this_strategy_subjective)


# 修改 simulate_trial 函數以包含後設認知和每個項目的卸載決策
def simulate_trial(params: ModelParameters, strategy: Strategy) -> Tuple[float, int, int, int, int, int, int]:
    """
    模擬單次試驗並計算總獎勵、命中數和實際卸載數。
    返回: (總獎勵, 高價值正常命中數, 低價值正常命中數, 高價值意外命中數, 低價值意外命中數, 實際卸載高價值數, 實際卸載低價值數)
    """
    store_H_internal, store_L_internal, offload_H_allowed, offload_L_allowed = strategy

    total_reward = 0.0
    high_value_hits_normal_count = 0
    low_value_hits_normal_count = 0
    high_value_hits_surprise_count = 0
    low_value_hits_surprise_count = 0
    actual_offloaded_H_count = 0
    actual_offloaded_L_count = 0

    # 處理高價值項目
    for _ in range(params.n_high_items):
        # 生成任務實際表現（基於內部記憶準確率）
        # 在 SDT 框架下，Type 1 任務的實際正確性由底層的 d' 和 criterion 決定
        # 我們在這裡簡化處理，假設內部記憶的「實際準確率」作為生成 Type 1 結果的依據
        # 這部分的連接可能需要根據具體的 SDT 模型實現進行調整
        p_internal_actual = get_internal_memory_accuracy((store_H_internal, store_L_internal), params)
        # Simulate Type 1 outcome based on actual internal accuracy if storing
        is_actually_correct = np.random.rand() < p_internal_actual if store_H_internal else False # Simplified Type 1 outcome

        # 生成 SDT 證據信號 (Type 1 任務判斷後的內部信號)
        # 這裡假設證據信號反映了 Type 1 判斷的確定性
        # 如果 Type 1 判斷正確，證據信號來自一個分佈；如果錯誤，來自另一個分佈
        # 簡化起見，我們可以根據「實際是否正確」來生成對應分佈的證據
        # 更嚴謹的 SDT 模擬會先生成證據，再根據證據和 Type 1 準則決定 Type 1 判斷
        # 我們這裡假設 Type 1 判斷已經根據 internal_accuracy_scalar 得出，然後生成與之對應的證據
        # 為了與 generate_evidence_signal 一致，我們需要知道是 Hit/Miss (正確) 或 FA/CR (錯誤)
        # 這裡假設如果 is_actually_correct 為 True，則生成來自「正確」分佈的證據
        # 如果 is_actually_correct 為 False，則生成來自「錯誤」分佈的證據
        evidence = generate_evidence_signal(params.d_prime, is_actually_correct, params.type1_criterion)

        # 計算信心水平 (Type 2 判斷，基於證據信號)
        # 這裡需要考慮後設認知效率、偏差、噪音以及價值敏感性
        confidence = compute_confidence_from_evidence(evidence, params, is_high_value=True)

        # 做出卸載決策 (Type 2 決策，基於信心水平和預期效用)
        decided_to_offload = False
        if offload_H_allowed:
            decided_to_offload = make_offload_decision_sdt(confidence, params.value_high, params)

        # 模擬回憶結果 (基於實際的儲存位置和準確率)
        item_recalled_normal = False
        item_recalled_surprise = False # 意外測試只考慮內部記憶

        if decided_to_offload:
            actual_offloaded_H_count += 1
            # 從外部記憶回憶
            if np.random.rand() < params.accuracy_offloaded:
                item_recalled_normal = True
            # 從外部記憶回憶成功，不算意外命中
        else:
            # 依賴內部記憶 (只有在策略允許編碼高價值時才可能)
            if store_H_internal:
                 # 內部記憶是否成功取決於實際的内部準確率
                 # 注意：這裡的實際回憶機率應與生成 Type 1 結果的機制一致
                 # 如果上面的 Type 1 結果模擬使用了 p_internal_actual，這裡也應使用
                 if np.random.rand() < p_internal_actual: # 使用 p_internal_actual 模擬實際內部回憶
                     item_recalled_normal = True # 內部回憶成功也是正常命中
                     item_recalled_surprise = True # 內部記憶成功才算意外命中


        if item_recalled_normal:
            high_value_hits_normal_count += 1
            total_reward += params.value_high # 正常回憶成功獲得獎勵

        if item_recalled_surprise:
             high_value_hits_surprise_count += 1

    ### 處理低價值項目 (邏輯同高價值項目)
    for _ in range(params.n_low_items):
        p_internal_actual = get_internal_memory_accuracy((store_H_internal, store_L_internal), params)
        is_actually_correct = np.random.rand() < p_internal_actual if store_L_internal else False # Simplified Type 1 outcome

        evidence = generate_evidence_signal(params.d_prime, is_actually_correct, params.type1_criterion)
        confidence = compute_confidence_from_evidence(evidence, params, is_high_value=False)

        decided_to_offload = False
        if offload_L_allowed:
            decided_to_offload = make_offload_decision_sdt(confidence, params.value_low, params)

        item_recalled_normal = False
        item_recalled_surprise = False

        if decided_to_offload:
            actual_offloaded_L_count += 1
            if np.random.rand() < params.accuracy_offloaded:
                item_recalled_normal = True
        else:
            if store_L_internal:
                 if np.random.rand() < p_internal_actual: # 使用 p_internal_actual 模擬實際內部回憶
                     item_recalled_normal = True
                     item_recalled_surprise = True

        if item_recalled_normal:
            low_value_hits_normal_count += 1
            total_reward += params.value_low

        if item_recalled_surprise:
             low_value_hits_surprise_count += 1

    # 計算總卸載成本 (根據實際卸載的項目數)
    total_reward -= actual_offloaded_H_count * params.cost_offloading
    total_reward -= actual_offloaded_L_count * params.cost_offloading

    return total_reward, high_value_hits_normal_count, low_value_hits_normal_count, high_value_hits_surprise_count, low_value_hits_surprise_count, actual_offloaded_H_count, actual_offloaded_L_count


# 原始的 simulate_trial 函數 (不包含後設認知，用於 Fig 2-8)
def simulate_trial_original(params: ModelParameters, strategy: Strategy) -> Tuple[float, int, int, int, int, int, int]:
    """
    模擬單次試驗並計算總獎勵、命中數 (原始版本，不包含後設認知)
    返回: (總獎勵, 高價值正常命中數, 低價值正常命中數, 高價值意外命中數, 低價值意外命中數, 實際卸載高價值數, 實際卸載低價值數)
    """
    store_H_internal, store_L_internal, offload_H_allowed, offload_L_allowed = strategy

    total_reward = 0.0
    high_value_hits_normal_count = 0
    low_value_hits_normal_count = 0
    high_value_hits_surprise_count = 0
    low_value_hits_surprise_count = 0
    actual_offloaded_H_count = 0
    actual_offloaded_L_count = 0

    # 計算內部記憶準確率
    p_internal_actual = get_internal_memory_accuracy((store_H_internal, store_L_internal), params)

    # 處理高價值項目
    for _ in range(params.n_high_items):
        item_recalled_H_normal = False
        item_recalled_H_surprise = False

        if store_H_internal and offload_H_allowed:
            # 同時使用內部記憶和卸載
            actual_offloaded_H_count += 1
            # 聯合概率: 1 - (1-p_internal)(1-p_external)
            combined_success_prob = 1.0 - (1.0 - p_internal_actual) * (1.0 - params.accuracy_offloaded)
            if np.random.rand() < combined_success_prob:
                item_recalled_H_normal = True
            # 意外測試只考慮內部記憶
            if np.random.rand() < p_internal_actual:
                item_recalled_H_surprise = True
        elif store_H_internal:
            # 只使用內部記憶
            if np.random.rand() < p_internal_actual:
                item_recalled_H_normal = True
                item_recalled_H_surprise = True
        elif offload_H_allowed:
            # 只使用卸載
            actual_offloaded_H_count += 1
            if np.random.rand() < params.accuracy_offloaded:
                item_recalled_H_normal = True

        if item_recalled_H_normal:
            high_value_hits_normal_count += 1
            total_reward += params.value_high

        if item_recalled_H_surprise:
            high_value_hits_surprise_count += 1

    # 處理低價值項目
    for _ in range(params.n_low_items):
        item_recalled_L_normal = False
        item_recalled_L_surprise = False

        if store_L_internal and offload_L_allowed:
            # 同時使用內部記憶和卸載
            actual_offloaded_L_count += 1
            combined_success_prob = 1.0 - (1.0 - p_internal_actual) * (1.0 - params.accuracy_offloaded)
            if np.random.rand() < combined_success_prob:
                item_recalled_L_normal = True
            if np.random.rand() < p_internal_actual:
                item_recalled_L_surprise = True
        elif store_L_internal:
            # 只使用內部記憶
            if np.random.rand() < p_internal_actual:
                item_recalled_L_normal = True
                item_recalled_L_surprise = True
        elif offload_L_allowed:
            # 只使用卸載
            actual_offloaded_L_count += 1
            if np.random.rand() < params.accuracy_offloaded:
                item_recalled_L_normal = True

        if item_recalled_L_normal:
            low_value_hits_normal_count += 1
            total_reward += params.value_low

        if item_recalled_L_surprise:
            low_value_hits_surprise_count += 1

    # 計算卸載成本
    total_reward -= actual_offloaded_H_count * params.cost_offloading
    total_reward -= actual_offloaded_L_count * params.cost_offloading

    return total_reward, high_value_hits_normal_count, low_value_hits_normal_count, high_value_hits_surprise_count, low_value_hits_surprise_count, actual_offloaded_H_count, actual_offloaded_L_count


# 原始的 run_simulation 函數 (不包含後設認知，用於 Fig 2-8)
def run_simulation_original(params: ModelParameters, strategies: List[Strategy]) -> Dict[str, Any]:
    print(f"評估運行模擬 (原始方法) ({params.n_model_runs} runs, {params.n_episodes_per_strategy_eval} episodes per run)...")

    overall_best_strategy_counts: Dict[Strategy, int] = {s: 0 for s in strategies}
    cumulative_best_strategy_total_hits_H_normal = 0
    cumulative_best_strategy_total_hits_L_normal = 0
    cumulative_best_strategy_total_hits_H_surprise = 0
    cumulative_best_strategy_total_hits_L_surprise = 0
    cumulative_best_strategy_total_offloaded_H = 0
    cumulative_best_strategy_total_offloaded_L = 0
    cumulative_best_strategy_total_episodes = 0
    successful_runs_count = 0

    for i_run in range(params.n_model_runs):
        if (i_run + 1) % (max(1, params.n_model_runs // 100)) == 0:
            print(f"  運行中 (原始)... {((i_run + 1) / params.n_model_runs) * 100:.0f}%")

        total_reward_per_strategy_this_run: Dict[Strategy, float] = defaultdict(float)
        sample_count_per_strategy_this_run: Dict[Strategy, int] = defaultdict(int)
        total_hits_H_normal_per_strategy_this_run: Dict[Strategy, int] = defaultdict(int)
        total_hits_L_normal_per_strategy_this_run: Dict[Strategy, int] = defaultdict(int)
        total_hits_H_surprise_per_strategy_this_run: Dict[Strategy, int] = defaultdict(int)
        total_hits_L_surprise_per_strategy_this_run: Dict[Strategy, int] = defaultdict(int)
        total_offloaded_H_per_strategy_this_run: Dict[Strategy, int] = defaultdict(int)
        total_offloaded_L_per_strategy_this_run: Dict[Strategy, int] = defaultdict(int)

        allowed_strategies_list_this_run = strategies

        for i_episode in range(params.n_episodes_per_strategy_eval):
            chosen_strategy = random.choice(allowed_strategies_list_this_run)
            chosen_strategy_tuple = tuple(chosen_strategy)

            # 使用原始的 simulate_trial
            reward, hits_H_norm, hits_L_norm, hits_H_surp, hits_L_surp, actual_offloaded_H, actual_offloaded_L = simulate_trial_original(params, chosen_strategy_tuple)

            total_reward_per_strategy_this_run[chosen_strategy_tuple] += reward
            sample_count_per_strategy_this_run[chosen_strategy_tuple] += 1
            total_hits_H_normal_per_strategy_this_run[chosen_strategy_tuple] += hits_H_norm
            total_hits_L_normal_per_strategy_this_run[chosen_strategy_tuple] += hits_L_norm
            total_hits_H_surprise_per_strategy_this_run[chosen_strategy_tuple] += hits_H_surp
            total_hits_L_surprise_per_strategy_this_run[chosen_strategy_tuple] += hits_L_surp
            total_offloaded_H_per_strategy_this_run[chosen_strategy_tuple] += actual_offloaded_H
            total_offloaded_L_per_strategy_this_run[chosen_strategy_tuple] += actual_offloaded_L

        mean_rewards_this_run: Dict[Strategy, float] = {}
        max_avg_reward_this_run = -float('inf')
        strategies_sampled_this_run = [s for s in strategies if sample_count_per_strategy_this_run[s] > 0]

        if not strategies_sampled_this_run:
            continue

        for strategy in strategies_sampled_this_run:
            if sample_count_per_strategy_this_run[strategy] > 0:
                mean_reward = total_reward_per_strategy_this_run[strategy] / sample_count_per_strategy_this_run[strategy]
                mean_rewards_this_run[strategy] = mean_reward
                if mean_reward > max_avg_reward_this_run:
                    max_avg_reward_this_run = mean_reward

        tolerance = 1e-9
        best_strategies_this_run = [
            s for s, avg_r in mean_rewards_this_run.items() if abs(avg_r - max_avg_reward_this_run) < tolerance
        ]

        best_strategy_for_this_run = random.choice(best_strategies_this_run)
        overall_best_strategy_counts[best_strategy_for_this_run] += 1

        samples_best_strategy_this_run = sample_count_per_strategy_this_run[best_strategy_for_this_run]

        if samples_best_strategy_this_run > 0:
            cumulative_best_strategy_total_hits_H_normal += total_hits_H_normal_per_strategy_this_run[best_strategy_for_this_run]
            cumulative_best_strategy_total_hits_L_normal += total_hits_L_normal_per_strategy_this_run[best_strategy_for_this_run]
            cumulative_best_strategy_total_hits_H_surprise += total_hits_H_surprise_per_strategy_this_run[best_strategy_for_this_run]
            cumulative_best_strategy_total_hits_L_surprise += total_hits_L_surprise_per_strategy_this_run[best_strategy_for_this_run]
            cumulative_best_strategy_total_offloaded_H += total_offloaded_H_per_strategy_this_run[best_strategy_for_this_run]
            cumulative_best_strategy_total_offloaded_L += total_offloaded_L_per_strategy_this_run[best_strategy_for_this_run]
            cumulative_best_strategy_total_episodes += samples_best_strategy_this_run
            successful_runs_count += 1

    total_item_opportunities_H = cumulative_best_strategy_total_episodes * params.n_high_items
    total_item_opportunities_L = cumulative_best_strategy_total_episodes * params.n_low_items

    mean_empirical_accuracy_H_normal = cumulative_best_strategy_total_hits_H_normal / total_item_opportunities_H if total_item_opportunities_H > 0 else 0.0
    mean_empirical_accuracy_L_normal = cumulative_best_strategy_total_hits_L_normal / total_item_opportunities_L if total_item_opportunities_L > 0 else 0.0
    mean_empirical_accuracy_H_surprise = cumulative_best_strategy_total_hits_H_surprise / total_item_opportunities_H if total_item_opportunities_H > 0 else 0.0
    mean_empirical_accuracy_L_surprise = cumulative_best_strategy_total_hits_L_surprise / total_item_opportunities_L if total_item_opportunities_L > 0 else 0.0
    mean_empirical_offload_H = cumulative_best_strategy_total_offloaded_H / total_item_opportunities_H if total_item_opportunities_H > 0 else 0.0
    mean_empirical_offload_L = cumulative_best_strategy_total_offloaded_L / total_item_opportunities_L if total_item_opportunities_L > 0 else 0.0

    strategy_proportions: Dict[Strategy, float] = {
        s: overall_best_strategy_counts[s] / successful_runs_count if successful_runs_count > 0 else 0.0 for s in strategies
    }

    print("\n最終策略選擇比例:")
    sorted_overall_proportions = sorted(strategy_proportions.items(), key=lambda item: item[1], reverse=True)
    for s, prop in sorted_overall_proportions:
        print(f"  策略 {s}: 比例 {prop:.3f}")

    print(f"\n平均經驗命中率和卸載率:")
    print(f"  正常準確率 - 高價值: {mean_empirical_accuracy_H_normal:.3f}")
    print(f"  正常準確率 - 低價值: {mean_empirical_accuracy_L_normal:.3f}")
    print(f"  意外準確率 - 高價值: {mean_empirical_accuracy_H_surprise:.3f}")
    print(f"  意外準確率 - 低價值: {mean_empirical_accuracy_L_surprise:.3f}")
    print(f"  卸載率 - 高價值: {mean_empirical_offload_H:.3f}")
    print(f"  卸載率 - 低價值: {mean_empirical_offload_L:.3f}")

    return {
        "strategy_proportions": strategy_proportions,
        "mean_accuracy_H": mean_empirical_accuracy_H_normal,
        "mean_accuracy_L": mean_empirical_accuracy_L_normal,
        "mean_accuracy_H_surprise": mean_empirical_accuracy_H_surprise,
        "mean_accuracy_L_surprise": mean_empirical_accuracy_L_surprise,
        "mean_offload_H": mean_empirical_offload_H,
        "mean_offload_L": mean_empirical_offload_L,
        "overall_best_strategy_counts": overall_best_strategy_counts
    }

# 新增後設認知模擬的 run_simulation 函數
def run_simulation_with_metacognition(params: ModelParameters, strategies: List[Strategy]) -> Dict[str, Any]:
    """
    運行包含後設認知的模擬
    """
    print(f"評估運行模擬 (包含後設認知) ({params.n_model_runs} runs, {params.n_episodes_per_strategy_eval} episodes per run)...")

    overall_best_strategy_counts: Dict[Strategy, int] = {s: 0 for s in strategies}
    cumulative_best_strategy_total_hits_H_normal = 0
    cumulative_best_strategy_total_hits_L_normal = 0
    cumulative_best_strategy_total_hits_H_surprise = 0
    cumulative_best_strategy_total_hits_L_surprise = 0
    cumulative_best_strategy_total_offloaded_H = 0
    cumulative_best_strategy_total_offloaded_L = 0
    cumulative_best_strategy_total_episodes = 0
    successful_runs_count = 0

    for i_run in range(params.n_model_runs):
        if (i_run + 1) % (max(1, params.n_model_runs // 100)) == 0:
            print(f"  運行中 (後設認知)... {((i_run + 1) / params.n_model_runs) * 100:.0f}%")

        total_reward_per_strategy_this_run: Dict[Strategy, float] = defaultdict(float)
        sample_count_per_strategy_this_run: Dict[Strategy, int] = defaultdict(int)
        total_hits_H_normal_per_strategy_this_run: Dict[Strategy, int] = defaultdict(int)
        total_hits_L_normal_per_strategy_this_run: Dict[Strategy, int] = defaultdict(int)
        total_hits_H_surprise_per_strategy_this_run: Dict[Strategy, int] = defaultdict(int)
        total_hits_L_surprise_per_strategy_this_run: Dict[Strategy, int] = defaultdict(int)
        total_offloaded_H_per_strategy_this_run: Dict[Strategy, int] = defaultdict(int)
        total_offloaded_L_per_strategy_this_run: Dict[Strategy, int] = defaultdict(int)

        allowed_strategies_list_this_run = strategies

        # 在每個 episode 中，應該基於主觀評估選擇策略
        for i_episode in range(params.n_episodes_per_strategy_eval):
            # 計算每個策略的主觀預期獎勵
            strategy_subjective_rewards = {}
            for strategy in allowed_strategies_list_this_run:
                strategy_subjective_rewards[strategy] = evaluate_strategy_subjective(strategy, params)

            # 選擇主觀預期獎勵最高的策略
            best_subjective_strategy = max(strategy_subjective_rewards.keys(),
                                         key=lambda s: strategy_subjective_rewards[s])
            chosen_strategy_tuple = tuple(best_subjective_strategy)

            # 使用包含後設認知的 simulate_trial
            reward, hits_H_norm, hits_L_norm, hits_H_surp, hits_L_surp, actual_offloaded_H, actual_offloaded_L = simulate_trial(params, chosen_strategy_tuple)

            total_reward_per_strategy_this_run[chosen_strategy_tuple] += reward
            sample_count_per_strategy_this_run[chosen_strategy_tuple] += 1
            total_hits_H_normal_per_strategy_this_run[chosen_strategy_tuple] += hits_H_norm
            total_hits_L_normal_per_strategy_this_run[chosen_strategy_tuple] += hits_L_norm
            total_hits_H_surprise_per_strategy_this_run[chosen_strategy_tuple] += hits_H_surp
            total_hits_L_surprise_per_strategy_this_run[chosen_strategy_tuple] += hits_L_surp
            total_offloaded_H_per_strategy_this_run[chosen_strategy_tuple] += actual_offloaded_H
            total_offloaded_L_per_strategy_this_run[chosen_strategy_tuple] += actual_offloaded_L

        mean_rewards_this_run: Dict[Strategy, float] = {}
        max_avg_reward_this_run = -float('inf')
        strategies_sampled_this_run = [s for s in strategies if sample_count_per_strategy_this_run[s] > 0]

        if not strategies_sampled_this_run:
            continue

        for strategy in strategies_sampled_this_run:
            if sample_count_per_strategy_this_run[strategy] > 0:
                mean_reward = total_reward_per_strategy_this_run[strategy] / sample_count_per_strategy_this_run[strategy]
                mean_rewards_this_run[strategy] = mean_reward
                if mean_reward > max_avg_reward_this_run:
                    max_avg_reward_this_run = mean_reward

        tolerance = 1e-9
        best_strategies_this_run = [
            s for s, avg_r in mean_rewards_this_run.items() if abs(avg_r - max_avg_reward_this_run) < tolerance
        ]

        best_strategy_for_this_run = random.choice(best_strategies_this_run)
        overall_best_strategy_counts[best_strategy_for_this_run] += 1

        samples_best_strategy_this_run = sample_count_per_strategy_this_run[best_strategy_for_this_run]

        if samples_best_strategy_this_run > 0:
            cumulative_best_strategy_total_hits_H_normal += total_hits_H_normal_per_strategy_this_run[best_strategy_for_this_run]
            cumulative_best_strategy_total_hits_L_normal += total_hits_L_normal_per_strategy_this_run[best_strategy_for_this_run]
            cumulative_best_strategy_total_hits_H_surprise += total_hits_H_surprise_per_strategy_this_run[best_strategy_for_this_run]
            cumulative_best_strategy_total_hits_L_surprise += total_hits_L_surprise_per_strategy_this_run[best_strategy_for_this_run]
            cumulative_best_strategy_total_offloaded_H += total_offloaded_H_per_strategy_this_run[best_strategy_for_this_run]
            cumulative_best_strategy_total_offloaded_L += total_offloaded_L_per_strategy_this_run[best_strategy_for_this_run]
            cumulative_best_strategy_total_episodes += samples_best_strategy_this_run
            successful_runs_count += 1

    total_item_opportunities_H = cumulative_best_strategy_total_episodes * params.n_high_items
    total_item_opportunities_L = cumulative_best_strategy_total_episodes * params.n_low_items

    mean_empirical_accuracy_H_normal = cumulative_best_strategy_total_hits_H_normal / total_item_opportunities_H if total_item_opportunities_H > 0 else 0.0
    mean_empirical_accuracy_L_normal = cumulative_best_strategy_total_hits_L_normal / total_item_opportunities_L if total_item_opportunities_L > 0 else 0.0
    mean_empirical_accuracy_H_surprise = cumulative_best_strategy_total_hits_H_surprise / total_item_opportunities_H if total_item_opportunities_H > 0 else 0.0
    mean_empirical_accuracy_L_surprise = cumulative_best_strategy_total_hits_L_surprise / total_item_opportunities_L if total_item_opportunities_L > 0 else 0.0
    mean_empirical_offload_H = cumulative_best_strategy_total_offloaded_H / total_item_opportunities_H if total_item_opportunities_H > 0 else 0.0
    mean_empirical_offload_L = cumulative_best_strategy_total_offloaded_L / total_item_opportunities_L if total_item_opportunities_L > 0 else 0.0

    strategy_proportions: Dict[Strategy, float] = {
        s: overall_best_strategy_counts[s] / successful_runs_count if successful_runs_count > 0 else 0.0 for s in strategies
    }

    print("\n最終策略選擇比例 (後設認知):")
    sorted_overall_proportions = sorted(strategy_proportions.items(), key=lambda item: item[1], reverse=True)
    for s, prop in sorted_overall_proportions:
        print(f"  策略 {s}: 比例 {prop:.3f}")

    print(f"\n平均經驗命中率和卸載率 (後設認知):")
    print(f"  正常準確率 - 高價值: {mean_empirical_accuracy_H_normal:.3f}")
    print(f"  正常準確率 - 低價值: {mean_empirical_accuracy_L_normal:.3f}")
    print(f"  意外準確率 - 高價值: {mean_empirical_accuracy_H_surprise:.3f}")
    print(f"  意外準確率 - 低價值: {mean_empirical_accuracy_L_surprise:.3f}")
    print(f"  卸載率 - 高價值: {mean_empirical_offload_H:.3f}")
    print(f"  卸載率 - 低價值: {mean_empirical_offload_L:.3f}")

    return {
        "strategy_proportions": strategy_proportions,
        "mean_accuracy_H": mean_empirical_accuracy_H_normal,
        "mean_accuracy_L": mean_empirical_accuracy_L_normal,
        "mean_accuracy_H_surprise": mean_empirical_accuracy_H_surprise,
        "mean_accuracy_L_surprise": mean_empirical_accuracy_L_surprise,
        "mean_offload_H": mean_empirical_offload_H,
        "mean_offload_L": mean_empirical_offload_L,
        "overall_best_strategy_counts": overall_best_strategy_counts
    }

# SDT 相關函数
def generate_evidence_signal(d_prime: float, is_correct: bool, criterion: float = 0.0) -> float:
    """
    基於 SDT 生成內部證據信號
    
    Args:
        d_prime: 任務敏感度
        is_correct: 實際答案是否正確
        criterion: 決策標準
        
    Returns:
        內部證據信號強度
    """
    if is_correct:
        # 正確答案的信號分佈: N(d'/2, 1)
        evidence = np.random.normal(d_prime / 2, 1)
    else:
        # 錯誤答案的信號分佈: N(-d'/2, 1)
        evidence = np.random.normal(-d_prime / 2, 1)
    
    return evidence + criterion

def compute_confidence_from_evidence(evidence: float, params: ModelParameters, 
                                   is_high_value: bool = False) -> float:
    """
    基於 SDT 框架從證據信號計算信心水平
    
    Args:
        evidence: 內部證據信號
        params: 模型參數
        is_high_value: 是否為高價值項目
        
    Returns:
        信心水平 (0-1)
    """
    # Type-2 信心判斷：基於證據強度的絕對值
    # 考慮後設認知效率和偏差
    confidence_signal = abs(evidence) * params.metacognitive_efficiency
    
    # 添加信心判斷的內部噪音
    confidence_signal += np.random.normal(0, params.confidence_noise_std)
    
    # 應用後設認知偏差（調整信心閾值）
    confidence_criterion = params.confidence_criterion_bias
    
    # 價值敏感的信心調整
    if is_high_value and params.value_sensitive_metacognition:
        confidence_criterion -= params.high_value_confidence_boost
    
    # 將信心信號轉換為 0-1 的機率
    # 使用累積常態分佈函數
    confidence = stats.norm.cdf(confidence_signal - confidence_criterion)
    
    return np.clip(confidence, 0, 1)

def make_offload_decision_sdt(confidence: float, item_value: float, 
                             params: ModelParameters) -> bool:
    """
    基於 SDT 和預期效用理論做出卸載決策
    
    Args:
        confidence: 對內部記憶的信心水平
        item_value: 項目價值
        params: 模型參數
        
    Returns:
        是否卸載 (True/False)
    """
    # 確定價值相關的信心閾值
    if item_value == params.value_high:
        base_threshold = params.offload_confidence_threshold_high
    else:
        base_threshold = params.offload_confidence_threshold_low
    
    # 計算預期效用
    # 不卸載的預期效用：信心 × 價值
    utility_no_offload = confidence * item_value
    
    # 卸載的預期效用：外部記憶準確率 × 價值 - 卸載成本
    utility_offload = params.accuracy_offloaded * item_value - params.cost_offloading
    
    # 結合閾值和效用計算做決策
    # 如果信心低於閾值且卸載效用更高，則卸載
    should_offload = (confidence < base_threshold) and (utility_offload > utility_no_offload)
    
    return should_offload

def simulate_trial_sdt(params: ModelParameters, strategy: Strategy) -> Tuple[float, int, int, int, int, int, int]:
    """
    基於 SDT 的後設認知模擬單次試驗
    """
    store_H_internal, store_L_internal, offload_H_allowed, offload_L_allowed = strategy
    
    total_reward = 0.0
    high_value_hits_normal_count = 0
    low_value_hits_normal_count = 0
    high_value_hits_surprise_count = 0
    low_value_hits_surprise_count = 0
    actual_offloaded_H_count = 0
    actual_offloaded_L_count = 0

    # 處理高價值項目
    for _ in range(params.n_high_items):
        # 生成任務實際表現（基於內部記憶準確率）
        p_internal_actual = get_internal_memory_accuracy((store_H_internal, store_L_internal), params)
        is_actually_correct = np.random.rand() < p_internal_actual if store_H_internal else False
        
        # 生成 SDT 證據信號
        evidence = generate_evidence_signal(params.d_prime, is_actually_correct, params.type1_criterion)
        
        # 計算信心水平
        confidence = compute_confidence_from_evidence(evidence, params, is_high_value=True)
        
        # 做出卸載決策
        decided_to_offload = False
        if offload_H_allowed:
            decided_to_offload = make_offload_decision_sdt(confidence, params.value_high, params)
        
        # 模擬回憶結果
        item_recalled_normal = False
        item_recalled_surprise = False
        
        if decided_to_offload:
            actual_offloaded_H_count += 1
            # 外部記憶回憶
            if np.random.rand() < params.accuracy_offloaded:
                item_recalled_normal = True
        else:
            # 內部記憶回憶
            if store_H_internal and is_actually_correct:
                item_recalled_normal = True
                item_recalled_surprise = True
        
        if item_recalled_normal:
            high_value_hits_normal_count += 1
            total_reward += params.value_high
        
        if item_recalled_surprise:
            high_value_hits_surprise_count += 1

    # 處理低價值項目（邏輯相同）
    for _ in range(params.n_low_items):
        p_internal_actual = get_internal_memory_accuracy((store_H_internal, store_L_internal), params)
        is_actually_correct = np.random.rand() < p_internal_actual if store_L_internal else False
        
        evidence = generate_evidence_signal(params.d_prime, is_actually_correct, params.type1_criterion)
        confidence = compute_confidence_from_evidence(evidence, params, is_high_value=False)
        
        decided_to_offload = False
        if offload_L_allowed:
            decided_to_offload = make_offload_decision_sdt(confidence, params.value_low, params)
        
        item_recalled_normal = False
        item_recalled_surprise = False
        
        if decided_to_offload:
            actual_offloaded_L_count += 1
            if np.random.rand() < params.accuracy_offloaded:
                item_recalled_normal = True
        else:
            if store_L_internal and is_actually_correct:
                item_recalled_normal = True
                item_recalled_surprise = True
        
        if item_recalled_normal:
            low_value_hits_normal_count += 1
            total_reward += params.value_low
        
        if item_recalled_surprise:
            low_value_hits_surprise_count += 1

    # 計算卸載成本
    total_reward -= actual_offloaded_H_count * params.cost_offloading
    total_reward -= actual_offloaded_L_count * params.cost_offloading

    return total_reward, high_value_hits_normal_count, low_value_hits_normal_count, high_value_hits_surprise_count, low_value_hits_surprise_count, actual_offloaded_H_count, actual_offloaded_L_count

def simulate_sdt_bias_effect(base_params: ModelParameters):
    """
    模擬 SDT 框架下後設認知偏差的影響
    """
    print(f"\n開始模擬 SDT 框架下的後設認知偏差影響...")
    
    # 測試不同的信心判斷偏差
    bias_values = np.linspace(-1.0, 1.0, 9)  # 從 -1 到 1
    
    results = {
        "biases": list(bias_values),
        "mean_offload_H": [],
        "mean_offload_L": [],
        "mean_confidence_H": [],
        "mean_confidence_L": []
    }
    
    strategies = get_all_strategies(allow_offloading=True)
    
    for bias in bias_values:
        print(f"  測試偏差值: {bias:.2f}")
        
        # 設定當前偏差參數
        current_params = replace(base_params, confidence_criterion_bias=bias)
        
        # 運行多次試驗統計
        total_offload_H = 0
        total_offload_L = 0
        total_items_H = 0
        total_items_L = 0
        confidence_samples_H = []
        confidence_samples_L = []
        
        for _ in range(100):  # 100 次試驗
            strategy = (True, True, True, True)  # 測試完整策略
            
            # 模擬並收集信心數據
            for _ in range(current_params.n_high_items):
                evidence = generate_evidence_signal(current_params.d_prime, True)  # 假設正確
                confidence = compute_confidence_from_evidence(evidence, current_params, is_high_value=True)
                confidence_samples_H.append(confidence)
                
                if make_offload_decision_sdt(confidence, current_params.value_high, current_params):
                    total_offload_H += 1
                total_items_H += 1
            
            for _ in range(current_params.n_low_items):
                evidence = generate_evidence_signal(current_params.d_prime, True)
                confidence = compute_confidence_from_evidence(evidence, current_params, is_high_value=False)
                confidence_samples_L.append(confidence)
                
                if make_offload_decision_sdt(confidence, current_params.value_low, current_params):
                    total_offload_L += 1
                total_items_L += 1
        
        # 計算平均值
        offload_rate_H = total_offload_H / total_items_H if total_items_H > 0 else 0
        offload_rate_L = total_offload_L / total_items_L if total_items_L > 0 else 0
        mean_confidence_H = np.mean(confidence_samples_H)
        mean_confidence_L = np.mean(confidence_samples_L)
        
        results["mean_offload_H"].append(offload_rate_H)
        results["mean_offload_L"].append(offload_rate_L)
        results["mean_confidence_H"].append(mean_confidence_H)
        results["mean_confidence_L"].append(mean_confidence_L)
    
    # 繪圖
    fig, axs = plt.subplots(1, 2, figsize=(12, 5))
    fig.suptitle("SDT-Based Metacognitive Bias Effects", fontsize=14)
    
    # 卸載率圖
    axs[0].plot(results["biases"], results["mean_offload_H"], 'o-', label="High-value", color='darkred')
    axs[0].plot(results["biases"], results["mean_offload_L"], 's-', label="Low-value", color='royalblue')
    axs[0].set_xlabel("Confidence Criterion Bias\n(Negative = Underconfident, Positive = Overconfident)")
    axs[0].set_ylabel("Mean Offloading Rate")
    axs[0].set_title("Offloading Rate vs. Metacognitive Bias")
    axs[0].legend()
    axs[0].grid(True, alpha=0.3)
    axs[0].axvline(x=0, color='gray', linestyle='--', alpha=0.7)
    
    # 平均信心圖
    axs[1].plot(results["biases"], results["mean_confidence_H"], 'o-', label="High-value", color='darkred')
    axs[1].plot(results["biases"], results["mean_confidence_L"], 's-', label="Low-value", color='royalblue')
    axs[1].set_xlabel("Confidence Criterion Bias")
    axs[1].set_ylabel("Mean Confidence Level")
    axs[1].set_title("Confidence Level vs. Metacognitive Bias")
    axs[1].legend()
    axs[1].grid(True, alpha=0.3)
    axs[1].axvline(x=0, color='gray', linestyle='--', alpha=0.7)
    
    plt.tight_layout()
    plt.show()
    
    return results

# --- 統一的模擬函數 ---
def run_unified_simulation(params: ModelParameters, strategies: List[Strategy], use_metacognition: bool = False) -> Dict[str, Any]:
    """
    統一的模擬函數，可以選擇是否使用後設認知

    Args:
        params: 模型參數
        strategies: 策略列表
        use_metacognition: 是否使用後設認知模擬

    Returns:
        模擬結果
    """
    if use_metacognition:
        return run_simulation_with_metacognition(params, strategies)
    else:
        return run_simulation_original(params, strategies)

# --- 模擬 Fig. 2: 不允許卸載 ---
def simulate_fig2(base_params: ModelParameters, use_metacognition: bool = False):
    method_name = "metacognition" if use_metacognition else "oringal"
    print(f"\n開始模擬 Fig. 2 (不允許考慮卸載) - {method_name}方法...")
    params_fig2 = replace(base_params,
        n_high_items=3, n_low_items=3,
    )
    # Fig.2 不允許【考慮】卸載
    strategies_fig2 = get_all_strategies(allow_offloading=False)
    # 使用統一的模擬函數
    results = run_unified_simulation(params_fig2, strategies_fig2, use_metacognition)

    # 匯總和打印結果
    prop_encode_low = sum(prop for s, prop in results["strategy_proportions"].items() if s[1])
    prop_encode_high = sum(prop for s, prop in results["strategy_proportions"].items() if s[0])
    acc_L_norm = results["mean_accuracy_L"]
    acc_H_norm = results["mean_accuracy_H"]

    print(f"Fig. 2 結果 (不允許考慮卸載) - {method_name}方法: ")
    print(f"  策略 - 編碼低價值比例: {prop_encode_low:.3f}")
    print(f"  策略 - 編碼高價值比例: {prop_encode_high:.3f}")
    print(f"  正常準確率 - 低價值: {acc_L_norm:.3f}")
    print(f"  正常準確率 - 高價值: {acc_H_norm:.3f}")

    # 繪圖
    fig, axs = plt.subplots(1, 2, figsize=(10, 4))
    fig.suptitle(f"Fig. 2 Simulation: No Offloading Allowed ({method_name})", fontsize=14)
    axs[0].bar(["Encode\nlow-value", "Encode\nhigh-value"], [prop_encode_low, prop_encode_high], color='cornflowerblue')
    axs[0].set_title("Strategy")
    axs[0].set_ylabel("Proportion of Runs Choosing Strategy")
    axs[0].set_ylim(0, 1.05)
    axs[1].bar(["Low-value", "High-value"], [acc_L_norm, acc_H_norm], color='cornflowerblue')
    axs[1].set_title("Mean Empirical Normal Accuracy")
    axs[1].set_ylabel("Accuracy")
    axs[1].set_ylim(0, 1.05)
    plt.tight_layout(rect=[0, 0, 1, 0.96])
    plt.show()

    return results

# --- 模擬 Fig. 3: 允許卸載 ---
def simulate_fig3(base_params: ModelParameters, use_metacognition: bool = False):
    method_name = "後設認知" if use_metacognition else "原始"
    print(f"\n開始模擬 Fig. 3 (允許考慮卸載) - {method_name}方法...")
    params_fig3 = replace(base_params,
        n_high_items=3, n_low_items=3,
    )
    # Fig.3 允許所有【考慮】卸載策略
    strategies_fig3 = get_all_strategies(allow_offloading=True)

    # 使用統一的模擬函數
    results = run_unified_simulation(params_fig3, strategies_fig3, use_metacognition)

    # 匯總和打印結果
    prop_encode_low = sum(prop for s, prop in results["strategy_proportions"].items() if s[1])
    prop_encode_high = sum(prop for s, prop in results["strategy_proportions"].items() if s[0])
    prop_offload_low_allowed = sum(prop for s, prop in results["strategy_proportions"].items() if s[3])
    prop_offload_high_allowed = sum(prop for s, prop in results["strategy_proportions"].items() if s[2])

    acc_L_norm = results["mean_accuracy_L"]
    acc_H_norm = results["mean_accuracy_H"]
    offload_L_rate = results["mean_offload_L"]
    offload_H_rate = results["mean_offload_H"]

    print(f"Fig. 3 結果 (允許考慮卸載) - {method_name}方法: ")
    print(f"  策略中允許編碼低價值比例: {prop_encode_low:.3f}")
    print(f"  策略中允許編碼高價值比例: {prop_encode_high:.3f}")
    print(f"  策略中允許卸載低價值比例: {prop_offload_low_allowed:.3f}")
    print(f"  策略中允許卸載高價值比例: {prop_offload_high_allowed:.3f}")
    print(f"  正常準確率 - 低價值: {acc_L_norm:.3f}")
    print(f"  正常準確率 - 高價值: {acc_H_norm:.3f}")
    print(f"  平均經驗卸載率 - 低價值: {offload_L_rate:.3f}")
    print(f"  平均經驗卸載率 - 高價值: {offload_H_rate:.3f}")

    # 繪圖
    fig, axs = plt.subplots(1, 2, figsize=(12, 5))
    fig.suptitle(f"Fig. 3 Simulation: Offloading Allowed ({method_name})", fontsize=14)

    strategy_labels = ["Encode\nlow-value", "Encode\nhigh-value", "Allow Offload\nlow-value", "Allow Offload\nhigh-value"]
    strategy_values = [prop_encode_low, prop_encode_high, prop_offload_low_allowed, prop_offload_high_allowed]
    axs[0].bar(strategy_labels, strategy_values, color='cornflowerblue')
    axs[0].set_title("Strategy Choice Proportion")
    axs[0].set_ylabel("Proportion of Runs Choosing Strategy")
    axs[0].set_ylim(0, 1.05)
    plt.setp(axs[0].get_xticklabels(), rotation=15, ha="right")

    axs[1].bar(["Low-value", "High-value"], [acc_L_norm, acc_H_norm], color='cornflowerblue')
    axs[1].set_title("Mean Empirical Normal Accuracy")
    axs[1].set_ylabel("Accuracy")
    axs[1].set_ylim(0, 1.05)

    plt.tight_layout(rect=[0, 0, 1, 0.96])
    plt.show()

    # 新增繪圖：平均經驗卸載率
    fig, ax = plt.subplots(figsize=(8, 6))
    fig.suptitle(f"Fig. 3 Simulation: Mean Empirical Offloading Rate", fontsize=14)
    ax.bar(["Low-value", "High-value"], [offload_L_rate, offload_H_rate], color=['cornflowerblue', 'brown'])
    ax.set_title("Mean Empirical Offloading Rate")
    ax.set_ylabel("Offloading Rate")
    ax.set_ylim(0, 1.05)
    plt.tight_layout(rect=[0, 0, 1, 0.96])
    plt.show()

    return results

# --- 模擬 Fig. 4: 只允許卸載高價值項目 ---
def simulate_fig4(base_params: ModelParameters):
    print(f"\n開始模擬 Fig. 4 (只允許考慮卸載高價值項目)...")
    params_fig4 = replace(base_params,
        n_high_items=3, n_low_items=3,
    )
    # 策略: 允許考慮卸載，但僅限高價值
    strategies_fig4 = get_all_strategies(allow_offloading=True, offload_restriction="high_only")

    # 使用原始的 run_simulation 函數
    results = run_simulation_original(params_fig4, strategies_fig4)

    # 匯總和打印結果
    prop_encode_low = sum(prop for s, prop in results["strategy_proportions"].items() if s[1])
    prop_encode_high = sum(prop for s, prop in results["strategy_proportions"].items() if s[0])
    prop_offload_low_allowed_check = sum(prop for s, prop in results["strategy_proportions"].items() if s[3])
    prop_offload_high_allowed = sum(prop for s, prop in results["strategy_proportions"].items() if s[2])

    acc_L_norm = results["mean_accuracy_L"]
    acc_H_norm = results["mean_accuracy_H"]
    offload_L_rate = results["mean_offload_L"]
    offload_H_rate = results["mean_offload_H"]

    print(f"Fig. 4 結果 (只允許考慮卸載高價值): ")
    print(f"  策略中允許編碼低價值比例: {prop_encode_low:.3f}")
    print(f"  策略中允許編碼高價值比例: {prop_encode_high:.3f}")
    print(f"  策略中允許卸載低價值比例 (應為0): {prop_offload_low_allowed_check:.3f}")
    print(f"  策略中允許卸載高價值比例: {prop_offload_high_allowed:.3f}")
    print(f"  正常準確率 - 低價值: {acc_L_norm:.3f}")
    print(f"  正常準確率 - 高價值: {acc_H_norm:.3f}")
    print(f"  平均經驗卸載率 - 低價值: {offload_L_rate:.3f}")
    print(f"  平均經驗卸載率 - 高價值: {offload_H_rate:.3f}")

    # 繪圖
    fig, axs = plt.subplots(1, 2, figsize=(12, 5))
    fig.suptitle(f"Fig. 4 Simulation: Only High-Value Offloading Allowed", fontsize=14)

    strategy_labels = ["Encode\nlow-value", "Encode\nhigh-value", "Allow Offload\nlow-value", "Allow Offload\nhigh-value"]
    strategy_values = [prop_encode_low, prop_encode_high, prop_offload_low_allowed_check, prop_offload_high_allowed]
    axs[0].bar(strategy_labels, strategy_values, color='cornflowerblue')
    axs[0].set_title("Strategy Choice Proportion")
    axs[0].set_ylabel("Proportion of Runs Choosing Strategy")
    axs[0].set_ylim(0, 1.05)
    plt.setp(axs[0].get_xticklabels(), rotation=15, ha="right")

    axs[1].bar(["Low-value", "High-value"], [acc_L_norm, acc_H_norm], color='cornflowerblue')
    axs[1].set_title("Mean Empirical Normal Accuracy")
    axs[1].set_ylabel("Accuracy")
    axs[1].set_ylim(0, 1.05)

    plt.tight_layout(rect=[0, 0, 1, 0.96])
    plt.show()

    # 新增繪圖：平均經驗卸載率
    fig, ax = plt.subplots(figsize=(8, 6))
    fig.suptitle(f"Fig. 4 Simulation: Mean Empirical Offloading Rate", fontsize=14)
    ax.bar(["Low-value", "High-value"], [offload_L_rate, offload_H_rate], color=['cornflowerblue', 'brown'])
    ax.set_title("Mean Empirical Offloading Rate")
    ax.set_ylabel("Offloading Rate")
    ax.set_ylim(0, 1.05)
    plt.tight_layout(rect=[0, 0, 1, 0.96])
    plt.show()

    return results

# --- 新增: 模擬 Fig. 5: 意外記憶測試 ---
def simulate_fig5(base_params: ModelParameters):
    print(f"\n開始模擬 Fig. 5 (意外記憶測試)...")

    params_common = replace(base_params,
        n_high_items=3, n_low_items=3,
    )

    # 條件1: 不允許考慮卸載
    print("  Fig. 5 條件1: 不允許考慮卸載 (意外測試)")
    strategies_cond1 = get_all_strategies(allow_offloading=False)
    results_cond1 = run_simulation_original(params_common, strategies_cond1)

    # 條件2: 只允許考慮卸載高價值項目
    print("  Fig. 5 條件2: 只允許考慮卸載高價值 (意外測試)")
    strategies_cond2 = get_all_strategies(allow_offloading=True, offload_restriction="high_only")
    results_cond2 = run_simulation_original(params_common, strategies_cond2)

    # 匯總和打印結果 (提取意外準確率)
    acc_L_cond1_surprise = results_cond1["mean_accuracy_L_surprise"]
    acc_H_cond1_surprise = results_cond1["mean_accuracy_H_surprise"]
    acc_L_cond2_surprise = results_cond2["mean_accuracy_L_surprise"]
    acc_H_cond2_surprise = results_cond2["mean_accuracy_H_surprise"]

    print(f"    意外測試準確率 - 不允許考慮卸載 - 低價值: {acc_L_cond1_surprise:.3f}, 高價值: {acc_H_cond1_surprise:.3f}")
    print(f"    意外測試準確率 - 只允許考慮卸載高價值 - 低價值: {acc_L_cond2_surprise:.3f}, 高價值: {acc_H_cond2_surprise:.3f}")

    # 繪圖
    fig, axs = plt.subplots(1, 2, figsize=(10, 4), sharey=True)
    fig.suptitle(f"Fig. 5 Simulation: Surprise-Test Accuracy", fontsize=14)

    axs[0].bar(["Low-value", "High-value"], [acc_L_cond1_surprise, acc_H_cond1_surprise], color='cornflowerblue')
    axs[0].set_title("No Offloading Allowed")
    axs[0].set_ylabel("Mean Empirical Surprise-Test Accuracy")
    axs[0].set_ylim(0, 1.0)

    axs[1].bar(["Low-value", "High-value"], [acc_L_cond2_surprise, acc_H_cond2_surprise], color='lightcoral')
    axs[1].set_title("High-Value Offloading Allowed")
    axs[1].set_ylim(0, 1.0)

    plt.tight_layout(rect=[0, 0, 1, 0.94])
    plt.show()
    return {"cond1": results_cond1, "cond2": results_cond2}

# --- 新增: 模擬 Fig. 6: 記憶負荷對卸載率的影響 ---
def simulate_fig6(base_params: ModelParameters):
    print(f"\n開始模擬 Fig. 6 (記憶負荷對卸載率的影響)...")

    # 條件1: 每種價值1個項目 (1 high, 1 low)
    print("  Fig. 6 條件1: 每種價值1個項目 (記憶負荷=2)")
    params_load1 = replace(base_params,
        n_high_items=1, n_low_items=1,
    )
    strategies_load1 = get_all_strategies(allow_offloading=True)
    results_load1 = run_simulation_original(params_load1, strategies_load1)

    # 匯總和打印結果
    offload_L_rate_load1 = results_load1["mean_offload_L"] # 平均經驗卸載率
    offload_H_rate_load1 = results_load1["mean_offload_H"] # 平均經驗卸載率
    print(f"    平均經驗卸載率 (記憶負荷=2) - 低價值: {offload_L_rate_load1:.3f}, 高價值: {offload_H_rate_load1:.3f}")

    # 條件2: 每種價值3個項目 (標準情況)
    print("  Fig. 6 條件2: 每種價值3個項目 (標準負荷=6)")
    params_load3 = replace(base_params,
        n_high_items=3, n_low_items=3,
    )
    strategies_load3 = get_all_strategies(allow_offloading=True)
    results_load3 = run_simulation_original(params_load3, strategies_load3)

    # 匯總和打印結果
    offload_L_rate_load3 = results_load3["mean_offload_L"] # 平均經驗卸載率
    offload_H_rate_load3 = results_load3["mean_offload_H"] # 平均經驗卸載率
    print(f"    平均經驗卸載率 (記憶負荷=6) - 低價值: {offload_L_rate_load3:.3f}, 高價值: {offload_H_rate_load3:.3f}")

    # 繪圖
    fig, axs = plt.subplots(1, 2, figsize=(10, 4), sharey=True)
    fig.suptitle(f"Fig. 6 Simulation: Effect of Memory Load on Offloading Rate", fontsize=14)

    axs[0].bar(["Low-value", "High-value"], [offload_L_rate_load1, offload_H_rate_load1], color='cornflowerblue')
    axs[0].set_title("Memory Load = 2")
    axs[0].set_ylabel("Mean Empirical Offloading Rate")
    axs[0].set_ylim(0, 1.0)

    axs[1].bar(["Low-value", "High-value"], [offload_L_rate_load3, offload_H_rate_load3], color='lightcoral')
    axs[1].set_title("Memory Load = 6")
    axs[1].set_ylim(0, 1.0)

    plt.tight_layout(rect=[0, 0, 1, 0.94])
    plt.show()
    return {"load1": results_load1, "load3": results_load3}

# --- 模擬 Fig. 7: 卸載成本的影響 ---
def simulate_fig7(base_params: ModelParameters):
    print(f"\n開始模擬 Fig. 7 (卸載成本的影響)...")
    # 卸載成本從 0.0 到 2.0 分成 9 個點
    offloading_costs = np.linspace(0.0, 2.0, 9)

    # 初始化結果存儲列表
    results_over_costs: Dict[str, List[Any]] = { # 使用 Any 來允許存儲不同類型的結果
        "costs": list(offloading_costs),
        "memory_encoding_rate_low": [], "memory_encoding_rate_high": [],
        "offloading_rate_low": [], "offloading_rate_high": [],
        "mean_accuracy_H_normal": [], "mean_accuracy_L_normal": [], # 添加經驗準確率存儲
        "mean_accuracy_H_surprise": [], "mean_accuracy_L_surprise": [] # 添加經驗意外準確率存儲
    }

    # 所有策略 (允許所有卸載選項)
    strategies_fig7 = get_all_strategies(allow_offloading=True)

    # 針對每個卸載成本運行模擬
    for cost_idx, cost in enumerate(offloading_costs):
        print(f"  模擬卸載成本 ({cost_idx+1}/{len(offloading_costs)}): {cost:.2f}")

        # 為當前成本創建參數對象
        current_params = ModelParameters(
            n_high_items=3, n_low_items=3,
            value_high=base_params.value_high, value_low=base_params.value_low,
            cost_offloading=cost, # 使用當前的卸載成本
            accuracy_internal_1_item=base_params.accuracy_internal_1_item,
            accuracy_internal_2_items=base_params.accuracy_internal_2_items,
            accuracy_internal_3_items=base_params.accuracy_internal_3_items,
            accuracy_internal_6_items=base_params.accuracy_internal_6_items,
            accuracy_offloaded=base_params.accuracy_offloaded,
            n_episodes_per_strategy_eval=base_params.n_episodes_per_strategy_eval, # 每個 run 的 episodes 數量
            n_model_runs=base_params.n_model_runs # 運行次數
        )

        # 使用 run_simulation 函數
        sim_results = run_simulation_original(current_params, strategies_fig7) # 移除 method 參數

        # 從結果中提取編碼率、卸載率和經驗準確率
        prop_encode_low = sum(prop for s, prop in sim_results["strategy_proportions"].items() if s[1])
        prop_encode_high = sum(prop for s, prop in sim_results["strategy_proportions"].items() if s[0])
        prop_offload_low = sum(prop for s, prop in sim_results["strategy_proportions"].items() if s[3])
        prop_offload_high = sum(prop for s, prop in sim_results["strategy_proportions"].items() if s[2])

        acc_H_norm = sim_results["mean_accuracy_H"] # 經驗正常準確率
        acc_L_norm = sim_results["mean_accuracy_L"] # 經驗正常準確率
        acc_H_surp = sim_results["mean_accuracy_H_surprise"] # 經驗意外準確率
        acc_L_surp = sim_results["mean_accuracy_L_surprise"] # 經驗意外準確率


        # 將結果添加到列表中
        results_over_costs["memory_encoding_rate_low"].append(prop_encode_low)
        results_over_costs["memory_encoding_rate_high"].append(prop_encode_high)
        results_over_costs["offloading_rate_low"].append(prop_offload_low)
        results_over_costs["offloading_rate_high"].append(prop_offload_high)
        results_over_costs["mean_accuracy_H_normal"].append(acc_H_norm)
        results_over_costs["mean_accuracy_L_normal"].append(acc_L_norm)
        results_over_costs["mean_accuracy_H_surprise"].append(acc_H_surp)
        results_over_costs["mean_accuracy_L_surprise"].append(acc_L_surp)

    print(f"Fig. 7 結果: ")
    for i, cost_val in enumerate(results_over_costs["costs"]):
        print(f"  成本={cost_val:.2f}: EncL={results_over_costs['memory_encoding_rate_low'][i]:.3f}, EncH={results_over_costs['memory_encoding_rate_high'][i]:.3f}, OffL={results_over_costs['offloading_rate_low'][i]:.3f}, OffH={results_over_costs['offloading_rate_high'][i]:.3f}, AccL_norm={results_over_costs['mean_accuracy_L_normal'][i]:.3f}, AccH_norm={results_over_costs['mean_accuracy_H_normal'][i]:.3f}, AccL_surp={results_over_costs['mean_accuracy_L_surprise'][i]:.3f}, AccH_surp={results_over_costs['mean_accuracy_H_surprise'][i]:.3f}")


    # 繪圖
    fig, axs = plt.subplots(1, 2, figsize=(12, 5))
    fig.suptitle(f"Fig. 7 Simulation: Effect of Offloading Cost", fontsize=14)
    axs[0].plot(results_over_costs["costs"], results_over_costs["memory_encoding_rate_low"], 'o-', label="Low-value", color='cornflowerblue')
    axs[0].plot(results_over_costs["costs"], results_over_costs["memory_encoding_rate_high"], 's-', label="High-value", color='brown')
    axs[0].set_title("Memory Encoding")
    axs[0].set_xlabel("Cost of Offloading")
    axs[0].set_ylabel("Memory Encoding Rate")
    axs[0].legend()
    axs[0].grid(True, linestyle='--', alpha=0.7)

    # 在右側子圖繪製卸載率和準確率
    # 移除 ax2 的創建和繪製準確率的部分
    # ax2 = axs[1].twinx() # 創建一個共用 x 軸的雙軸

    # 繪製卸載率 (使用左軸)
    axs[1].plot(results_over_costs["costs"], results_over_costs["offloading_rate_low"], 'o--', label="Offload Low", color='cornflowerblue')
    axs[1].plot(results_over_costs["costs"], results_over_costs["offloading_rate_high"], 's--', label="Offload High", color='brown')
    axs[1].set_ylabel("Offloading Rate")
    axs[1].set_ylim(0, 1.05)


    axs[1].set_title("Offloading Rate")
    axs[1].set_xlabel("Cost of Offloading")
    # 合併圖例 - 移除合併，只保留 axs[1] 的圖例
    # lines, labels = axs[1].get_legend_handles_labels()
    # lines2, labels2 = ax2.get_legend_handles_labels()
    # ax2.legend(lines + lines2, labels + labels2, loc='best')
    axs[1].legend(loc='best') # 只顯示 axs[1] 的圖例


    axs[1].grid(True, linestyle='--', alpha=0.7) # 網格線
    plt.tight_layout(rect=[0, 0, 1, 0.96])
    plt.show()

# Fig. 8 專用的 run_simulation 函數
def run_simulation_fig8_original(params: ModelParameters, internal_accuracy_scalar: float) -> Dict[str, float]:
    """
    運行 Fig. 8 原始蒙地卡羅模擬評估策略 (單一項目，不包含後設認知)
    """
    strategies_fig8 = get_strategies_fig8()

    overall_best_strategy_counts: Dict[StrategyFig8, int] = {s: 0 for s in strategies_fig8}
    cumulative_best_strategy_total_offloaded = 0
    cumulative_best_strategy_total_episodes = 0
    successful_runs_count = 0

    for i_run in range(params.n_model_runs):
        if (i_run + 1) % (max(1, params.n_model_runs // min(100, params.n_model_runs))) == 0:
            print(f"  運行中 Fig 8... {((i_run + 1) / params.n_model_runs) * 100:.0f}%")

        total_reward_per_strategy_this_run: Dict[StrategyFig8, float] = defaultdict(float)
        sample_count_per_strategy_this_run: Dict[StrategyFig8, int] = defaultdict(int)
        total_offloaded_per_strategy_this_run: Dict[StrategyFig8, int] = defaultdict(int)

        allowed_strategies_list_this_run = strategies_fig8

        for i_episode in range(params.n_episodes_per_strategy_eval):
            chosen_strategy = random.choice(allowed_strategies_list_this_run)
            chosen_strategy_tuple = tuple(chosen_strategy)

            # 使用原始的 simulate_trial_fig8 (不包含後設認知)
            reward, _, _, actual_offloaded = simulate_trial_fig8_original(params, chosen_strategy_tuple, internal_accuracy_scalar)

            total_reward_per_strategy_this_run[chosen_strategy_tuple] += reward
            sample_count_per_strategy_this_run[chosen_strategy_tuple] += 1
            total_offloaded_per_strategy_this_run[chosen_strategy_tuple] += actual_offloaded

        mean_rewards_this_run: Dict[StrategyFig8, float] = {}
        max_avg_reward_this_run = -float('inf')
        strategies_sampled_this_run = [s for s in strategies_fig8 if sample_count_per_strategy_this_run[s] > 0]

        if not strategies_sampled_this_run:
            continue

        for strategy in strategies_sampled_this_run:
            if sample_count_per_strategy_this_run[strategy] > 0:
                mean_reward = total_reward_per_strategy_this_run[strategy] / sample_count_per_strategy_this_run[strategy]
                mean_rewards_this_run[strategy] = mean_reward
                if mean_reward > max_avg_reward_this_run:
                    max_avg_reward_this_run = mean_reward

        tolerance = 1e-9
        best_strategies_this_run = [
            s for s, avg_r in mean_rewards_this_run.items() if abs(avg_r - max_avg_reward_this_run) < tolerance
        ]

        best_strategy_for_this_run = random.choice(best_strategies_this_run)
        overall_best_strategy_counts[best_strategy_for_this_run] += 1

        samples_best_strategy_this_run = sample_count_per_strategy_this_run[best_strategy_for_this_run]

        if samples_best_strategy_this_run > 0:
            cumulative_best_strategy_total_offloaded += total_offloaded_per_strategy_this_run[best_strategy_for_this_run]
            cumulative_best_strategy_total_episodes += samples_best_strategy_this_run
            successful_runs_count += 1

    total_runs_considered = successful_runs_count

    prop_encode_chosen = sum(count for (encode, offload_allowed), count in overall_best_strategy_counts.items() if encode) / total_runs_considered if total_runs_considered > 0 else 0.0
    prop_offload_allowed_chosen = sum(count for (encode, offload_allowed), count in overall_best_strategy_counts.items() if offload_allowed) / total_runs_considered if total_runs_considered > 0 else 0.0

    total_item_opportunities = cumulative_best_strategy_total_episodes * params.n_high_items

    mean_empirical_offload_rate = cumulative_best_strategy_total_offloaded / total_item_opportunities if total_item_opportunities > 0 else 0.0

    return {
        "prop_encode_chosen": prop_encode_chosen,
        "prop_offload_allowed_chosen": prop_offload_allowed_chosen,
        "mean_offload_rate": mean_empirical_offload_rate
    }

# Fig. 8 專用的 simulate_trial (原始版本)
def simulate_trial_fig8_original(params: ModelParameters, strategy: StrategyFig8, internal_accuracy_scalar: float) -> Tuple[float, bool, bool, int]:
    """
    模擬單次試驗並計算總獎勵和命中數 (Fig. 8 原始版本，不包含後設認知)
    """
    encode_strategy = strategy[0]
    offload_allowed = strategy[1]

    total_reward = 0.0
    item_recalled_normal = False
    item_recalled_surprise = False
    actual_offloaded_count = 0

    if encode_strategy and offload_allowed:
        # 同時使用內部記憶和卸載
        actual_offloaded_count = 1
        combined_success_prob = 1.0 - (1.0 - internal_accuracy_scalar) * (1.0 - params.accuracy_offloaded)
        if np.random.rand() < combined_success_prob:
            item_recalled_normal = True
        if np.random.rand() < internal_accuracy_scalar:
            item_recalled_surprise = True
    elif encode_strategy:
        # 只使用內部記憶
        if np.random.rand() < internal_accuracy_scalar:
            item_recalled_normal = True
            item_recalled_surprise = True
    elif offload_allowed:
        # 只使用卸載
        actual_offloaded_count = 1
        if np.random.rand() < params.accuracy_offloaded:
            item_recalled_normal = True

    if item_recalled_normal:
        total_reward += params.value_high

    # 計算卸載成本
    if actual_offloaded_count > 0:
        total_reward -= actual_offloaded_count * params.cost_offloading

    return total_reward, item_recalled_normal, item_recalled_surprise, actual_offloaded_count

# 修改 simulate_fig8 函數以使用原始版本
def simulate_fig8(base_params: ModelParameters):
    print(f"\n開始模擬 Fig. 8 (內部記憶準確率的影響)...")

    # Fig. 8 變化的是內部記憶準確率
    internal_accuracies_to_test = np.linspace(0.55, 0.95, 9)

    # 初始化結果存儲列表
    low_cost_offload_rates = []
    high_cost_offload_rates = []
    internal_acc_values_recorded = []

    # Fig 8 模擬的是單一高價值項目
    params_base_fig8 = replace(base_params,
        n_high_items=1, n_low_items=0,
        value_high=base_params.value_high, value_low=0,
    )

    # 遍歷不同的內部記憶準確率
    for int_acc in internal_accuracies_to_test:
        print(f"  模擬內部記憶準確率: {int_acc:.2f}")

        # 低卸載成本條件
        params_low_cost_fig8 = replace(params_base_fig8,
            cost_offloading=1.0
        )
        results_low_cost = run_simulation_fig8_original(params_low_cost_fig8, int_acc)
        low_cost_offload_rates.append(results_low_cost['mean_offload_rate'])


        # 高卸載成本條件
        params_high_cost_fig8 = replace(params_base_fig8,
            cost_offloading=2.0
        )
        results_high_cost = run_simulation_fig8_original(params_high_cost_fig8, int_acc)
        high_cost_offload_rates.append(results_high_cost['mean_offload_rate'])

        # 記錄當前使用的 internal accuracy 值
        internal_acc_values_recorded.append(int_acc)

    # 計算成本敏感度
    cost_sensitivity = np.array(low_cost_offload_rates) - np.array(high_cost_offload_rates)

    print(f"Fig. 8 結果: ")
    for i, int_acc_val in enumerate(internal_acc_values_recorded):
        print(f"  內部準確率={int_acc_val:.2f}: 低成本卸載率={low_cost_offload_rates[i]:.3f}, 高成本卸載率={high_cost_offload_rates[i]:.3f}, 成本敏感度={cost_sensitivity[i]:.3f}")

    # 繪圖
    fig, axs = plt.subplots(1, 2, figsize=(12, 5))
    fig.suptitle(f"Fig. 8 Simulation: Effect of Internal Accuracy", fontsize=14)

    # 左圖：卸載率 vs 內部記憶準確率
    axs[0].plot(internal_acc_values_recorded, low_cost_offload_rates, 'o-', label="Low Cost (cost=1)", color='blue')
    axs[0].plot(internal_acc_values_recorded, high_cost_offload_rates, 's-', label="High Cost (cost=2)", color='darkred')
    axs[0].set_title("Mean Empirical Offloading Rate")
    axs[0].set_xlabel("Internal Accuracy")
    axs[0].set_ylabel("Mean Empirical Offloading Rate")
    axs[0].set_ylim(0, 1.05)
    axs[0].legend()
    axs[0].grid(True, linestyle='--', alpha=0.7)

    # 右圖：成本敏感度 vs 內部記憶準確率
    axs[1].plot(internal_acc_values_recorded, cost_sensitivity, '^-', label="Cost Sensitivity", color='green')
    axs[1].set_title("Cost Sensitivity")
    axs[1].set_xlabel("Internal Accuracy")
    axs[1].set_ylabel("Cost Sensitivity (Offload Rate Diff)")
    y_min_sens = min(0, min(cost_sensitivity) * 1.2 if cost_sensitivity.size > 0 else 0)
    y_max_sens = max(0.55, max(cost_sensitivity) * 1.2 if cost_sensitivity.size > 0 else 0.55)
    axs[1].set_ylim(y_min_sens, y_max_sens)

    axs[1].legend()
    axs[1].grid(True, linestyle='--', alpha=0.7)

    plt.tight_layout(rect=[0, 0, 1, 0.96])
    plt.show()

    return {"internal_accuracies": internal_acc_values_recorded, "low_cost_offload": low_cost_offload_rates, "high_cost_offload": high_cost_offload_rates, "cost_sensitivity": high_cost_offload_rates}

# --- 新增: 模擬 後設認知閾值的影響 ---
def simulate_metacognition_effect(base_params: ModelParameters):
    print(f"\n開始模擬 後設認知閾值的影響...")

    # 定義要測試的後設認知閾值範圍
    # 閾值適用於內部記憶準確率 (0 到 1)。從 0.0 到 1.0 測試一些點。
    metacognitive_thresholds = np.linspace(0.0, 1.0, 11) # 例如: 0.0, 0.1, ..., 1.0

    # 初始化結果存储列表
    results_over_thresholds: Dict[str, List[Any]] = {
        "thresholds": list(metacognitive_thresholds),
        "mean_offload_H": [], # 存储平均經驗卸載率
        "mean_offload_L": [], # 存储平均經驗卸載率
        "mean_accuracy_H_normal": [],
        "mean_accuracy_L_normal": [],
        "mean_accuracy_H_surprise": [],
        "mean_accuracy_L_surprise": []
    }

    # 使用標準策略集 (允許對高低價值項目都考慮卸載) 來進行測試
    strategies_to_test = get_all_strategies(allow_offloading=True)

    # 遍歷不同的後設認知閾值
    for threshold_idx, threshold in enumerate(metacognitive_thresholds):
        print(f"  模擬後設認知閾值 ({threshold_idx+1}/{len(metacognitive_thresholds)}): {threshold:.2f}")

        # 為當前閾值創建參數對象 (使用 base_params 的其餘參數)
        current_params = replace(base_params,
            metacognitive_threshold=threshold # 設定當前的後設認知閾值
            # 其他參數從 base_params 複製
        )

        # 運行模擬 (使用新的 run_simulation_with_metacognition 函數)
        sim_results = run_simulation_with_metacognition(current_params, strategies_to_test)

        # 存儲相關結果 (特別是平均經驗卸載率和準確率)
        results_over_thresholds["mean_offload_H"].append(sim_results["mean_offload_H"])
        results_over_thresholds["mean_offload_L"].append(sim_results["mean_offload_L"])
        results_over_thresholds["mean_accuracy_H_normal"].append(sim_results["mean_accuracy_H"])
        results_over_thresholds["mean_accuracy_L_normal"].append(sim_results["mean_accuracy_L"])
        results_over_thresholds["mean_accuracy_H_surprise"].append(sim_results["mean_accuracy_H_surprise"])
        results_over_thresholds["mean_accuracy_L_surprise"].append(sim_results["mean_accuracy_L_surprise"])

    # 打印结果
    print(f"\n後設認知閾值影響模擬結果:")
    for i, thresh_val in enumerate(results_over_thresholds["thresholds"]):
        print(f"  閾值={thresh_val:.2f}: OffH={results_over_thresholds['mean_offload_H'][i]:.3f}, OffL={results_over_thresholds['mean_offload_L'][i]:.3f}, AccH_norm={results_over_thresholds['mean_accuracy_H_normal'][i]:.3f}, AccL_norm={results_over_thresholds['mean_accuracy_L_normal'][i]:.3f}, AccH_surp={results_over_thresholds['mean_accuracy_H_surprise'][i]:.3f}, AccL_surp={results_over_thresholds['mean_accuracy_L_surprise'][i]:.3f}")

    # 繪圖 (平均經驗卸載率 vs. 後設認知閾值)
    fig, ax = plt.subplots(figsize=(10, 6))
    fig.suptitle(f"Mean Empirical Offloading Rate vs. Metacognitive Threshold", fontsize=14)

    # 使用紅色線條和圓形標記繪製高價值項目
    ax.plot(results_over_thresholds["thresholds"], results_over_thresholds["mean_offload_H"], 'o-', label="High-value items", color='darkred')
    # 使用藍色線條和方形標記繪製低價值項目
    ax.plot(results_over_thresholds["thresholds"], results_over_thresholds["mean_offload_L"], 's-', label="Low-value items", color='royalblue')

    ax.set_xlabel("Metacognitive Threshold (Perceived Internal Accuracy)")
    ax.set_ylabel("Mean Empirical Offloading Rate")
    ax.set_ylim(0, 1.05)
    ax.legend()
    ax.grid(True, linestyle='--', alpha=0.7)

    # 確保圖表顯示完整

    plt.tight_layout()
    plt.show()

    # 可以選擇也繪製準確率隨閾值變化的圖表，如果需要的話
    fig, axs = plt.subplots(1, 2, figsize=(10, 4), sharey=True)
    fig.suptitle(f"Effect of Metacognitive Threshold on Mean Empirical Accuracy", fontsize=14)
    axs[0].plot(results_over_thresholds["thresholds"], results_over_thresholds["mean_accuracy_H_normal"], 'o-', label="High-value Normal", color='brown')
    axs[0].plot(results_over_thresholds["thresholds"], results_over_thresholds["mean_accuracy_L_normal"], 's-', label="Low-value Normal", color='cornflowerblue')
    axs[0].set_title("Normal Accuracy")
    axs[0].set_xlabel("Metacognitive Threshold")
    axs[0].set_ylabel("Mean Empirical Accuracy")
    axs[0].set_ylim(0, 1.05)
    axs[0].legend()
    axs[0].grid(True, linestyle='--', alpha=0.7)

    axs[1].plot(results_over_thresholds["thresholds"], results_over_thresholds["mean_accuracy_H_surprise"], 'o--', label="High-value Surprise", color='brown')
    axs[1].plot(results_over_thresholds["thresholds"], results_over_thresholds["mean_accuracy_L_surprise"], 's--', label="Low-value Surprise", color='cornflowerblue')
    axs[1].set_title("Surprise Accuracy")
    axs[1].set_xlabel("Metacognitive Threshold")
    axs[1].set_ylim(0, 1.05)
    axs[1].legend()
    axs[1].grid(True, linestyle='--', alpha=0.7)

    plt.tight_layout(rect=[0, 0, 1, 0.96])
    plt.show()

    return results_over_thresholds


# --- 新增: 模擬後設認知偏差的影響 (基於 Hu, Luo, & Fleming 2019) ---
def simulate_metacognitive_bias_effect(base_params: ModelParameters):
    """
    模擬後設認知偏差對策略選擇和卸載行為的影響。
    基於 Hu, Luo, & Fleming (2019) 的理論，探討不同程度的後設認知偏差
    如何影響個體對內部記憶的主觀評估，進而影響卸載決策。

    Args:
        base_params: 基礎模型參數

    Returns:
        不同後設認知偏差下的模擬結果
    """
    print(f"\n開始模擬後設認知偏差的影響 (基於 Hu, Luo, & Fleming 2019)...")

    # 定義要測試的後設認知偏差範圍
    # 負值表示低估內部記憶能力，正值表示高估內部記憶能力
    metacognitive_biases = np.linspace(-0.3, 0.3, 7)  # 例如: -0.3, -0.2, -0.1, 0, 0.1, 0.2, 0.3

    # 初始化結果存儲列表
    results_over_biases: Dict[str, List[Any]] = {
        "biases": list(metacognitive_biases),
        "mean_offload_H": [],  # 存儲平均經驗卸載率
        "mean_offload_L": [],  # 存儲平均經驗卸載率
        "mean_accuracy_H_normal": [],
        "mean_accuracy_L_normal": [],
        "mean_accuracy_H_surprise": [],
        "mean_accuracy_L_surprise": []
    }

    # 使用標準策略集進行測試
    strategies_to_test = get_all_strategies(allow_offloading=True)

    # 遍歷不同的後設認知偏差
    for bias_idx, bias in enumerate(metacognitive_biases):
        print(f"  模擬後設認知偏差 ({bias_idx+1}/{len(metacognitive_biases)}): {bias:.2f}")

        # 為當前偏差創建參數對象
        current_params = replace(base_params,
            metacognitive_bias=bias  # 設定當前的後設認知偏差
            # 其他參數從 base_params 複製
        )

        # 運行模擬 (使用新的 run_simulation_with_metacognition 函數)
        sim_results = run_simulation_with_metacognition(current_params, strategies_to_test)

        # 存儲相關結果
        results_over_biases["mean_offload_H"].append(sim_results["mean_offload_H"])
        results_over_biases["mean_offload_L"].append(sim_results["mean_offload_L"])
        results_over_biases["mean_accuracy_H_normal"].append(sim_results["mean_accuracy_H"])
        results_over_biases["mean_accuracy_L_normal"].append(sim_results["mean_accuracy_L"])
        results_over_biases["mean_accuracy_H_surprise"].append(sim_results["mean_accuracy_H_surprise"])
        results_over_biases["mean_accuracy_L_surprise"].append(sim_results["mean_accuracy_L_surprise"])

    # 繪图 (後設認知偏差 vs. 卸載率)
    fig, ax = plt.subplots(figsize=(10, 6))
    fig.suptitle(f"Effect of Metacognitive Bias on Mean Empirical Offloading Rate", fontsize=14)

    # 使用紅色線條和圓形標記繪製高價值項目
    ax.plot(results_over_biases["biases"], results_over_biases["mean_offload_H"], 'o-', label="High-value items", color='darkred')
    # 使用藍色線條和方形標記繪製低價值項目
    ax.plot(results_over_biases["biases"], results_over_biases["mean_offload_L"], 's-', label="Low-value items", color='royalblue')

    ax.set_xlabel("Metacognitive Bias\n(Negative = Underconfidence, Positive = Overconfidence)")
    ax.set_ylabel("Mean Empirical Offloading Rate")
    ax.set_ylim(0, 1.05)
    ax.axvline(x=0, color='gray', linestyle='--', alpha=0.7)  # 添加垂直線表示無偏差
    ax.legend()
    ax.grid(True, linestyle='--', alpha=0.7)

    # 確保圖表顯示完整
    plt.tight_layout()
    plt.show()

    # 繪製準確率隨後設認知偏差變化的圖表
    fig, axs = plt.subplots(1, 2, figsize=(12, 5), sharey=True)
    fig.suptitle(f"Effect of Metacognitive Bias on Mean Empirical Accuracy", fontsize=14)

    # 正常準確率
    axs[0].plot(results_over_biases["biases"], results_over_biases["mean_accuracy_H_normal"], 'o-', label="High-value Normal", color='darkred')
    axs[0].plot(results_over_biases["biases"], results_over_biases["mean_accuracy_L_normal"], 's-', label="Low-value Normal", color='cornflowerblue')
    axs[0].set_title("Normal Accuracy")
    axs[0].set_xlabel("Metacognitive Bias")
    axs[0].set_ylabel("Mean Empirical Accuracy")
    axs[0].set_ylim(0, 1.05)
    axs[0].legend()
    axs[0].grid(True, linestyle='--', alpha=0.7)

    # 意外準確率
    axs[1].plot(results_over_biases["biases"], results_over_biases["mean_accuracy_H_surprise"], 'o--', label="High-value Surprise", color='darkred')
    axs[1].plot(results_over_biases["biases"], results_over_biases["mean_accuracy_L_surprise"], 's--', label="Low-value Surprise", color='cornflowerblue')
    axs[1].set_title("Surprise Accuracy")
    axs[1].set_xlabel("Metacognitive Bias")
    axs[1].set_ylim(0, 1.05)
    axs[1].legend()
    axs[1].grid(True, linestyle='--', alpha=0.7)

    plt.tight_layout(rect=[0, 0, 1, 0.96])
    plt.show()

    return results_over_biases


# --- 比較原始方法和後設認知方法的函數 ---
def compare_methods_for_figure(figure_name: str, base_params: ModelParameters, simulate_func):
    """
    比較原始方法和後設認知方法在特定圖表上的表現

    Args:
        figure_name: 圖表名稱 (如 "Fig. 2")
        base_params: 基礎參數
        simulate_func: 模擬函數

    Returns:
        包含兩種方法結果的字典
    """
    print(f"\n=== {figure_name} 方法比較 ===")

    # 運行原始方法
    print(f"\n--- {figure_name} 原始方法 ---")
    results_original = simulate_func(base_params, use_metacognition=False)

    # 運行後設認知方法
    print(f"\n--- {figure_name} 後設認知方法 ---")
    results_metacognition = simulate_func(base_params, use_metacognition=True)

    # 比較關鍵指標
    print(f"\n--- {figure_name} 方法比較結果 ---")
    print(f"高價值準確率: 原始={results_original['mean_accuracy_H']:.3f}, 後設認知={results_metacognition['mean_accuracy_H']:.3f}")
    print(f"低價值準確率: 原始={results_original['mean_accuracy_L']:.3f}, 後設認知={results_metacognition['mean_accuracy_L']:.3f}")

    if 'mean_offload_H' in results_original:
        print(f"高價值卸載率: 原始={results_original['mean_offload_H']:.3f}, 後設認知={results_metacognition['mean_offload_H']:.3f}")
        print(f"低價值卸載率: 原始={results_original['mean_offload_L']:.3f}, 後設認知={results_metacognition['mean_offload_L']:.3f}")

    return {
        'original': results_original,
        'metacognition': results_metacognition
    }


if __name__ == '__main__':
    shared_base_params = ModelParameters(
        n_model_runs=50,  # 減少運行次數以加快測試
        n_episodes_per_strategy_eval=10,  # 減少每次運行的episodes

        # 舊模型後設認知參數的預設值 (確保這些參數存在於 ModelParameters 中)
        metacognitive_threshold=0.85,
        metacognitive_bias=0.0,
        load_dependent_confidence=True,
        load_confidence_penalty=0.01,
        # confidence_noise_std=0.05, # 使用共用的 confidence_noise_std
        # value_sensitive_metacognition=True, # 使用共用的 value_sensitive_metacognition
        # high_value_confidence_boost=0.03, # 使用共用的 high_value_confidence_boost

        # SDT 後設認知參數的預設值 (確保這些參數存在於 ModelParameters 中)
        d_prime=1.5,
        type1_criterion=0.0,
        metacognitive_efficiency=0.8,
        confidence_criterion_bias=0.0,
        confidence_noise_std=0.05, # 設定共用的 confidence_noise_std 預設值
        offload_confidence_threshold_high=0.6,
        offload_confidence_threshold_low=0.4,
        value_sensitive_metacognition=True, # 設定共用的 value_sensitive_metacognition 預設值
        high_value_confidence_boost=0.03, # 設定共用的 high_value_confidence_boost 預設值
    )

    print(f"--- 執行 SDT 後設認知神經網路模擬 ---")
    print(f"使用以下參數:")
    print(f"  - Type-1 d': {shared_base_params.d_prime}")
    print(f"  - Type-1 criterion: {shared_base_params.type1_criterion}")
    print(f"  - 後設認知效率 (SDT): {shared_base_params.metacognitive_efficiency}")
    print(f"  - 信心判斷偏差 (SDT): {shared_base_params.confidence_criterion_bias}")
    print(f"  - 信心噪音標準差: {shared_base_params.confidence_noise_std}")
    print(f"  - 卸載信心閾值 (高價值, SDT): {shared_base_params.offload_confidence_threshold_high}")
    print(f"  - 卸載信心閾值 (低價值, SDT): {shared_base_params.offload_confidence_threshold_low}")
    print(f"  - 價值敏感後設認知: {shared_base_params.value_sensitive_metacognition}")
    print(f"  - 高價值信心加成: {shared_base_params.high_value_confidence_boost}")
    print(f"  - 後設認知閾值 (舊): {shared_base_params.metacognitive_threshold}")
    print(f"  - 後設認知偏差 (舊): {shared_base_params.metacognitive_bias}")
    print(f"  - 負荷依賴信心 (舊): {shared_base_params.load_dependent_confidence}")
    print(f"  - 負荷信心懲罰 (舊): {shared_base_params.load_confidence_penalty}")


    # 比較原始方法和後設認知方法在 Fig 2-4 上的表現 (這裡的後設認知方法現在使用 SDT 邏輯)
    # Fig 2 和 Fig 4 的模擬仍然使用 run_simulation_original，因為這兩個圖表模擬的是不允許或部分允許考慮卸載，
    # 即策略空間受限，而不是模擬後設認知決策過程本身對卸載的影響。
    # Fig 3 允許所有策略，我們在這裡比較使用 run_simulation_original (理論最優策略) 和 run_simulation_with_metacognition (基於主觀評估的策略) 的差異。
    print(f"\n--- 執行圖表模擬 (比較原始方法與 SDT 後設認知方法) ---")

    # Fig 2 使用原始方法 (不允許考慮卸載)
    results_fig2_original = simulate_fig2(shared_base_params, use_metacognition=False)
    # Fig 3 比較原始方法和 SDT 後設認知方法
    comparison_fig3_sdt = compare_methods_for_figure("Fig. 3 (SDT Metacognition)", shared_base_params, simulate_fig3)
    # Fig 4 使用原始方法 (只允許考慮卸載高價值)
    results_fig4_original = simulate_fig4(shared_base_params)


    # 執行其他圖表的原始模擬 (這些圖表主要展示認知卸載的基本效應，不涉及後設認知決策過程)
    print(f"\n--- 執行其他圖表模擬 (使用原始方法) ---")
    results_fig5 = simulate_fig5(shared_base_params)
    results_fig6 = simulate_fig6(shared_base_params)
    results_fig7 = simulate_fig7(shared_base_params)
    results_fig8 = simulate_fig8(shared_base_params)


    # 測試 SDT 後設認知參數的影響 (使用 SDT 模擬函數)
    print(f"\n--- 測試 SDT 後設認知參數的影響 ---")
    results_metacognition_thresholds = simulate_metacognition_effect(shared_base_params) # 現在變化卸載信心閾值
    results_metacognition_bias_sdt = simulate_sdt_bias_effect(shared_base_params) # 變化信心判斷偏差

    print(f"\n=== 模擬結束 ===")

