############
#initialise#
############

source('initialise_fig8.R')

#these are the cost values we will test
int_acc=seq(0.55,0.95,length.out=9)

#####################
#run all simulations#
#####################

low_effort=double(0)
high_effort=double(0)

for (remember_internal in int_acc) {
  #display the current offload cost, to check progress
  print(remember_internal)
  
  offload_cost=1
  
  source('run_simulation_fig8.R')
  
  best_policy=best_policy/runs
  
  low_effort=c(low_effort,best_policy[OFFLOAD])

  offload_cost=2
  
  best_policy=integer(2) 
  source('run_simulation_fig8.R')
  
  best_policy=best_policy/runs
  
  high_effort=c(high_effort,best_policy[OFFLOAD])
}

##############
#plot results#
##############
library(patchwork)
library(ggplot2)

effort_diff = low_effort-high_effort

plot_data = data.frame(int_acc=int_acc, low_effort=low_effort, high_effort=high_effort, effort_diff=effort_diff)

leftplot = ggplot(plot_data, aes(x=int_acc)) +
  geom_line(aes(y = low_effort), color = "blue") +
  geom_line(aes(y = high_effort), color="darkred") +
  ggtitle("Offloading") +
  scale_x_continuous(name="Internal accuracy") +
  scale_y_continuous(name="Offloading rate") +
  theme(panel.border = element_blank(),
        # Remove panel grid lines
        panel.grid.major = element_blank(),
        panel.grid.minor = element_blank(),
        # Remove panel background
        panel.background = element_rect(fill = "white", colour = "black", size = 1, linetype = "solid"),
        # Style text
        text = element_text(size = 14),
        plot.title = element_text(hjust = 0.5))

rightplot = ggplot(plot_data, aes(x = int_acc)) +
  geom_line(aes(y = low_effort, color = "Low cost"), alpha=0) +
  geom_line(aes(y = high_effort, color = "High cost"), alpha=0) +
  geom_line(aes(y = effort_diff, color = "Cost sensitivity")) +
  ggtitle("Cost sensitivity") +
  scale_x_continuous(name = "Internal accuracy") +
  scale_y_continuous(name = "Cost sensitivity", limits = c(0, 0.5)) +
  scale_color_manual(
    name = "",
    values = c("Low cost" = "blue", "High cost" = "darkred", "Cost sensitivity" = "green"),
    labels = c("Low cost", "High cost", "Cost sensitivity"),
    breaks = c("Low cost", "High cost", "Cost sensitivity") 
  ) +
  theme(panel.border = element_blank(),
        panel.grid.major = element_blank(),
        panel.grid.minor = element_blank(),
        panel.background = element_rect(fill = "white", colour = "black", size = 1, linetype = "solid"),
        legend.text = element_text(size = 14),
        legend.key = element_rect(colour = "transparent", fill = "white"),
        legend.position = "right",
        text = element_text(size = 14),
        plot.title = element_text(hjust = 0.5))

graph = leftplot + rightplot

pdf(file="fig.pdf", width = 10, height = 4)

print(graph)
dev.off()

