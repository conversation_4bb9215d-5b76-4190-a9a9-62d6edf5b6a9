##############################
#set up the summary variables#
##############################

#which policy was best? encoded according to the four orthogonal decisions:
#1.encode 2.offload
best_policy=integer(2) 

#hit rates, stored separately for 1. low-value, 2. high-value
hits=integer(1) 
surprise_hits=integer(1) 

#####################
#run the simulations#
#####################

for (r in 1:runs) {
  rewards=integer(4) #use this variable to collect the total reward for each policy
  samples=integer(4) #number of samples collected for each policy
  
  hit=matrix(0,nrow=4,ncol=1) #number of hits for each policy, separate for high- and low-value
  surprise_hit=matrix(0,nrow=4,ncol=1) #number of surprise hits, i.e. hits from internal memory alone
  
  for (e in 1:episodes) {
    #first pick a random policy, making sure that it is allowed
    valid_policy=FALSE 
    
    while (valid_policy==FALSE) {
      #select a random policy, a number from 1-4
      policy=sample.int(4,1)
      
      #set up the strategy, according to this policy
      #we subtract 1 because we want to convert policies 1-16 to 0-15 so that we can work out binary equivalent
      encode_strategy=integer(1)
      encode_strategy=intToBits(policy-1)[ENCODE]==TRUE
      
      offload_strategy=integer(1)
      offload_strategy=intToBits(policy-1)[OFFLOAD]==TRUE
      
      #check whether the selected policy is allowed
      valid_policy=TRUE
      
      
      if ((offload_strategy==TRUE)&(offloading_allowed==FALSE)) {
        valid_policy=FALSE
      }
    }
    
    reward=0 #initialise the reward variable, collecting the total reward on this run
    memory_set=NULL #initialise the memory set, containing all the memorised items
    offload_set=NULL #initialise the offload set, containing all the offloaded items
    
    #loop over items and offload/encode into memory according to strategy
    for (item in 1:nItems) {
      if (offload_strategy) { #do we offload items of this value?
        #if so, add it to the offload set...
        offload_set=c(offload_set,item)
        #... and subtract the offloading cost
        if (item==1) { #only aply the cost to the first item
          reward=reward-offload_cost
        }
      }
      
      if (encode_strategy) { #do we encode items of this value into memory?
        #if so, add it to the memory set
        memory_set=c(memory_set,item)
      }
    }
    
    #now retrieve the items
    for (item in 1:nItems) {
      #set this to true if the item was recalled
      item_recalled=FALSE
      
      #is the item in the memory set?
      if (sum(item==memory_set)==1) {
        #recall the item with the specified probability
        if(runif(1)<remember_internal) {
          item_recalled=TRUE
          
          #we can now work out the surprise hits, ignoring any information stored in the offloaded set
          surprise_hit[policy]=surprise_hit[policy]+1
        }
      }
      
      #is the item in the offloaded set?
      if (sum(item==offload_set)==1) {
        #recall the item
        if(runif(1)<remember_external) {
          item_recalled=TRUE
        }
      }
      
      #now work out the reward, and the hit rates
      if (item_recalled) {
        hit[policy]=hit[policy]+1
        reward=reward+reward_value
      }
    }
    
    #record the reward associated with this policy, and the number of times it has been sampled
    rewards[policy]=rewards[policy]+reward
    samples[policy]=samples[policy]+1
  }
  
  #now work out the winning strategy, by calculating the mean reward for each policy
  mean_rewards=rewards/samples
  
  #which policy is associated with highest reward?
  maxReward=which(mean_rewards==max(mean_rewards[!is.na(mean_rewards)])) #need to exclude policies that were never sampled
  #if there are multiple policies associated with the same reward, pick one at random
  maxReward=maxReward[sample(length(maxReward),1)]
  
  #now record results in the summary variables
  
  #which policy was best?
  best_policy=best_policy+as.integer(intToBits(maxReward-1)) #we need the -1 because we want policies 0-15 for binary conversion
  
  #what were the hit rates associated with this policy?
  hits=hits+(hit[maxReward,]/(samples[maxReward]*nItems))
  
  #what were the hit rates associated with this policy, using the memory set alone?
  surprise_hits=surprise_hits+(surprise_hit[maxReward,]/(samples[maxReward]*nItems))
}