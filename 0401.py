#卷積神經網路是因為輸入太多，所以可以用一些方法讓輸入降低維度，用filter來做
#filter會把輸入的資料做卷積，卷積的方式有很多種，例如平均、最大、最小等等
#先將輸入變成卷積層跟池化層，再將卷積層跟池化層變成全連接層(隱藏層，輸出層)進行學習
#卷積(convolution)是一種線性操作，它將一個矩陣與一個過濾器(filter)進行向量內積，得到一個新的矩陣
#池化(pooling)是一種非線性操作，它將一個矩陣縮減為一個數字，然後做為下一層的輸入
#卷積層的輸出會是一個矩陣，而池化層的輸出會是一個數字
#filter可以自由設定，例如3x3、5x5等等，其形狀也能自己設定，例如正方形、長方形、圓形等等
#filter的數量也可以自由設定，例如10、20、30等等
#filter的數量會影響到卷積層的輸出，例如filter的數量為10，則卷積層的輸出為10個矩陣


from tensorflow.keras.datasets import mnist
import matplotlib.pyplot as plt
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import Dense  
import numpy as np
# 導入將標籤轉換為one-hot編碼的工具
from tensorflow.keras.utils import to_categorical

# 載入MNIST數據集，分為訓練集和測試集
(train_images, train_labels), (test_images, test_labels) = mnist.load_data()

# 顯示第一張訓練圖片，使用二進制顏色映射
plt.imshow(train_images[0], cmap='binary')
# plt.show()

# 定義一個函數來顯示圖片，設定圖片大小為1x1英寸
def show_image(image):
    fig = plt.gcf()
    fig.set_size_inches(1,1)
    plt.imshow(image, cmap='binary')
    plt.show()

# 將訓練圖片reshape成一維向量(784=28*28)，並轉換為float32類型
train_vec = train_images.reshape(len(train_images), 28*28).astype('float32')
# 將測試圖片做相同的處理
test_vec = test_images.reshape(len(test_images), 28*28).astype('float32') 

# 將像素值正規化到0-1之間，0代表黑色，255代表白色，中間值代表不同程度的灰色
train_vec = train_vec/255
test_vec = test_vec/255

# 將標籤轉換為one-hot編碼
train_labels = to_categorical(train_labels)

test_labels = to_categorical(test_labels)

# 創建序列模型
model = Sequential()

# 添加第一層Dense層，輸入維度784，輸出維度256，使用sigmoid激活函數
model.add(Dense(units=256, input_dim=784,kernel_initializer = "normal" ,activation='sigmoid'))

# 添加輸出層，10個神經元對應10個數字，使用softmax激活函數
model.add(Dense(units=10, kernel_initializer = "normal",activation='softmax'))

# 編譯模型，設定損失函數、優化器和評估指標
model.compile(loss='categorical_crossentropy', optimizer='adam', metrics=['accuracy'])

# 訓練模型，設定驗證集比例、訓練輪數和批次大小
model.fit(train_vec, train_labels,validation_split=0.2 ,  epochs=10, batch_size=100, verbose=2)
test_score=model.evaluate(test_vec, test_labels)
print(test_score)

prediction =  model.predict(test_vec)
# print(prediction[0])
# print(np.argmax(prediction[0]))
# ... 前面的代碼保持不變 ...

# 獲取預測結果
prediction = model.predict(test_vec)
# 獲取第一張測試圖片的真實標籤
true_label = np.argmax(test_labels[0])
# 獲取模型對第一張圖片的預測結果
predicted_label = np.argmax(prediction[0])

print(f"輸入號碼為: {true_label}")
print(f"最終經過模型學習，預測號碼為: {predicted_label}")