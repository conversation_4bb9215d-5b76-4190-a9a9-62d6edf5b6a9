import numpy as np

# # w = np.array([0.7,0.7])
# w = np.array([np.random.normal(loc = 0 , scale = 0.1),np.random.normal(loc = 0 , scale = 0.1)]) #定義常態分配中心位置和標偉差
# print(w)
# x = np.array([[0,0],[1,0],[0,1],[1,1]])  #and-gate，or-gate(a=10,wei = 0.7,0.7,bias = 0.5)
# t = np.array([0,1,1,1])
# m = np.dot(x , w)

# a = 10
# bias = 0.5
# # y = 1/(1+np.exp(-a*(m-bias)))
# # print(y)

# def get_output(inp , wei):
#     m = np.dot(inp , wei)
#     return 1/(1+np.exp(-a*(m-bias)))

# y = np.array([get_output(x[0,:] , w),get_output(x[1,:],w),get_output(x[2,:],w),get_output(x[3,:],w)])
# print(y)


# lr = 0.1 #學習常數
# y = get_output(x[3,:],w)

# def delta_learning(target,outp,inp):
#     delta = target-outp
#     derva = a*outp*(1-outp) #sigmoid專屬
#     return lr*delta*derva*inp

  

# # delta = (t[3]-y)
# # print(delta)
# # dw = delta_learning(t[3], y, x[3])
# # w = w+dw
# # print(w)
# # y = get_output(x[3,:],w)

# er = np.zeros(100)
# ##迴路
# for r in range(100):
#     for i in range(4):
#         y = get_output(x[i,:],w)
#         dw = delta_learning(t[i], y, x[i,:])
#         w = w+dw
#         er[r]=er[r]+0.5*(t[i]-y)**2 #舊誤差加上新誤差，舊誤差一開始是0

# y = np.array([get_output(x[0,:] , w),get_output(x[1,:],w),get_output(x[2,:],w),get_output(x[3,:],w)])
# print(y)
# print(w)
# print(er)




##########################################
w1 = np.array(np.random.normal(loc = 0 , scale = 0.1, size = (2,3)))
w2 = w = np.array([np.random.normal(loc = 0 , scale = 0.1),np.random.normal(loc = 0 , scale = 0.1),np.random.normal(loc = 0 , scale = 0.1)])
print(w1)
print(w2)

x = np.array([[0,0],[1,0],[0,1],[1,1]])  #and-gate，or-gate(a=10,wei = 0.7,0.7,bias = 0.5)
t = np.array([0,1,1,1])

a = 10
def sigmoid(s):
    a = 10
    bias = 0.7
    return 1/(1+np.exp(-a*(s-bias)))

def get_output(inp,wei):
    m =np.dot(inp,wei)
    y = sigmoid(m)
    return y

# y = np.array([[get_output(x[0,:],w1,w2)],[get_output(x[1,:],w1,w2)],[get_output(x[2,:],w1,w2)],[get_output(x[3,:],w1,w2)]])
# print(y)

for i in range(4):
    y1 = get_output(x[i,:],w1)
    y2 = get_output(y1,w2)
    print(y2)

y1 = get_output(x[1,:],w1)
y2 = get_output(y1,w2)
delta2 = a*(t-y2)*y2*(1-y2)
# dw2 = delta2*y1
delta1 = delta2*w2*a*y1*(1-y1)
print(delta1)
print(delta2)