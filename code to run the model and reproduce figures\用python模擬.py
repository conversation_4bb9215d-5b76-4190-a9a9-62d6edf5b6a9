import numpy as np
import matplotlib.pyplot as plt
from dataclasses import dataclass, field
from typing import List, Tuple, Dict, Any
import itertools
from collections import defaultdict

# 傳統中文註釋

@dataclass
class ModelParameters:
    """模型使用的參數"""
    n_high_items: int = 3  # 高價值項目數量 (預設為 Fig.2,3,7 標準情況)
    n_low_items: int = 3   # 低價值項目數量 (預設為 Fig.2,3,7 標準情況)
    value_high: float = 8.0  # 高價值項目的價值
    value_low: float = 2.0   # 低價值項目的價值
    cost_offloading: float = 1.5  # 卸載一個項目的成本 (預設值，會在模擬Fig.7時改變)
    
    # 內部記憶準確率，根據總內部記憶項目數
    accuracy_internal_1_item: float = 0.95  # Fig.6 需要
    accuracy_internal_2_items: float = 0.925 # Fig.6 需要
    accuracy_internal_3_items: float = 0.90  # 標準情況: 只記一種類型 (3個)
    accuracy_internal_6_items: float = 0.75  # 標準情況: 記兩種類型 (6個)
    accuracy_offloaded: float = 0.98         # 外部卸載項目的準確率
    
    # 模擬控制參數
    n_episodes_per_strategy_eval: int = 50  # 論文中 "Number of simulated trials per run"
    n_model_runs: int = 10000 # 論文中使用 1,000,000，這裡用較小值以便快速運行
    
    # 強化學習參數
    learning_rate: float = 0.1  # Q-learning 學習率
    discount_factor: float = 0.95  # Q-learning 折扣因子
    epsilon: float = 0.1  # ε-greedy 探索率

# 策略表示: (store_H_internal, store_L_internal, offload_H, offload_L)
# 每個元素是布林值 (True/False)
Strategy = Tuple[bool, bool, bool, bool]

class QLearningAgent:
    """Q-learning 代理"""
    def __init__(self, params: ModelParameters):
        self.params = params
        self.q_table = defaultdict(lambda: defaultdict(float))  # Q(s,a) 表
        self.learning_rate = params.learning_rate
        self.discount_factor = params.discount_factor
        self.epsilon = params.epsilon
    
    def get_state(self, stored_H: int, stored_L: int) -> Tuple[int, int]:
        """將當前記憶狀態轉換為狀態表示"""
        return (stored_H, stored_L)
    
    def get_action(self, state: Tuple[int, int]) -> Strategy:
        """使用 ε-greedy 策略選擇動作"""
        if np.random.random() < self.epsilon:  # 探索
            return tuple(np.random.choice([True, False], size=4))
        
        # 利用：選擇Q值最大的動作
        actions = list(self.q_table[state].keys())
        if not actions:  # 如果是新狀態
            return tuple(np.random.choice([True, False], size=4))
        
        return max(actions, key=lambda a: self.q_table[state][a])
    
    def update(self, state: Tuple[int, int], action: Strategy, reward: float, next_state: Tuple[int, int]):
        """更新Q值"""
        # 獲取下一個狀態的最大Q值
        next_q = max([self.q_table[next_state][a] for a in self.q_table[next_state]]) if self.q_table[next_state] else 0
        
        # Q-learning更新公式
        current_q = self.q_table[state][action]
        self.q_table[state][action] = current_q + self.learning_rate * (reward + self.discount_factor * next_q - current_q)

def get_all_strategies(allow_offloading: bool = True, offload_restriction: str = "none") -> List[Strategy]:
    """
    產生所有可能的策略
    offload_restriction: "none", "high_only" (只允許卸載高價值), "no_low" (不允許卸載低價值)
    """
    options_sh = [True, False] # Store High Internal
    options_sl = [True, False] # Store Low Internal

    if not allow_offloading:
        options_oh = [False] # Offload High
        options_ol = [False] # Offload Low
    else:
        if offload_restriction == "high_only":
            options_oh = [True, False]
            options_ol = [False] # 低價值不允許卸載
        elif offload_restriction == "no_low": # 與 "high_only" 效果相同，如果只有兩種價值
            options_oh = [True, False]
            options_ol = [False]
        else: # "none" restriction or other unhandled
            options_oh = [True, False]
            options_ol = [True, False]
            
    return list(itertools.product(options_sh, options_sl, options_oh, options_ol))


def get_internal_memory_accuracy(strategy: Strategy, params: ModelParameters) -> float:
    """根據策略和當前參數計算內部記憶的實際準確率"""
    items_stored_internally = 0
    if strategy[0]: # store_H_internal
        items_stored_internally += params.n_high_items
    if strategy[1]: # store_L_internal
        items_stored_internally += params.n_low_items

    if items_stored_internally == 0:
        return 0.0
    elif items_stored_internally == 1:
        return params.accuracy_internal_1_item
    elif items_stored_internally == 2:
        return params.accuracy_internal_2_items
    elif items_stored_internally == 3: # 論文中 "3 items" 的情況 (只記一種類型)
        # 這裡的邏輯基於總內部項目數
        # 如果 params.n_high_items 或 params.n_low_items 不是3，這個標籤可能不準確
        # 但在標準情況 (3+3) 和 Fig.6 (1+1) 中，這個分支會正確處理負荷為3的情況
        return params.accuracy_internal_3_items
    elif items_stored_internally == (params.n_high_items + params.n_low_items) and params.n_high_items == 3: # 標準情況 6 items
         return params.accuracy_internal_6_items
    # 處理 Fig.6 中 1+1 = 2 個項目的情況，已在上面 items_stored_internally == 2 處理
    else:
        # 對於其他未明確定義的負載，可以使用插值或最接近的保守值
        # 為了簡化，如果不是明確的1,2,3,6，我們可能需要更詳細的規則
        # 但對於本模擬中涉及的場景，上述分支應該足夠
        # print(f"警告: 未明確定義的內部記憶負載 {items_stored_internally} (H:{params.n_high_items}, L:{params.n_low_items})")
        if items_stored_internally > 3 : return params.accuracy_internal_6_items # 默認更差的情況
        if items_stored_internally > 0 : return params.accuracy_internal_3_items
        return 0.0


def simulate_trial(strategy: Strategy, params: ModelParameters) -> float:
    """模擬單次試驗並計算總獎勵"""
    total_reward = 0.0
    p_internal_actual = get_internal_memory_accuracy(strategy, params)

    # 處理高價值項目
    for _ in range(params.n_high_items):
        prob_remember_H = 0.0
        p_H_internal_component = p_internal_actual if strategy[0] else 0.0
        p_H_offload_component = params.accuracy_offloaded if strategy[2] else 0.0
        if strategy[0] and strategy[2]:
            prob_remember_H = 1.0 - (1.0 - p_H_internal_component) * (1.0 - p_H_offload_component)
        elif strategy[0]:
            prob_remember_H = p_H_internal_component
        elif strategy[2]:
            prob_remember_H = p_H_offload_component
        if np.random.rand() < prob_remember_H:
            total_reward += params.value_high
    if strategy[2]:
        total_reward -= params.n_high_items * params.cost_offloading

    # 處理低價值項目
    for _ in range(params.n_low_items):
        prob_remember_L = 0.0
        p_L_internal_component = p_internal_actual if strategy[1] else 0.0
        p_L_offload_component = params.accuracy_offloaded if strategy[3] else 0.0
        if strategy[1] and strategy[3]:
            prob_remember_L = 1.0 - (1.0 - p_L_internal_component) * (1.0 - p_L_offload_component)
        elif strategy[1]:
            prob_remember_L = p_L_internal_component
        elif strategy[3]:
            prob_remember_L = p_L_offload_component
        if np.random.rand() < prob_remember_L:
            total_reward += params.value_low
    if strategy[3]:
        total_reward -= params.n_low_items * params.cost_offloading
        
    return total_reward

def evaluate_strategy(strategy: Strategy, params: ModelParameters) -> float:
    """評估一個策略在多次試驗中的平均獎勵"""
    rewards = [simulate_trial(strategy, params) for _ in range(params.n_episodes_per_strategy_eval)]
    return np.mean(rewards)

def calculate_accuracy_for_strategy(strategy: Strategy, params: ModelParameters, mode: str = "normal") -> Tuple[float, float]:
    """
    計算給定策略下高價值和低價值項目的預期準確率
    mode: "normal" 或 "surprise_test" (只考慮內部記憶)
    """
    p_internal_actual = get_internal_memory_accuracy(strategy, params)

    # 高價值項目預期準確率
    acc_H = 0.0
    p_H_internal_component = p_internal_actual if strategy[0] else 0.0
    if mode == "surprise_test":
        acc_H = p_H_internal_component # 意外測試只看內部記憶
    else: # normal mode
        p_H_offload_component = params.accuracy_offloaded if strategy[2] else 0.0
        if strategy[0] and strategy[2]:
            acc_H = 1.0 - (1.0 - p_H_internal_component) * (1.0 - p_H_offload_component)
        elif strategy[0]:
            acc_H = p_H_internal_component
        elif strategy[2]:
            acc_H = p_H_offload_component

    # 低價值項目預期準確率
    acc_L = 0.0
    p_L_internal_component = p_internal_actual if strategy[1] else 0.0
    if mode == "surprise_test":
        acc_L = p_L_internal_component # 意外測試只看內部記憶
    else: # normal mode
        p_L_offload_component = params.accuracy_offloaded if strategy[3] else 0.0
        if strategy[1] and strategy[3]:
            acc_L = 1.0 - (1.0 - p_L_internal_component) * (1.0 - p_L_offload_component)
        elif strategy[1]:
            acc_L = p_L_internal_component
        elif strategy[3]:
            acc_L = p_L_offload_component
            
    return acc_H, acc_L

def run_simulation_main_loop(params: ModelParameters, strategies: List[Strategy], accuracy_mode: str = "normal", use_rl: bool = False) -> Dict[str, Any]:
    """主模擬迴圈，返回策略選擇比例和平均準確率"""
    if not use_rl:
        # 原始的窮舉策略方法
        strategy_counts: Dict[Strategy, int] = {s: 0 for s in strategies}
        total_accuracy_H = 0.0
        total_accuracy_L = 0.0

        for i_run in range(params.n_model_runs):
            if (i_run + 1) % (max(1, params.n_model_runs // 10)) == 0:
                print(f"  運行中... {((i_run + 1) / params.n_model_runs) * 100:.0f}%")
                
            best_strategy_for_run: Strategy = strategies[0]
            max_avg_reward = -float('inf')

            for strategy_idx, strategy in enumerate(strategies):
                avg_reward = evaluate_strategy(strategy, params)
                if avg_reward > max_avg_reward:
                    max_avg_reward = avg_reward
                    best_strategy_for_run = strategy
            
            strategy_counts[best_strategy_for_run] += 1
            
            acc_H, acc_L = calculate_accuracy_for_strategy(best_strategy_for_run, params, mode=accuracy_mode)
            total_accuracy_H += acc_H
            total_accuracy_L += acc_L
            
        strategy_proportions: Dict[Strategy, float] = {s: count / params.n_model_runs for s, count in strategy_counts.items()}
        mean_accuracy_H = total_accuracy_H / params.n_model_runs
        mean_accuracy_L = total_accuracy_L / params.n_model_runs
        
        return {
            "strategy_proportions": strategy_proportions,
            "mean_accuracy_H": mean_accuracy_H,
            "mean_accuracy_L": mean_accuracy_L,
            "strategy_counts": strategy_counts # 用於調試
        }
    else:
        # 使用Q-learning進行強化學習
        agent = QLearningAgent(params)
        strategy_counts: Dict[Strategy, int] = {s: 0 for s in strategies}
        total_accuracy_H = 0.0
        total_accuracy_L = 0.0
        episode_rewards = []

        for i_run in range(params.n_model_runs):
            if (i_run + 1) % (max(1, params.n_model_runs // 10)) == 0:
                print(f"  運行中... {((i_run + 1) / params.n_model_runs) * 100:.0f}%")

            # 初始狀態：沒有記憶任何項目
            state = agent.get_state(0, 0)
            action = agent.get_action(state)
            
            # 執行動作並獲得獎勵
            reward = simulate_trial(action, params)
            episode_rewards.append(reward)
            
            # 計算下一個狀態
            next_stored_H = params.n_high_items if action[0] else 0
            next_stored_L = params.n_low_items if action[1] else 0
            next_state = agent.get_state(next_stored_H, next_stored_L)
            
            # 更新Q值
            agent.update(state, action, reward, next_state)
            
            # 記錄策略使用情況和準確率
            strategy_counts[action] += 1
            acc_H, acc_L = calculate_accuracy_for_strategy(action, params, mode=accuracy_mode)
            total_accuracy_H += acc_H
            total_accuracy_L += acc_L

        strategy_proportions = {s: count / params.n_model_runs for s, count in strategy_counts.items()}
        mean_accuracy_H = total_accuracy_H / params.n_model_runs
        mean_accuracy_L = total_accuracy_L / params.n_model_runs
        mean_reward = np.mean(episode_rewards)

        return {
            "strategy_proportions": strategy_proportions,
            "mean_accuracy_H": mean_accuracy_H,
            "mean_accuracy_L": mean_accuracy_L,
            "strategy_counts": strategy_counts,
            "mean_reward": mean_reward,
            "episode_rewards": episode_rewards,
            "q_table": dict(agent.q_table)  # 轉換為普通字典以便序列化
        }

# --- 模擬 Fig. 2: 不允許卸載 (與之前版本相同) ---
def simulate_fig2(base_params: ModelParameters):
    print("開始模擬 Fig. 2 (不允許卸載)...")
    # Fig.2 的參數是標準的 3 個高價值, 3 個低價值項目
    params_fig2 = ModelParameters(
        n_high_items=3, n_low_items=3,
        value_high=base_params.value_high, value_low=base_params.value_low,
        cost_offloading=base_params.cost_offloading, # 雖然不允許卸載，但保持一致
        accuracy_internal_1_item=base_params.accuracy_internal_1_item,
        accuracy_internal_2_items=base_params.accuracy_internal_2_items,
        accuracy_internal_3_items=base_params.accuracy_internal_3_items,
        accuracy_internal_6_items=base_params.accuracy_internal_6_items,
        accuracy_offloaded=base_params.accuracy_offloaded,
        n_episodes_per_strategy_eval=base_params.n_episodes_per_strategy_eval,
        n_model_runs=base_params.n_model_runs
    )
    strategies_fig2 = get_all_strategies(allow_offloading=False)
    results = run_simulation_main_loop(params_fig2, strategies_fig2, accuracy_mode="normal")
    
    prop_encode_low = sum(prop for s, prop in results["strategy_proportions"].items() if s[1])
    prop_encode_high = sum(prop for s, prop in results["strategy_proportions"].items() if s[0])
    acc_L = results["mean_accuracy_L"]
    acc_H = results["mean_accuracy_H"]

    print(f"Fig. 2 結果: ")
    print(f"  策略 - 編碼低價值比例: {prop_encode_low:.3f}")
    print(f"  策略 - 編碼高價值比例: {prop_encode_high:.3f}")
    print(f"  準確率 - 低價值: {acc_L:.3f}")
    print(f"  準確率 - 高價值: {acc_H:.3f}")

    fig, axs = plt.subplots(1, 2, figsize=(10, 4))
    fig.suptitle("Fig. 2 Simulation: Offloading Not Allowed (不允許卸載)", fontsize=14)
    axs[0].bar(["Encode\nlow-value", "Encode\nhigh-value"], [prop_encode_low, prop_encode_high], color='cornflowerblue')
    axs[0].set_title("Strategy (策略選擇)")
    axs[0].set_ylabel("Proportion (比例)")
    axs[0].set_ylim(0, 1.05)
    axs[1].bar(["Low-value", "High-value"], [acc_L, acc_H], color='cornflowerblue')
    axs[1].set_title("Accuracy (記憶準確率)")
    axs[1].set_ylabel("Mean Accuracy (平均準確率)")
    axs[1].set_ylim(0, 1.05)
    plt.tight_layout(rect=[0, 0, 1, 0.96])
    plt.show()
    return results

# --- 模擬 Fig. 3: 允許卸載 (與之前版本相同) ---
def simulate_fig3(base_params: ModelParameters):
    print("\n開始模擬 Fig. 3 (允許卸載)...")
    params_fig3 = ModelParameters(
        n_high_items=3, n_low_items=3,
        value_high=base_params.value_high, value_low=base_params.value_low,
        cost_offloading=base_params.cost_offloading,
        accuracy_internal_1_item=base_params.accuracy_internal_1_item,
        accuracy_internal_2_items=base_params.accuracy_internal_2_items,
        accuracy_internal_3_items=base_params.accuracy_internal_3_items,
        accuracy_internal_6_items=base_params.accuracy_internal_6_items,
        accuracy_offloaded=base_params.accuracy_offloaded,
        n_episodes_per_strategy_eval=base_params.n_episodes_per_strategy_eval,
        n_model_runs=base_params.n_model_runs
    )
    strategies_fig3 = get_all_strategies(allow_offloading=True)
    results = run_simulation_main_loop(params_fig3, strategies_fig3, accuracy_mode="normal")

    prop_encode_low = sum(prop for s, prop in results["strategy_proportions"].items() if s[1])
    prop_encode_high = sum(prop for s, prop in results["strategy_proportions"].items() if s[0])
    prop_offload_low = sum(prop for s, prop in results["strategy_proportions"].items() if s[3])
    prop_offload_high = sum(prop for s, prop in results["strategy_proportions"].items() if s[2])
    acc_L = results["mean_accuracy_L"]
    acc_H = results["mean_accuracy_H"]

    print(f"Fig. 3 結果: ")
    print(f"  策略 - 編碼低價值比例: {prop_encode_low:.3f}")
    print(f"  策略 - 編碼高價值比例: {prop_encode_high:.3f}")
    print(f"  策略 - 卸載低價值比例: {prop_offload_low:.3f}")
    print(f"  策略 - 卸載高價值比例: {prop_offload_high:.3f}")
    print(f"  準確率 - 低價值: {acc_L:.3f}")
    print(f"  準確率 - 高價值: {acc_H:.3f}")

    fig, axs = plt.subplots(1, 2, figsize=(12, 5))
    fig.suptitle("Fig. 3 Simulation: Offloading Allowed (允許卸載)", fontsize=14)
    strategy_labels = ["Encode\nlow-value", "Encode\nhigh-value", "Offload\nlow-value", "Offload\nhigh-value"]
    strategy_values = [prop_encode_low, prop_encode_high, prop_offload_low, prop_offload_high]
    axs[0].bar(strategy_labels, strategy_values, color='cornflowerblue')
    axs[0].set_title("Strategy (策略選擇)")
    axs[0].set_ylabel("Proportion (比例)")
    axs[0].set_ylim(0, 1.05)
    plt.setp(axs[0].get_xticklabels(), rotation=15, ha="right")
    axs[1].bar(["Low-value", "High-value"], [acc_L, acc_H], color='cornflowerblue')
    axs[1].set_title("Accuracy (記憶準確率)")
    axs[1].set_ylabel("Mean Accuracy (平均準確率)")
    axs[1].set_ylim(0, 1.05)
    plt.tight_layout(rect=[0, 0, 1, 0.96])
    plt.show()
    return results

# --- 新增: 模擬 Fig. 4: 只允許卸載高價值項目 ---
def simulate_fig4(base_params: ModelParameters):
    print("\n開始模擬 Fig. 4 (只允許卸載高價值項目)...")
    # 根據論文描述，Fig.4 的情境是 "participants were only allowed to offload high-value items"
    # 比較對象是 Fig.1 (即我們的 Fig.2, 不允許卸載時) 低價值項目的準確率
    params_fig4 = ModelParameters(
        n_high_items=3, n_low_items=3, # 標準項目數量
        value_high=base_params.value_high, value_low=base_params.value_low,
        cost_offloading=base_params.cost_offloading, # 使用基礎卸載成本
        accuracy_internal_1_item=base_params.accuracy_internal_1_item,
        accuracy_internal_2_items=base_params.accuracy_internal_2_items,
        accuracy_internal_3_items=base_params.accuracy_internal_3_items,
        accuracy_internal_6_items=base_params.accuracy_internal_6_items,
        accuracy_offloaded=base_params.accuracy_offloaded,
        n_episodes_per_strategy_eval=base_params.n_episodes_per_strategy_eval,
        n_model_runs=base_params.n_model_runs
    )
    # 策略: 允許卸載，但僅限高價值 (即 offload_L 總是 False)
    strategies_fig4 = get_all_strategies(allow_offloading=True, offload_restriction="high_only")
    
    results = run_simulation_main_loop(params_fig4, strategies_fig4, accuracy_mode="normal")

    prop_encode_low = sum(prop for s, prop in results["strategy_proportions"].items() if s[1])
    prop_encode_high = sum(prop for s, prop in results["strategy_proportions"].items() if s[0])
    # prop_offload_low 應該為0，因為策略不允許
    prop_offload_low_check = sum(prop for s, prop in results["strategy_proportions"].items() if s[3])
    prop_offload_high = sum(prop for s, prop in results["strategy_proportions"].items() if s[2])
    
    acc_L = results["mean_accuracy_L"]
    acc_H = results["mean_accuracy_H"]

    print(f"Fig. 4 結果 (只允許卸載高價值): ")
    print(f"  策略 - 編碼低價值比例: {prop_encode_low:.3f}")
    print(f"  策略 - 編碼高價值比例: {prop_encode_high:.3f}")
    print(f"  策略 - 卸載低價值比例 (應為0): {prop_offload_low_check:.3f}")
    print(f"  策略 - 卸載高價值比例: {prop_offload_high:.3f}")
    print(f"  準確率 - 低價值: {acc_L:.3f}")
    print(f"  準確率 - 高價值: {acc_H:.3f}")
    
    # Fig.4 的圖表是策略和準確率，類似 Fig.3 但策略中 Offload low-value 應很低或0
    fig, axs = plt.subplots(1, 2, figsize=(12, 5))
    fig.suptitle("Fig. 4 Simulation: Only High-Value Offloading Allowed (只允許卸載高價值)", fontsize=14)

    strategy_labels = ["Encode\nlow-value", "Encode\nhigh-value", "Offload\nlow-value", "Offload\nhigh-value"]
    strategy_values = [prop_encode_low, prop_encode_high, prop_offload_low_check, prop_offload_high]
    axs[0].bar(strategy_labels, strategy_values, color='cornflowerblue')
    axs[0].set_title("Strategy (策略選擇)")
    axs[0].set_ylabel("Proportion (比例)")
    axs[0].set_ylim(0, 1.05)
    plt.setp(axs[0].get_xticklabels(), rotation=15, ha="right")

    axs[1].bar(["Low-value", "High-value"], [acc_L, acc_H], color='cornflowerblue')
    axs[1].set_title("Accuracy (記憶準確率)")
    axs[1].set_ylabel("Mean Accuracy (平均準確率)")
    axs[1].set_ylim(0, 1.05)
    
    plt.tight_layout(rect=[0, 0, 1, 0.96])
    plt.show()
    
    # 論文中 Fig.4 的核心是比較低價值項目的準確率
    # "Comparing this with Fig. 1 (our Fig.2) shows that the model's accuracy for 
    # low-value items was improved, as found by Dupont et al."
    # 我們可以在調用此函數後，與 Fig.2 的 acc_L 進行比較。
    return results

# --- 新增: 模擬 Fig. 5: 意外記憶測試 ---
def simulate_fig5(base_params: ModelParameters):
    print("\n開始模擬 Fig. 5 (意外記憶測試)...")
    # Fig.5 包含兩種條件的意外測試準確率:
    # 1. 不允許卸載 (No offloading allowed)
    # 2. 只允許卸載高價值項目 (High-value offloading allowed)
    
    params_common = ModelParameters( # Fig.5 也是基於標準的3高3低項目設置
        n_high_items=3, n_low_items=3,
        value_high=base_params.value_high, value_low=base_params.value_low,
        cost_offloading=base_params.cost_offloading,
        accuracy_internal_1_item=base_params.accuracy_internal_1_item,
        accuracy_internal_2_items=base_params.accuracy_internal_2_items,
        accuracy_internal_3_items=base_params.accuracy_internal_3_items,
        accuracy_internal_6_items=base_params.accuracy_internal_6_items,
        accuracy_offloaded=base_params.accuracy_offloaded,
        n_episodes_per_strategy_eval=base_params.n_episodes_per_strategy_eval,
        n_model_runs=base_params.n_model_runs
    )

    # 條件1: 不允許卸載
    print("  Fig. 5 條件1: 不允許卸載 (意外測試)")
    strategies_cond1 = get_all_strategies(allow_offloading=False)
    results_cond1 = run_simulation_main_loop(params_common, strategies_cond1, accuracy_mode="surprise_test")
    acc_L_cond1 = results_cond1["mean_accuracy_L"]
    acc_H_cond1 = results_cond1["mean_accuracy_H"]
    print(f"    意外測試準確率 - 低價值 (不允許卸載): {acc_L_cond1:.3f}")
    print(f"    意外測試準確率 - 高價值 (不允許卸載): {acc_H_cond1:.3f}")

    # 條件2: 只允許卸載高價值項目
    print("  Fig. 5 條件2: 只允許卸載高價值 (意外測試)")
    strategies_cond2 = get_all_strategies(allow_offloading=True, offload_restriction="high_only")
    results_cond2 = run_simulation_main_loop(params_common, strategies_cond2, accuracy_mode="surprise_test")
    acc_L_cond2 = results_cond2["mean_accuracy_L"]
    acc_H_cond2 = results_cond2["mean_accuracy_H"]
    print(f"    意外測試準確率 - 低價值 (只允許卸載高價值): {acc_L_cond2:.3f}")
    print(f"    意外測試準確率 - 高價值 (只允許卸載高價值): {acc_H_cond2:.3f}")

    # 繪圖
    fig, axs = plt.subplots(1, 2, figsize=(10, 4), sharey=True)
    fig.suptitle("Fig. 5 Simulation: Surprise-Test Accuracy (意外記憶測試準確率)", fontsize=14)

    axs[0].bar(["Low-value", "High-value"], [acc_L_cond1, acc_H_cond1], color='cornflowerblue')
    axs[0].set_title("No Offloading Allowed\n(不允許卸載)")
    axs[0].set_ylabel("Surprise-Test Accuracy (意外測試準確率)")
    axs[0].set_ylim(0, 1.0)

    axs[1].bar(["Low-value", "High-value"], [acc_L_cond2, acc_H_cond2], color='lightcoral')
    axs[1].set_title("High-Value Offloading Allowed\n(只允許卸載高價值)")
    axs[1].set_ylim(0, 1.0)
    
    plt.tight_layout(rect=[0, 0, 1, 0.94])
    plt.show()
    return {"cond1": results_cond1, "cond2": results_cond2}

# --- 新增: 模擬 Fig. 6: 記憶負荷對卸載率的影響 ---
def simulate_fig6(base_params: ModelParameters):
    print("\n開始模擬 Fig. 6 (記憶負荷對卸載率的影響)...")
    # Fig.6 比較兩種記憶負荷:
    # 1. 每種價值1個項目 (1 high, 1 low)
    # 2. 每種價值3個項目 (3 high, 3 low) - 即標準情況

    # 條件1: 每種價值1個項目
    print("  Fig. 6 條件1: 每種價值1個項目")
    params_load1 = ModelParameters(
        n_high_items=1, n_low_items=1, # 改變項目數量
        value_high=base_params.value_high, value_low=base_params.value_low,
        cost_offloading=base_params.cost_offloading, # 使用基礎卸載成本
        accuracy_internal_1_item=base_params.accuracy_internal_1_item, # 1 item load
        accuracy_internal_2_items=base_params.accuracy_internal_2_items, # 2 items load (1H+1L)
        accuracy_internal_3_items=base_params.accuracy_internal_3_items, # 不適用
        accuracy_internal_6_items=base_params.accuracy_internal_6_items, # 不適用
        accuracy_offloaded=base_params.accuracy_offloaded,
        n_episodes_per_strategy_eval=base_params.n_episodes_per_strategy_eval,
        n_model_runs=base_params.n_model_runs
    )
    strategies_load1 = get_all_strategies(allow_offloading=True)
    results_load1 = run_simulation_main_loop(params_load1, strategies_load1, accuracy_mode="normal")
    offload_L_load1 = sum(prop for s, prop in results_load1["strategy_proportions"].items() if s[3])
    offload_H_load1 = sum(prop for s, prop in results_load1["strategy_proportions"].items() if s[2])
    print(f"    卸載率 (1H,1L) - 低價值: {offload_L_load1:.3f}, 高價值: {offload_H_load1:.3f}")

    # 條件2: 每種價值3個項目 (標準情況，可以重用 Fig.3 的結果或重新模擬)
    print("  Fig. 6 條件2: 每種價值3個項目 (標準負荷)")
    params_load3 = ModelParameters(
        n_high_items=3, n_low_items=3,
        value_high=base_params.value_high, value_low=base_params.value_low,
        cost_offloading=base_params.cost_offloading,
        accuracy_internal_1_item=base_params.accuracy_internal_1_item,
        accuracy_internal_2_items=base_params.accuracy_internal_2_items,
        accuracy_internal_3_items=base_params.accuracy_internal_3_items,
        accuracy_internal_6_items=base_params.accuracy_internal_6_items,
        accuracy_offloaded=base_params.accuracy_offloaded,
        n_episodes_per_strategy_eval=base_params.n_episodes_per_strategy_eval,
        n_model_runs=base_params.n_model_runs
    )
    strategies_load3 = get_all_strategies(allow_offloading=True)
    results_load3 = run_simulation_main_loop(params_load3, strategies_load3, accuracy_mode="normal")
    offload_L_load3 = sum(prop for s, prop in results_load3["strategy_proportions"].items() if s[3])
    offload_H_load3 = sum(prop for s, prop in results_load3["strategy_proportions"].items() if s[2])
    print(f"    卸載率 (3H,3L) - 低價值: {offload_L_load3:.3f}, 高價值: {offload_H_load3:.3f}")

    # 繪圖
    fig, axs = plt.subplots(1, 2, figsize=(10, 4), sharey=True)
    fig.suptitle("Fig. 6 Simulation: Effect of Memory Load on Offloading Rate (記憶負荷對卸載率影響)", fontsize=14)

    axs[0].bar(["Low-value", "High-value"], [offload_L_load1, offload_H_load1], color='cornflowerblue')
    axs[0].set_title("1 item at each value\n(每種價值1個項目)")
    axs[0].set_ylabel("Offloading rate (卸載率)")
    axs[0].set_ylim(0, 1.0)

    axs[1].bar(["Low-value", "High-value"], [offload_L_load3, offload_H_load3], color='lightcoral')
    axs[1].set_title("3 items at each value\n(每種價值3個項目)")
    axs[1].set_ylim(0, 1.0)
    
    plt.tight_layout(rect=[0, 0, 1, 0.94])
    plt.show()
    return {"load1": results_load1, "load3": results_load3}


# --- 模擬 Fig. 7: 卸載成本的影響 (與之前版本相同) ---
def simulate_fig7(base_params: ModelParameters):
    print("\n開始模擬 Fig. 7 (卸載成本的影響)...")
    offloading_costs = np.linspace(0.0, 2.0, 9) 
    results_over_costs: Dict[str, List[float]] = {
        "costs": list(offloading_costs), "memory_encoding_rate_low": [], "memory_encoding_rate_high": [],
        "offloading_rate_low": [], "offloading_rate_high": []
    }
    strategies_fig7 = get_all_strategies(allow_offloading=True)

    for cost_idx, cost in enumerate(offloading_costs):
        print(f"  模擬卸載成本 ({cost_idx+1}/{len(offloading_costs)}): {cost:.2f}")
        current_params = ModelParameters(
            n_high_items=3, n_low_items=3, # Fig.7 基於標準3H,3L項目
            value_high=base_params.value_high, value_low=base_params.value_low,
            cost_offloading=cost, 
            accuracy_internal_1_item=base_params.accuracy_internal_1_item,
            accuracy_internal_2_items=base_params.accuracy_internal_2_items,
            accuracy_internal_3_items=base_params.accuracy_internal_3_items,
            accuracy_internal_6_items=base_params.accuracy_internal_6_items,
            accuracy_offloaded=base_params.accuracy_offloaded,
            n_episodes_per_strategy_eval=base_params.n_episodes_per_strategy_eval,
            n_model_runs=base_params.n_model_runs # Fig.7 每個點的運行次數
        )
        sim_results = run_simulation_main_loop(current_params, strategies_fig7, accuracy_mode="normal")
        
        prop_encode_low = sum(prop for s, prop in sim_results["strategy_proportions"].items() if s[1])
        prop_encode_high = sum(prop for s, prop in sim_results["strategy_proportions"].items() if s[0])
        prop_offload_low = sum(prop for s, prop in sim_results["strategy_proportions"].items() if s[3])
        prop_offload_high = sum(prop for s, prop in sim_results["strategy_proportions"].items() if s[2])
        
        results_over_costs["memory_encoding_rate_low"].append(prop_encode_low)
        results_over_costs["memory_encoding_rate_high"].append(prop_encode_high)
        results_over_costs["offloading_rate_low"].append(prop_offload_low)
        results_over_costs["offloading_rate_high"].append(prop_offload_high)

    print(f"Fig. 7 結果: ")
    for i, cost_val in enumerate(results_over_costs["costs"]):
        print(f"  成本={cost_val:.2f}: EncL={results_over_costs['memory_encoding_rate_low'][i]:.3f}, EncH={results_over_costs['memory_encoding_rate_high'][i]:.3f}, OffL={results_over_costs['offloading_rate_low'][i]:.3f}, OffH={results_over_costs['offloading_rate_high'][i]:.3f}")

    fig, axs = plt.subplots(1, 2, figsize=(12, 5))
    fig.suptitle("Fig. 7 Simulation: Effect of Offloading Cost (卸載成本的影響)", fontsize=14)
    axs[0].plot(results_over_costs["costs"], results_over_costs["memory_encoding_rate_low"], 'o-', label="Low-value (低價值)", color='cornflowerblue')
    axs[0].plot(results_over_costs["costs"], results_over_costs["memory_encoding_rate_high"], 's-', label="High-value (高價值)", color='brown')
    axs[0].set_title("Memory encoding (內部記憶編碼率)")
    axs[0].set_xlabel("Cost of offloading (卸載成本)")
    axs[0].set_ylabel("Memory encoding rate (編碼率)")
    axs[0].legend()
    axs[0].grid(True, linestyle='--', alpha=0.7)
    axs[1].plot(results_over_costs["costs"], results_over_costs["offloading_rate_low"], 'o-', label="Low-value (低價值)", color='cornflowerblue')
    axs[1].plot(results_over_costs["costs"], results_over_costs["offloading_rate_high"], 's-', label="High-value (高價值)", color='brown')
    axs[1].set_title("Offloading (卸載率)")
    axs[1].set_xlabel("Cost of offloading (卸載成本)")
    axs[1].set_ylabel("Offloading rate (卸載率)")
    axs[1].legend()
    axs[1].grid(True, linestyle='--', alpha=0.7)
    plt.tight_layout(rect=[0, 0, 1, 0.96])
    plt.show()
    return results_over_costs

if __name__ == '__main__':
    # 設定基礎參數
    # 為了演示，n_model_runs 設為較小值。論文中為 1,000,000。
    # n_episodes_per_strategy_eval 論文中為 50。
    shared_base_params = ModelParameters(
        n_model_runs=5000, # 減少運行次數以便快速演示，實際應使用更大值
        n_episodes_per_strategy_eval=50
    )

    # 執行所有模擬
    print("--- 開始模擬 Fig. 2 ---")
    results_fig2 = simulate_fig2(shared_base_params)
    
    print("\n--- 開始模擬 Fig. 3 ---")
    results_fig3 = simulate_fig3(shared_base_params)
    
    print("\n--- 開始模擬 Fig. 4 ---")
    results_fig4 = simulate_fig4(shared_base_params)
    # 可以與 Fig.2 的 acc_L 比較:
    if results_fig2 and results_fig4:
        print(f"  比較低價值準確率: Fig.2 (不卸載)={results_fig2['mean_accuracy_L']:.3f} vs Fig.4 (僅卸載高價值)={results_fig4['mean_accuracy_L']:.3f}")

    print("\n--- 開始模擬 Fig. 5 ---")
    results_fig5 = simulate_fig5(shared_base_params)

    print("\n--- 開始模擬 Fig. 6 ---")
    results_fig6 = simulate_fig6(shared_base_params)
    
    print("\n--- 開始模擬 Fig. 7 ---")
    # Fig.7 每個成本點的運行次數可以單獨調整，如果 shared_base_params.n_model_runs 已經很小，就不需要再除了
    # 例如，如果 shared_base_params.n_model_runs = 1000，那麼每個點跑1000次。
    # 這裡的 simulate_fig7 內部沒有再除以5了，直接使用傳入的 n_model_runs
    results_fig7 = simulate_fig7(shared_base_params)

    print("\n所有模擬完成！")



