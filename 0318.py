import numpy as np

# w = np.array([0.7,0.7])
w = np.array([np.random.normal(loc = 0 , scale = 0.1),np.random.normal(loc = 0 , scale = 0.1)]) #定義常態分配中心位置和標偉差
print(w)
x = np.array([[0,0],[1,0],[0,1],[1,1]])  #and-gate，or-gate(a=10,wei = 0.7,0.7,bias = 0.5)
t = np.array([0,1,1,1])
m = np.dot(x , w)

a = 10
bias = 0.5
# y = 1/(1+np.exp(-a*(m-bias)))
# print(y)

def get_output(inp , wei):
    m = np.dot(inp , wei)
    return 1/(1+np.exp(-a*(m-bias)))

y = np.array([get_output(x[0,:] , w),get_output(x[1,:],w),get_output(x[2,:],w),get_output(x[3,:],w)])
print(y)


lr = 0.1 #學習常數
y = get_output(x[3,:],w)

def delta_learning(target,outp,inp):
    delta = target-outp
    derva = a*outp*(1-outp) #sigmoid專屬
    return lr*delta*derva*inp

  

# delta = (t[3]-y)
# print(delta)
# dw = delta_learning(t[3], y, x[3])
# w = w+dw
# print(w)
# y = get_output(x[3,:],w)

er = np,np.zeros(100)
##迴路
for r in range(100):
    for i in range(3):
        y = get_output(x[i,:],w)
        dw = delta_learning(t[i], y, x[i,:])
        w = w+dw
        er[r]=er[r]+0.5*(t[i]-y)**2#舊誤差加上新誤差，舊誤差一開始是0

y = np.array([get_output(x[0,:] , w),get_output(x[1,:],w),get_output(x[2,:],w),get_output(x[3,:],w)])
print(y)
print(w)
print(er)

