import numpy as np
import matplotlib.pyplot as plt
import tensorflow as tf  # 添加這行
from tensorflow.keras.datasets import mnist
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import Dense
from tensorflow.keras.utils import to_categorical
from tensorflow.keras.layers import LSTM
from tensorflow.keras.layers import Dense, Dropout, Activation, Flatten
from tensorflow.keras.layers import Conv2D, MaxPooling2D
from tensorflow.keras.layers import Embedding

# 添加股市预测相关代码
import yfinance as yf
from sklearn.preprocessing import MinMaxScaler

# 下载股票数据（以台积电为例子）
stock = yf.Ticker("0056.TW")
df = stock.history(period="5y")

# 准备数据
data = df['Close'].values.reshape(-1, 1)
scaler = MinMaxScaler()
data_scaled = scaler.fit_transform(data)

# 创建训练数据
def create_dataset(data, time_steps):
    X, y = [], []
    for i in range(len(data) - time_steps):
        X.append(data[i:(i + time_steps), 0])
        y.append(data[i + time_steps, 0])
    return np.array(X), np.array(y)

time_steps = 60  # 使用过去60天的数据来预测
X, y = create_dataset(data_scaled, time_steps)

# 分割训练集和测试集
train_size = int(len(X) * 0.8)
X_train, X_test = X[:train_size], X[train_size:]
y_train, y_test = y[:train_size], y[train_size:]

# 重塑数据为LSTM需要的格式
X_train = X_train.reshape((X_train.shape[0], X_train.shape[1], 1))
X_test = X_test.reshape((X_test.shape[0], X_test.shape[1], 1))

# 建立LSTM模型
model = Sequential([
    LSTM(50, return_sequences=True, input_shape=(time_steps, 1)),
    Dropout(0.2),  # 添加Dropout層防止過擬合
    LSTM(50, return_sequences=False),
    Dropout(0.1),  # 添加Dropout層防止過擬合
    Dense(50),
    Dense(1)
])

# 编译模型
model.compile(optimizer='adam', loss='mse')

# 训练模型
history = model.fit(X_train, y_train, 
                   epochs=50,  # 減少訓練次數
                   batch_size=32, 
                   validation_split=0.2,  # 增加驗證集比例
                   callbacks=[
                       tf.keras.callbacks.EarlyStopping(
                           patience=10,
                           restore_best_weights=True
                       )
                   ],
                   verbose=1)

# 预测
y_pred = model.predict(X_test)
print(y_pred)

# 反转缩放
y_test_inv = scaler.inverse_transform([y_test]).T
y_pred_inv = scaler.inverse_transform([y_pred.flatten()]).T

# 绘制结果
plt.figure(figsize=(15,7))
plt.plot(y_test_inv, label='true')
plt.plot(y_pred_inv, label='predict')
plt.xlabel('time')
plt.ylabel('price')
plt.legend()
plt.show()


# 预测未来一周价格
def predict_next_week(model, last_data, scaler):
    future_predictions = []
    current_batch = last_data[-time_steps:].reshape((1, time_steps, 1))
    
    for i in range(5):  # 预测5个交易日
        prediction = model.predict(current_batch)[0]
        future_predictions.append(prediction)
        # 更新预测批次
        current_batch = np.append(current_batch[:,1:,:], prediction.reshape(1,1,1), axis=1)
    
    future_pred_array = np.array(future_predictions).reshape(-1, 1)
    future_pred_inv = scaler.inverse_transform(future_pred_array)
    return future_pred_inv

# 获取最新数据进行预测
last_data = data_scaled[-time_steps:]
future_prices = predict_next_week(model, last_data, scaler)

# 获取最后实际收盘价
last_actual_price = df['Close'].iloc[-1]

# 显示预测结果
print(f"\n最后实际收盘价: {last_actual_price:.2f}")
print("\n未来五个交易日预测价格:")
for i, price in enumerate(future_prices, 1):
    print(f"第{i}天: {price[0]:.2f}")

# 绘制预测图
plt.figure(figsize=(15,7))
import pandas as pd
actual_dates = pd.date_range(start=df.index[-30], periods=30, freq='B')
future_dates = pd.date_range(start=df.index[-1], periods=6, freq='B')[1:]

plt.plot(actual_dates, df['Close'].iloc[-30:], label='历史价格', color='blue')
plt.plot(future_dates, future_prices, label='预测价格', color='red', linestyle='--')
plt.title('0056 ETF价格预测（未来一周）')
plt.xlabel('日期')
plt.ylabel('价格')
plt.legend()
plt.grid(True)
plt.show()

# 计算预测精度指标
mse = np.mean((y_test_inv - y_pred_inv) ** 2)
rmse = np.sqrt(mse)
print(f"\n模型精度指标（RMSE）: {rmse:.2f}")