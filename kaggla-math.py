import numpy as np # linear algebra
import pandas as pd # data processing, CSV file I/O (e.g. pd.read_csv)
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.model_selection import train_test_split
from sklearn.linear_model import LinearReg<PERSON>, <PERSON>, <PERSON>so
from sklearn.metrics import mean_squared_error, r2_score
from sklearn.preprocessing import StandardScaler
from sklearn.ensemble import RandomForestClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.metrics import accuracy_score, classification_report
from sklearn.svm import SVC
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
from tensorflow.keras.models import Model
from tensorflow.keras.layers import Input, Dense, Dropout
from tensorflow.keras.utils import to_categorical
from tensorflow.keras.optimizers import Adam
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
import os
for dirname, _, filenames in os.walk('/kaggle/input'):
    for filename in filenames:
        print(os.path.join(dirname, filename))
    
df = pd.read_csv("E:\KAGGLE Files\Math-Students.csv")
# df.info()
# df.head()

###檢查遺漏直
# df.isnull().sum()

# print(df[["G1","G2","G3"]].describe())

##視覺化
sns.set(style="whitegrid")
# plt.rcParams["figure.figsize"] = (10, 6)

plt.figure(figsize=(12, 6))
sns.histplot(df["G3"], kde=True, bins=20, color="skyblue")
plt.title("Distribution of Final Grades (G3)")
plt.xlabel("Final Grade (G3)")
plt.ylabel("Frequency")
# plt.show()

plt.figure(figsize=(12, 6))
sns.countplot(data = df,x = "school",palette = "Set2")
plt.title("Count of Students by School")
plt.xlabel("School")
plt.ylabel("Count")
# plt.show()

plt.figure(figsize=(12, 6))
sns.boxplot(data = df,x = "sex",y = "G3",palette = "pastel")
plt.title("Final Grades (G3) by Gender")
plt.xlabel("Gender")
plt.ylabel("Final Grade (G3)")
# plt.show()

numeric_df = df.select_dtypes(include=["float64", "int64"])
corr = numeric_df.corr()
fig, ax = plt.subplots(figsize=(14, 10))
sns.heatmap(corr,annot = True, fmt=".2f", cmap="coolwarm",square = True, ax=ax,linewidths=0.5)
ax.set_title("Correlation Matrix of Numerical Features", fontsize=16)
plt.tight_layout()
plt.show()

study_related = ["studytime", "failures", "absences", "G1", "G2", "G3"]
plt.figure(figsize=(12, 6))
sns.pairplot(df[study_related],kind = "reg",corner = True,plot_kws={"line_kws":{"color":"red"}})
plt.suptitle("Pairwise Relationships between Study-Related Features", y=1.02, fontsize=16)
plt.show()

X = df.drop(columns=['G3']) #drop the target variable
y = df['G3']
X = pd.get_dummies(X, drop_first=True) #one-hot encoding
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=537)

scaler = StandardScaler()
X_train_scaled = scaler.fit_transform(X_train) #fit and transform the training data
X_test_scaled = scaler.transform(X_test) #注意這裡只transform測試數據，不使用訓練的平均值和標準差

models = {
    "Linear Regression": LinearRegression(),
    "Ridge Regression": Ridge(alpha=1.0), #加入正則化項，將係數變小，防止過擬合
    "Lasso Regression": Lasso(alpha=0.1) #加入正則化項，將不重要係數變為0，進行特徵選擇
}

results = {}

for name, model in models.items():
    model.fit(X_train_scaled, y_train)
    y_pred = model.predict(X_test_scaled)
    mse = mean_squared_error(y_test, y_pred)
    r2 = r2_score(y_test, y_pred)
    results[name] = {
        "Mean Squared Error": mse,
        "R2 Score": r2
    }
    
for model_name, metrics in results.items():
    print(f"\n📘 {model_name}")
    for metric, value in metrics.items():
        print(f"{metric}: {value:.4f}")
        # https://www.kaggle.com/code/muhammedaliyilmazz/eda-in-ml-dl-regression-classification-nn