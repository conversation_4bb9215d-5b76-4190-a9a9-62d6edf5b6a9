import numpy as np
np.random.seed()
w1=np.random.normal(loc=0,scale = 0.1, size = (2,3))#兩個輸入三個輸出
w2=np.random.normal(loc=0,scale = 0.1, size = (3))#三個輸入一個輸出
a = 1 #控制陡峭程度
bias = 0.7 #控制曲線左右移動
def sigmoid(m):
    return 1/(1+np.exp(-a*(m-bias)))

#caculate output 向前傳播
def get_output(inp, wei):
    memP = np.dot(inp, wei)
    out =sigmoid(memP)
    return out
 
x = np.array([[0,0],[1,0],[0,1],[1,1]])
t = np.array([0,1,1,1]) #產生目標 #or gate
# o1 = get_output(x[1,:], w1) 


al=10 #學習速率
#learning
def delta_rule_output(target,outp):
    #修改delta變成oup-target-->這樣會差一個負號
    delta = outp-target
    delt= delta*a*outp*(1-outp)
    return delt

def delta_rule_hidden(delta_p, wei, outp):
    delta = np.dot(delta_p, wei)    
    delt = delta*outp*(1-outp)
    return delt

o1 = get_output(x[1,:],w1)
o2 = get_output(o1,w2)
del2=delta_rule_output(t[1], o2)
print(del2)

del1=delta_rule_hidden(del2,w2,o1)
# print(del1)

#caculate output before training
def get_all_output():
    ov = np.zeros(4)
    for i in range(4):
        o1 = get_output(x[i,:],w1)
        o2 = get_output(o1,w2)
        ov[i] =o2
    return ov

out = get_all_output()
print("學習前")
print(out)

for rpt in range(5000):
    for i in range(4):
        o1 = get_output(x[i,:],w1)
        o2=get_output(o1,w2)
        del2 = delta_rule_output(t[i], o2) #為了得到2*3矩陣，要用outer外積，反向傳播回去
        del1 = delta_rule_hidden(del2,w2,o1)
        w2=w2-al*del2*o1
        w1 = w1-al*np.outer(x[i,:],del1)


out = get_all_output()
print("學習後")
print(out)


