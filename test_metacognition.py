#!/usr/bin/env python3
"""
測試後設認知模擬的修正
"""

try:
    import sys
    import os
    print("Imports successful")

    # 添加路徑以便導入主模組
    sys.path.append(os.path.dirname(os.path.abspath(__file__)))
    print("Path added")

    # 導入主模組的函數和類別
    exec(open('(後設認知)神經網路期末.py').read())
    print("Main module loaded successfully")
    
except Exception as e:
    print(f"Error during imports: {e}")
    import traceback
    traceback.print_exc()
    exit(1)

def test_metacognitive_decisions():
    """測試後設認知決策是否正常工作"""
    print("=== 測試後設認知決策機制 ===")
    
    # 創建測試參數
    test_params = ModelParameters(
        n_model_runs=1,  # 只運行一次
        n_episodes_per_strategy_eval=5,  # 少量episode進行測試
        metacognitive_threshold=0.85,  # 使用修正後的閾值
        n_high_items=2,
        n_low_items=2
    )
    
    # 測試策略: 允許所有類型的存儲和卸載
    test_strategy = (True, True, True, True)  # (store_H, store_L, offload_H, offload_L)
    
    print(f"測試參數:")
    print(f"  後設認知閾值: {test_params.metacognitive_threshold}")
    print(f"  高價值項目數: {test_params.n_high_items}")
    print(f"  低價值項目數: {test_params.n_low_items}")
    print(f"  測試策略: {test_strategy}")
    
    # 運行幾次試驗來觀察後設認知決策
    print(f"\n運行測試試驗:")
    total_H_decisions = 0
    total_L_decisions = 0
    
    for i in range(5):
        print(f"\n--- 試驗 {i+1} ---")
        result = simulate_trial(test_params, test_strategy)
        reward, hits_H, hits_L, surprise_H, surprise_L, offloaded_H, offloaded_L = result
        
        print(f"結果: 獎勵={reward:.2f}, 卸載H={offloaded_H}, 卸載L={offloaded_L}")
    
    print(f"\n=== 測試完成 ===")

def test_strategy_evaluation():
    """測試策略主觀評估"""
    print("\n=== 測試策略主觀評估 ===")
    
    test_params = ModelParameters(
        metacognitive_threshold=0.85,
        metacognitive_bias=0.1,  # 適中的偏差
        confidence_noise=0.05    # 適中的噪音
    )
    
    # 測試不同策略的主觀評估
    strategies = [
        (True, True, False, False),  # 只內部存儲
        (True, True, True, False),   # 允許高價值卸載
        (True, True, True, True),    # 允許所有卸載
    ]
    
    print(f"策略主觀評估比較 (閾值={test_params.metacognitive_threshold}):")
    for i, strategy in enumerate(strategies):
        subjective_reward = evaluate_strategy_subjective(strategy, test_params)
        print(f"  策略 {i+1} {strategy}: 主觀預期獎勵 = {subjective_reward:.3f}")

if __name__ == "__main__":
    test_metacognitive_decisions()
    test_strategy_evaluation()
    
    print(f"\n如果看到後設認知決策的調試輸出，表示修正成功！")
    print(f"如果沒有看到，可能需要進一步調整參數。")
