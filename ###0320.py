###0320
import numpy as np
np.random.seed()
w = np.random.normal(loc=0, scale=0.1, size=(2))

a = 20
bias = 0.7
def sigmoid(m):
    return 1/(1+np.exp(-a*(m-bias)))

#caculate output
def get_output(inp, wei):
    memP = np.dot(inp, wei)
    out =sigmoid(memP)
    return out
 
x = np.array([[0,0],[1,0],[0,1],[1,1]])
t = np.array([1,1,0,0]) #產生目標 #and gate,如果要改成or gate就改成[0,1,1,1]
o = get_output(x[1,:], w)

#learning
def delta_rule(inp,target,outp):
    #修改delta變成oup-target-->這樣會差一個負號
    delta = outp-target
    al=0.5 #學習速率
    # dw = -al*np.outer(delta*outp*(1-outp),inp)
    dw = -al*delta*a*outp*(1-outp)*inp
    return dw

# dw = delta_rule(x[1,:], t[1], o)
# print(dw)

#caculate output before training
def get_all_output():
    o = get_output(x[0,:],w)
    ov = np.zeros(4)
    for i in range(4):
        ov[i] = np.array([get_output(x[i,:],w)])#將兩個向量合併
    return ov

out = get_all_output()
print(out)

# def get_all_output():
#     outputs = []  # 创建一个空列表来存储所有输出
#     for i in range(4):  # 遍历所有4个输入
#         outputs.append(get_output(x[i,:], w))  # 将每个输出添加到列表中
#     return np.array(outputs)  # 最后转换为numpy数组

# out = get_all_output()
# print(out)
#training
for rpt in range(10000):
    for i in range(4):
        op = get_output(x[i,:],w)
        dw = delta_rule(x[i,:],t[i],op)
        w=w+dw

#test the result
out = get_all_output()
print(out)

