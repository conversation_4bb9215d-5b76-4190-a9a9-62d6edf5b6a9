import numpy as np
from scipy.stats import norm
import matplotlib.pyplot as plt

wei_0 = np.zeros((52,52))
x = range(0,52)
CsA = norm.pdf(x,3,7)*10
    # CsA[51] = 0 # 嘗試移除或註解此行
CsB = norm.pdf(x,26,7)*10
    # CsB[51] = 0 # 嘗試移除或註解此行
CsX = norm.pdf(x,49,7)*10
    # CsX[51] = 0 # 嘗試移除或註解此行
Us = np.zeros(52)

alpha = 0.01
beta = 0.1
epsilon = 0.01
timeframe = 50
is_training = True
is_testing = False

def model_core(outs,wei):
    outi = np.zeros(52)
    outp = np.zeros(52)
    previ = outi
    prevp = outp
    vi = []
    vp = []  
    
    for t in range(0,timeframe):
        deli = beta*(outs-outp-outi)
        delp = beta*(np.inner(outi,wei)-outp)
        outi = outi+deli
        outp = outp+delp
        if is_training:
           
            del_wei = alpha*np.outer((outi-outp), outi)
            wei = wei+del_wei
            wei = wei*(np.ones((52,52))-np.identity(52))
        di=sum(abs(outi-previ))  
        vi.append(di)
        dp = sum(abs(outp-prevp))
        vp.append(dp) 
        previ = outi
        prevp = outp
    
    if is_testing:
        return (vi,vp,wei)
    else:
        return (outi,outp,wei)

cs = CsA+CsB+Us
o_i = np.zeros(52)
o_p = np.zeros(52)
is_testing = True
oi,op,wei = model_core(cs,wei_0)
plt.plot(oi,color='b')
plt.plot(op,color='r')
plt.show()

wei = wei_0
is_testing = False
veca = []
vecx = []
vecb = []
for t in range(0,8):
    cs = CsA+CsX+Us
    is_training = True
    oi,op,wei = model_core(cs,wei)
    cs = CsA+CsB+Us
    oi,op,wei = model_core(cs,wei)
    is_training = False
    oi,opa,wei = model_core(CsA,wei)
    oi,opx,wei = model_core(CsX,wei)
    oi,opb,bwei = model_core(CsB,wei) 
    veca.append(opa[51])
    vecx.append(opx[51])
    vecb.append(opb[51])


for t in range(0,4):
    cs = CsB+Us
    is_training = True
    oi,op,wei = model_core(cs,wei)
    cs = CsA+CsB+Us
    oi,op,wei = model_core(cs,wei)
    is_training = False
    oi,opa,wei = model_core(CsA,wei)
    oi,opx,wei = model_core(CsX,wei)
    oi,opb,bwei = model_core(CsB,wei) 
    veca.append(opa[51])
    vecx.append(opx[51])
    vecb.append(opb[51])

plt.plot(veca,color='b')
plt.plot(vecx,color='r')
plt.plot(vecb,color='g')
plt.show()





    
    
    
    
    
