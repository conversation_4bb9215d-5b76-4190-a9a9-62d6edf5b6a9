import numpy as np
from scipy import stats
from typing import Tuple, List, Dict, Any
from dataclasses import dataclass, replace
import itertools
from collections import defaultdict
import random
import matplotlib.pyplot as plt

@dataclass
class ModelParameters:
    # 基礎任務參數
    n_high_items: int = 3
    n_low_items: int = 3
    value_high: float = 8.0
    value_low: float = 2.0
    cost_offloading: float = 1.0

    # 記憶準確率參數
    accuracy_internal_1_item: float = 0.95    # 內部記憶1個項目時的準確率
    accuracy_internal_2_items: float = 0.925  # 內部記憶2個項目時的準確率
    accuracy_internal_3_items: float = 0.90   # 內部記憶3個項目時的準確率 (標準情況: 只記一種類型)
    accuracy_internal_6_items: float = 0.75   # 內部記憶6個項目時的準確率 (標準情況: 記兩種類型)
    accuracy_offloaded: float = 0.98          # 外部卸載項目的準確率

    # 模擬控制參數
    n_episodes_per_strategy_eval: int = 20 # 評估每個策略時模擬的試驗次數 (用於計算主觀預期獎勵)
    n_model_runs: int = 100 # 獨立的模型運行次數 (最終結果會跨這些運行平均)

    # --- SDT 後設認知參數 ---
    # Type-1 任務表現
    d_prime: float = 1.5
    type1_criterion: float = 0.0

    # Type-2 信心判斷
    metacognitive_efficiency: float = 0.8  # 後設認知效率 (0-1)，衡量信心判斷與實際表現的相關程度
    confidence_criterion_bias: float = 0.0  # 信心判斷的決策標準偏移 (Meta-c)，正值表示過度自信，負值表示缺乏自信
    confidence_noise_std: float = 0.2      # 信心判斷的內部噪音標準差，影響信心判斷的穩定性和精確度

    # 卸載決策參數
    offload_confidence_threshold_high: float = 0.6
    offload_confidence_threshold_low: float = 0.4

    # 價值敏感性
    value_sensitive_metacognition: bool = True
    high_value_confidence_boost: float = 0.05

# 策略定義
Strategy = Tuple[bool, bool, bool, bool]
StrategyFig8 = Tuple[bool, bool]

def get_all_strategies(allow_offloading=True, offload_restriction="none") -> List[Strategy]:
    """
    生成所有可能的策略組合。
    allow_offloading: 是否允許任何形式的卸載。
    offload_restriction: "none" (無限制), "high_only" (只允許卸載高價值)。
    """
    options_sh = [True, False] # 是否內部記憶高價值
    options_sl = [True, False] # 是否內部記憶低價值

    if not allow_offloading:
        options_oh = [False] # 不允許卸載高價值
        options_ol = [False] # 不允許卸載低價值
    else:
        if offload_restriction == "high_only":
            options_oh = [True, False] # 可以選擇卸載高價值或不卸載
            options_ol = [False] # 低價值不允許卸載
        else: # "none" restriction (或未指定 restriction)
            options_oh = [True, False] # 可以選擇卸載高價值或不卸載
            options_ol = [True, False] # 可以選擇卸載低價值或不卸載
    
    # 使用 itertools.product 產生所有組合
    return list(itertools.product(options_sh, options_sl, options_oh, options_ol))

def get_strategies_fig8() -> List[StrategyFig8]:
    """
    Fig 8 專用的策略組合 (針對單一項目): (是否編碼, 是否允許卸載)。
    """
    return [(True, True), (True, False), (False, True), (False, False)]

def get_internal_memory_accuracy(strategy_encoding: Tuple[bool, bool], params: ModelParameters) -> float:
    """
    根據內部記憶策略 (編碼高價值, 編碼低價值) 計算內部記憶的理論準確率。
    這反映了內部記憶受負荷影響的假設 (記憶項目越多，準確率越低)。
    """
    items_stored = 0
    if strategy_encoding[0]:  # 如果選擇編碼高價值
        items_stored += params.n_high_items
    if strategy_encoding[1]:  # 如果選擇編碼低價值
        items_stored += params.n_low_items

    if items_stored == 0:
        return 0.0 # 不記憶任何東西，準確率為 0
    elif items_stored == 1:
        return params.accuracy_internal_1_item
    elif items_stored == 2:
        return params.accuracy_internal_2_items
    elif items_stored == 3:
        return params.accuracy_internal_3_items
    # 如果總項目數等於高價值+低價值總數，使用針對總負荷的準確率
    elif items_stored == (params.n_high_items + params.n_low_items):
        return params.accuracy_internal_6_items
    else:
         # 處理其他可能的項目數 (如果 n_high_items 或 n_low_items 被修改)
         # 這裡簡單回退到最高負荷的準確率，可能需要根據實際模型調整
        print(f"警告: 未預期的內部記憶項目數: {items_stored}。使用 accuracy_internal_6_items。")
        return params.accuracy_internal_6_items

# --- SDT 後設認知核心函數 ---
def generate_evidence_signal(d_prime: float, is_correct: bool, criterion: float = 0.0) -> float:
    """
    基於 SDT 生成內部證據信號。
    如果正確，從 N(d'/2 + criterion, 1) 中取樣；如果錯誤，從 N(-d'/2 + criterion, 1) 中取樣。
    """
    # 平均值根據正確性 (信號或噪音分佈) 和 Type 1 判斷準則確定
    mean = (d_prime / 2 if is_correct else -d_prime / 2) + criterion
    # 標準差假設為 1
    std_dev = 1
    # 從常態分佈中取樣生成證據信號
    evidence = np.random.normal(mean, std_dev)
    return evidence

def compute_confidence_from_evidence(evidence: float, params: ModelParameters, 
                                   is_high_value: bool = False) -> float:
    """
    基於 SDT 從證據信號計算信心水平 (Type 2 判斷)。
    信心信號基於證據信號的絕對值 (強度)，並受到後設認知效率、噪音和偏差的影響。
    """
    # Type-2 信心判斷基於證據強度的絕對值 (|x|)
    confidence_signal = abs(evidence) * params.metacognitive_efficiency
    
    # 添加信心判斷噪音
    confidence_signal += np.random.normal(0, params.confidence_noise_std)
    
    # 應用後設認知偏差 (Meta-c 效應)
    # 這個偏差會作為信心判斷的「基準線」或「閾值」的偏移量
    confidence_criterion = params.confidence_criterion_bias
    
    # 價值敏感的信心調整：對於高價值項目，降低其信心判斷閾值 (等同於信心信號加上一個正向偏移)
    # 這裡實現為降低信心判斷所需的「基準線」，使高價值項目更容易達到高信心或滿足卸載條件
    if is_high_value and params.value_sensitive_metacognition:
        confidence_criterion -= params.high_value_confidence_boost # 降低所需的信心信號，等同於提升信心感
    
    # 轉換為 0-1 範圍的信心機率，使用標準常態分佈的累積分佈函數 (CDF)
    # 這裡的 (confidence_signal - confidence_criterion) 可以視為相對於信心閾值的證據信號強度
    confidence = stats.norm.cdf(confidence_signal - confidence_criterion)
    
    # 確保信心值在 0 到 1 之間
    return np.clip(confidence, 0, 1)

def make_offload_decision_sdt(confidence: float, item_value: float, 
                             params: ModelParameters) -> bool:
    """
    基於 SDT 計算出的信心水平和預期效用做出卸載決策。
    決策規則: 信心低於特定閾值 AND 卸載的預期效用更高時才卸載。
    """
    # 確定基線信心閾值 (根據項目價值)
    if item_value == params.value_high:
        base_threshold = params.offload_confidence_threshold_high
    else:
        base_threshold = params.offload_confidence_threshold_low
    
    # 計算預期效用 (Expected Utility)
    # 如果不卸載，預期效用 = 信心 * 項目價值 (假設信心直接反映了回憶成功的機率和相應獎勵)
    utility_no_offload = confidence * item_value
    # 如果卸載，預期效用 = 外部記憶準確率 * 項目價值 - 卸載成本
    utility_offload = params.accuracy_offloaded * item_value - params.cost_offloading
    
    # 卸載決策：只有當當前信心不足 (低於閾值) 且外部卸載更有利時才卸載
    should_offload = (confidence < base_threshold) and (utility_offload > utility_no_offload)
    
    return should_offload

def simulate_trial_sdt(params: ModelParameters, strategy: Strategy) -> Tuple[float, int, int, int, int, int, int]:
    """
    基於 SDT 後設認知模型模擬單次試驗。
    返回: (總獎勵, 高價值正常命中數, 低價值正常命中數, 高價值意外命中數, 低價值意外命中數, 實際卸載高價值數, 實際卸載低價值數)
    """
    # 解包策略: (是否內部記憶高, 是否內部記憶低, 是否允許卸載高, 是否允許卸載低)
    store_H_internal, store_L_internal, offload_H_allowed, offload_L_allowed = strategy
    
    total_reward = 0.0
    high_value_hits_normal = 0 # 正常測試 (內部或外部成功)
    low_value_hits_normal = 0
    high_value_hits_surprise = 0 # 意外測試 (僅內部成功)
    low_value_hits_surprise = 0
    actual_offloaded_H = 0 # 實際決定卸載的高價值項目數
    actual_offloaded_L = 0 # 實際決定卸載的低價值項目數

    # 獲取內部記憶的理論準確率 (基於策略中的內部記憶選擇)
    p_internal = get_internal_memory_accuracy((store_H_internal, store_L_internal), params)

    # --- 處理高價值項目 ---
    for _ in range(params.n_high_items):
        # 判斷該項目是否在內部記憶中被「正確」記住 (基於理論準確率)
        is_actually_correct_internal = (np.random.rand() < p_internal) if store_H_internal else False
        
        # 生成基於 Type 1 表現的證據信號 (即使不內部記憶，也有潛在的 Type 1 表現信號用於信心判斷)
        # 注意這裡的 is_actually_correct_internal 用於生成 Type 1 信號的來源分佈 (正確 vs 錯誤)
        evidence = generate_evidence_signal(params.d_prime, is_actually_correct_internal, params.type1_criterion)
        
        # 計算基於證據信號的信心水平
        confidence = compute_confidence_from_evidence(evidence, params, is_high_value=True)
        
        # 做出卸載決策 (只有在策略允許卸載該類型項目時才進行決策)
        decided_to_offload = False
        if offload_H_allowed:
            decided_to_offload = make_offload_decision_sdt(confidence, params.value_high, params)
        
        # 模擬回憶結果 (正常測試和意外測試)
        item_recalled_normal = False
        item_recalled_surprise = False
        
        if decided_to_offload:
            actual_offloaded_H += 1
            # 如果決定卸載，回憶成功取決於外部記憶準確率
            if np.random.rand() < params.accuracy_offloaded:
                item_recalled_normal = True
        else:
            # 如果決定不卸載，回憶成功取決於內部記憶的實際表現
            if store_H_internal and is_actually_correct_internal:
                item_recalled_normal = True
                item_recalled_surprise = True # 內部記憶成功也計入意外測試
        
        # 累計獎勵和命中數
        if item_recalled_normal:
            high_value_hits_normal += 1
            total_reward += params.value_high
        
        if item_recalled_surprise:
            high_value_hits_surprise += 1

    # --- 處理低價值項目 (邏輯與高價值項目相同) ---
    for _ in range(params.n_low_items):
        is_actually_correct_internal = (np.random.rand() < p_internal) if store_L_internal else False
        
        evidence = generate_evidence_signal(params.d_prime, is_actually_correct_internal, params.type1_criterion)
        confidence = compute_confidence_from_evidence(evidence, params, is_high_value=False) # 低價值項目
        
        decided_to_offload = False
        if offload_L_allowed:
            decided_to_offload = make_offload_decision_sdt(confidence, params.value_low, params) # 低價值項目
        
        item_recalled_normal = False
        item_recalled_surprise = False
        
        if decided_to_offload:
            actual_offloaded_L += 1
            if np.random.rand() < params.accuracy_offloaded:
                item_recalled_normal = True
        else:
            if store_L_internal and is_actually_correct_internal:
                item_recalled_normal = True
                item_recalled_surprise = True
        
        if item_recalled_normal:
            low_value_hits_normal += 1
            total_reward += params.value_low
        
        if item_recalled_surprise:
            low_value_hits_surprise += 1

    # 計算總卸載成本
    total_reward -= actual_offloaded_H * params.cost_offloading # 注意這裡實際卸載數量直接來自策略選擇
    total_reward -= actual_offloaded_L * params.cost_offloading

    return (total_reward, high_value_hits_normal, low_value_hits_normal, 
            high_value_hits_surprise, low_value_hits_surprise, actual_offloaded_H, actual_offloaded_L)

def simulate_trial_original(params: ModelParameters, strategy: Strategy) -> Tuple[float, int, int, int, int, int, int]:
    """
    原始模型模擬單次試驗 (不含 SDT 後設認知)。
    返回: (總獎勵, 高價值正常命中數, 低價值正常命中數, 高價值意外命中數, 低價值意外命中數, 實際卸載高價值數, 實際卸載低價值數)
    """
    # 解包策略
    store_H_internal, store_L_internal, offload_H_allowed, offload_L_allowed = strategy
    
    total_reward = 0.0
    high_value_hits_normal = 0
    low_value_hits_normal = 0
    high_value_hits_surprise = 0
    low_value_hits_surprise = 0
    actual_offloaded_H = 0 # 在原始模型中，如果允許卸載且選擇了相應策略，則視為全部卸載
    actual_offloaded_L = 0

    # 獲取內部記憶的理論準確率
    p_internal = get_internal_memory_accuracy((store_H_internal, store_L_internal), params)

    # --- 處理高價值項目 ---
    for _ in range(params.n_high_items):
        item_recalled_normal = False
        item_recalled_surprise = False

        # 在原始模型中，如果同時選擇了內部記憶和允許卸載，則成功率是兩者的組合
        if store_H_internal and offload_H_allowed:
            actual_offloaded_H += 1 # 選擇允許卸載高價值，則視為全部卸載高價值項目
            # 回憶成功機率是內部記憶成功 OR 外部卸載成功
            combined_success_prob = 1.0 - (1.0 - p_internal) * (1.0 - params.accuracy_offloaded)
            if np.random.rand() < combined_success_prob:
                item_recalled_normal = True
            # 意外測試只考慮內部記憶成功
            if np.random.rand() < p_internal:
                item_recalled_surprise = True
        elif store_H_internal: # 只選擇內部記憶
            if np.random.rand() < p_internal:
                item_recalled_normal = True
                item_recalled_surprise = True
        elif offload_H_allowed: # 只選擇外部卸載
            actual_offloaded_H += 1 # 選擇允許卸載高價值，則視為全部卸載高價值項目
            if np.random.rand() < params.accuracy_offloaded:
                item_recalled_normal = True
        # 如果兩者都沒選，回憶成功率為 0，命中數不會增加

        if item_recalled_normal:
            high_value_hits_normal += 1
            total_reward += params.value_high
        if item_recalled_surprise:
            high_value_hits_surprise += 1

    # --- 處理低價值項目 (邏輯與高價值項目相同) ---
    for _ in range(params.n_low_items):
        item_recalled_normal = False
        item_recalled_surprise = False

        if store_L_internal and offload_L_allowed:
            actual_offloaded_L += 1 # 選擇允許卸載低價值，則視為全部卸載低價值項目
            combined_success_prob = 1.0 - (1.0 - p_internal) * (1.0 - params.accuracy_offloaded)
            if np.random.rand() < combined_success_prob:
                item_recalled_normal = True
            if np.random.rand() < p_internal:
                item_recalled_surprise = True
        elif store_L_internal: # 只選擇內部記憶
            if np.random.rand() < p_internal:
                item_recalled_normal = True
                item_recalled_surprise = True
        elif offload_L_allowed: # 只選擇外部卸載
            actual_offloaded_L += 1 # 選擇允許卸載低價值，則視為全部卸載低價值項目
            if np.random.rand() < params.accuracy_offloaded:
                item_recalled_normal = True
        # 如果兩者都沒選，回憶成功率為 0

        if item_recalled_normal:
            low_value_hits_normal += 1
            total_reward += params.value_low
        if item_recalled_surprise:
            low_value_hits_surprise += 1

    # 計算總卸載成本
    total_reward -= actual_offloaded_H * params.cost_offloading # 注意這裡實際卸載數量直接來自策略選擇
    total_reward -= actual_offloaded_L * params.cost_offloading

    return (total_reward, high_value_hits_normal, low_value_hits_normal, 
            high_value_hits_surprise, low_value_hits_surprise, actual_offloaded_H, actual_offloaded_L)

def run_simulation(params: ModelParameters, strategies: List[Strategy], use_sdt: bool = False) -> Dict[str, Any]:
    """
    運行蒙地卡羅模擬，評估不同策略的表現，並找出在多次運行中平均收益最高的策略及其比例。
    計算並返回最佳策略下的平均經驗準確率和卸載率。
    params: 模型參數。
    strategies: 要評估的策略列表。
    use_sdt: 是否使用 SDT 後設認知模型 (True) 或原始模型 (False)。
    """
    method_name = "SDT 後設認知" if use_sdt else "原始模型"
    print(f"評估運行模擬 ({method_name}, {params.n_model_runs} runs, {params.n_episodes_per_strategy_eval} episodes per run)...")

    # 選擇要使用的試驗模擬函數
    simulate_func = simulate_trial_sdt if use_sdt else simulate_trial_original

    # 存儲所有 run 中，每個策略被選為最佳策略的次數
    overall_best_strategy_counts: Dict[Strategy, int] = {s: 0 for s in strategies}

    # 累計所有 run 中，最佳策略的經驗命中數和卸載數，用於計算最終平均經驗準確率和卸載率
    overall_cumulative_hits_H_normal = 0
    overall_cumulative_hits_L_normal = 0
    overall_cumulative_hits_H_surprise = 0
    overall_cumulative_hits_L_surprise = 0
    overall_cumulative_offloaded_H = 0 # 新增累計實際卸載數量
    overall_cumulative_offloaded_L = 0 # 新增累計實際卸載數量

    # 累計成功完成模擬的 runs 數量 (用於計算平均值時避免除以零)
    successful_runs_count = 0

    # 遍歷所有的 runs
    for i_run in range(params.n_model_runs):
        # 打印進度
        if (i_run + 1) % max(1, params.n_model_runs // 100) == 0: # 調整打印頻率
            print(f"  運行中... {((i_run + 1) / params.n_model_runs) * 100:.0f}%")

        # 初始化本次 run 中每個策略的總獎勵和樣本計數
        total_reward_per_strategy_this_run: Dict[Strategy, float] = defaultdict(float)
        sample_count_per_strategy_this_run: Dict[Strategy, int] = defaultdict(int)

        # 在本次 run 中進行多次 episodes (試驗)
        for i_episode in range(params.n_episodes_per_strategy_eval):
            # 隨機選擇一個策略
            chosen_strategy = random.choice(strategies)
            chosen_strategy_tuple = tuple(chosen_strategy) # 確保是 tuple 作為字典鍵

            # 模擬一次試驗，注意這裡調用的是 simulate_trial_sdt 或 simulate_trial_original
            reward, hits_H_norm, hits_L_norm, hits_H_surp, hits_L_surp, off_H, off_L = simulate_func(params, chosen_strategy_tuple)

            # 將結果累加到本次 run 的統計中
            total_reward_per_strategy_this_run[chosen_strategy_tuple] += reward
            sample_count_per_strategy_this_run[chosen_strategy_tuple] += 1

        # 在本次 run 結束後，計算每個策略的平均獎勵
        mean_rewards_this_run: Dict[Strategy, float] = {}
        max_avg_reward_this_run = -float('inf')
        strategies_sampled_this_run = [s for s in strategies if sample_count_per_strategy_this_run[s] > 0]

        if not strategies_sampled_this_run:
             # 如果本次 run 中沒有任何策略被 sampled，則沒有最佳策略，跳過本次 run 的統計
             # print(f"警告: Fig 8 Run {i_run+1} 沒有策略被 sampled。跳過。")
             continue

        for strategy in strategies_sampled_this_run:
             if sample_count_per_strategy_this_run[strategy] > 0:
                mean_reward = total_reward_per_strategy_this_run[strategy] / sample_count_per_strategy_this_run[strategy]
                mean_rewards_this_run[strategy] = mean_reward
                if mean_reward > max_avg_reward_this_run:
                    max_avg_reward_this_run = mean_reward

        # 找出本次 run 中平均獎勵最高的策略
        tolerance = 1e-9
        best_strategies_this_run = [
            s for s, avg_r in mean_rewards_this_run.items() if abs(avg_r - max_avg_reward_this_run) < tolerance
        ]
    
        # 如果有多個並列最佳，隨機選擇一個作為本次 run 的最佳策略
        best_strategy_for_this_run = random.choice(best_strategies_this_run)

        # 累加本次 run 的最佳策略的選擇次數
        overall_best_strategy_counts[best_strategy_for_this_run] += 1

        # 累計本次 run 的最佳策略的經驗命中數和卸載數，用於計算最終的平均經驗指標
        # 總的機會數 = 最佳策略樣本數 * 項目數
        samples_best_strategy = sample_count_per_strategy_this_run[best_strategy_for_this_run]
        n_high = params.n_high_items
        n_low = params.n_low_items

        if samples_best_strategy > 0:  # 確保分母不為零
             # 注意: 這裡累加的是總命中數/總機會數 (即平均經驗命中率)，而不是直接命中數
             overall_cumulative_hits_H_normal += hits_H_norm / (samples_best_strategy * n_high) if n_high > 0 else 0.0
             overall_cumulative_hits_L_normal += hits_L_norm / (samples_best_strategy * n_low) if n_low > 0 else 0.0
             overall_cumulative_hits_H_surprise += hits_H_surp / (samples_best_strategy * n_high) if n_high > 0 else 0.0
             overall_cumulative_hits_L_surprise += hits_L_surp / (samples_best_strategy * n_low) if n_low > 0 else 0.0
             # 累計平均經驗卸載率 (實際卸載數 / 總機會數)
             overall_cumulative_offloaded_H += off_H / (samples_best_strategy * n_high) if n_high > 0 else 0.0
             overall_cumulative_offloaded_L += off_L / (samples_best_strategy * n_low) if n_low > 0 else 0.0

             successful_runs_count += 1

    # 在所有 runs 結束後，計算最終的平均結果
    # 策略比例 = 該策略被選為最佳的總次數 / 總運行次數
    total_runs_considered = successful_runs_count

    if total_runs_considered == 0:
         print(f"警告: 沒有成功完成的 runs。無法計算平均結果。")
         return {
             "strategy_proportions": {s: 0.0 for s in strategies},
             "mean_accuracy_H": 0.0, "mean_accuracy_L": 0.0,
             "mean_accuracy_H_surprise": 0.0, "mean_accuracy_L_surprise": 0.0,
             "mean_offload_H": 0.0, "mean_offload_L": 0.0,
             "overall_best_strategy_counts": overall_best_strategy_counts
         }

    strategy_proportions: Dict[Strategy, float] = {
        s: overall_best_strategy_counts[s] / total_runs_considered for s in strategies
    }

    # 計算最終平均經驗命中率和卸載率 (這是所有成功 run 的最佳策略的平均經驗指標的平均)
    mean_empirical_accuracy_H_normal = overall_cumulative_hits_H_normal / total_runs_considered
    mean_empirical_accuracy_L_normal = overall_cumulative_hits_L_normal / total_runs_considered
    mean_empirical_accuracy_H_surprise = overall_cumulative_hits_H_surprise / total_runs_considered
    mean_empirical_accuracy_L_surprise = overall_cumulative_hits_L_surprise / total_runs_considered
    mean_empirical_offload_H = overall_cumulative_offloaded_H / total_runs_considered # 最終平均經驗卸載率
    mean_empirical_offload_L = overall_cumulative_offloaded_L / total_runs_considered # 最終平均經驗卸載率

    # 打印每個策略被選為最佳的次數和比例
    print(f"\n最終策略選擇結果 ({method_name}):")
    sorted_overall_counts = sorted(overall_best_strategy_counts.items(), key=lambda item: item[1], reverse=True)
    for s, count in sorted_overall_counts:
         print(f"  策略 {s}: 被選為最佳次數 {count}, 比例 {strategy_proportions[s]:.3f}")

    # 打印最終平均經驗指標
    print(f"  平均經驗正常準確率: 高價值={mean_empirical_accuracy_H_normal:.3f}, 低價值={mean_empirical_accuracy_L_normal:.3f}")
    print(f"  平均經驗意外準確率: 高價值={mean_empirical_accuracy_H_surprise:.3f}, 低價值={mean_empirical_accuracy_L_surprise:.3f}")
    print(f"  平均經驗卸載率: 高價值={mean_empirical_offload_H:.3f}, 低價值={mean_empirical_offload_L:.3f}")

    return {
        "strategy_proportions": strategy_proportions, # 每個策略在所有 run 中被選為最佳的比例
        "mean_accuracy_H": mean_empirical_accuracy_H_normal, # 所有 run 的最佳策略的平均經驗正常命中率
        "mean_accuracy_L": mean_empirical_accuracy_L_normal,
        "mean_accuracy_H_surprise": mean_empirical_accuracy_H_surprise, # 所有 run 的最佳策略的平均經驗意外命中率
        "mean_accuracy_L_surprise": mean_empirical_accuracy_L_surprise,
        "mean_offload_H": mean_empirical_offload_H, # 新增最終平均經驗卸載率
        "mean_offload_L": mean_empirical_offload_L, # 新增最終平均經驗卸載率
        "overall_best_strategy_counts": overall_best_strategy_counts # 用於調試或詳細輸出
    }

def simulate_trial_fig8_sdt(params: ModelParameters, strategy: StrategyFig8, internal_accuracy_scalar: float) -> Tuple[float, bool, bool]:
    """
    基於 SDT 後設認知模型模擬 Fig 8 的單次試驗 (單一高價值項目)。
    策略: (是否編碼, 是否允許卸載)。
    internal_accuracy_scalar: 用於調整內部記憶準確率的因子。
    返回: (總獎勵, 正常命中, 意外命中)
    """
    encode_strategy = strategy[0] # 是否選擇編碼
    offload_allowed = strategy[1] # 是否允許卸載 (在 Fig 8 中，如果允許，SDT 會根據信心決定是否實際卸載)

    total_reward = 0.0
    item_recalled_normal = False
    item_recalled_surprise = False
    actual_offloaded_count = 0 # 實際決定卸載的項目數

    # --- 處理單一高價值項目 ---
    # 這個項目是否有「實際」的內部記憶成功 (基於調整後的內部準確率)
    # Fig 8 的內部記憶準確率由 internal_accuracy_scalar 直接給定 (因為通常只記一個項目，且負荷變化是通過這個 scalar 模擬的)
    is_actually_correct_internal = (np.random.rand() < internal_accuracy_scalar) if encode_strategy else False

    # 生成基於 Type 1 表現的證據信號
    evidence = generate_evidence_signal(params.d_prime, is_actually_correct_internal, params.type1_criterion)

    # 計算基於證據信號的信心水平 ( Fig 8 通常只處理高價值項目)
    confidence = compute_confidence_from_evidence(evidence, params, is_high_value=True)

    # 做出卸載決策 (只有在策略允許卸載時才進行決策)
    decided_to_offload = False
    if offload_allowed: # 如果策略允許卸載
        # Fig 8 通常只涉及高價值項目，使用高價值參數
        decided_to_offload = make_offload_decision_sdt(confidence, params.value_high, params)

    # 模擬回憶結果
    if decided_to_offload:
        actual_offloaded_count += 1
        # 如果決定卸載，回憶成功取決於外部記憶準確率
        if np.random.rand() < params.accuracy_offloaded:
            item_recalled_normal = True
    else:
        # 如果決定不卸載，回憶成功取決於內部記憶的實際表現
        if encode_strategy and is_actually_correct_internal:
            item_recalled_normal = True
            item_recalled_surprise = True # 內部記憶成功計入意外測試

    # 累計獎勵
    if item_recalled_normal:
        total_reward += params.value_high # Fig 8 實驗中項目價值為 8.0

    # 計算卸載成本 (每個實際卸載的項目都有成本)
    total_reward -= actual_offloaded_count * params.cost_offloading

    return total_reward, item_recalled_normal, item_recalled_surprise

def simulate_trial_fig8_original(params: ModelParameters, strategy: StrategyFig8, internal_accuracy_scalar: float) -> Tuple[float, bool, bool]:
    """
    原始模型模擬 Fig 8 的單次試驗 (單一高價值項目)。
    策略: (是否編碼, 是否允許卸載)。
    internal_accuracy_scalar: 用於調整內部記憶準確率的因子。
    返回: (總獎勵, 正常命中, 意外命中)
    """
    encode_strategy = strategy[0]
    offload_allowed = strategy[1]

    total_reward = 0.0
    item_recalled_normal = False
    item_recalled_surprise = False
    actual_offloaded_count = 0

    # 原始模型中，如果允許卸載，就視為全部卸載
    if offload_allowed:
         actual_offloaded_count = params.n_high_items # Fig 8 只有一個高價值項目，所以是 1 * cost

    # 計算卸載成本
    total_reward -= actual_offloaded_count * params.cost_offloading

    # 模擬回憶結果
    if encode_strategy and offload_allowed:
        # 編碼且允許卸載 (原始模型中組合成功率)
        # 內部記憶準確率由 scalar 提供
        p_internal = internal_accuracy_scalar
        combined_success_prob = 1.0 - (1.0 - p_internal) * (1.0 - params.accuracy_offloaded)

        if np.random.rand() < combined_success_prob:
            item_recalled_normal = True
        if np.random.rand() < p_internal: # 意外測試只看內部
            item_recalled_surprise = True

    elif encode_strategy:
        # 只編碼 (內部記憶)
        p_internal = internal_accuracy_scalar
        if np.random.rand() < p_internal:
            item_recalled_normal = True
            item_recalled_surprise = True

    elif offload_allowed:
        # 只允許卸載 (外部記憶)
        if np.random.rand() < params.accuracy_offloaded:
            item_recalled_normal = True

    # 累計獎勵
    if item_recalled_normal:
        total_reward += params.value_high # Fig 8 項目價值為 8.0


    return total_reward, item_recalled_normal, item_recalled_surprise

def run_simulation_fig8(params: ModelParameters, internal_accuracy_scalar: float, use_sdt: bool = False) -> Dict[str, float]:
    """
    運行 Fig. 8 專用的蒙地卡羅模擬，評估在單一項目條件下不同內部記憶準確率對策略選擇的影響。
    params: 模型參數 (應配置為單一高價值項目)。
    internal_accuracy_scalar: 用於 simulate_trial_fig8 的內部記憶準確率因子。
    use_sdt: 是否使用 SDT 模型 (True) 或原始模型 (False)。
    返回: { 'prop_encode_only': 選擇 Encode only 策略的比例, 'prop_offload_only': 選擇 Offload only 策略的比例, ... }
    """
    method_name = "SDT 後設認知" if use_sdt else "原始模型"
    # print(f"評估運行 Fig 8 模擬 ({method_name}, 內部準確率 scalar: {internal_accuracy_scalar:.2f}, {params.n_model_runs} runs, {params.n_episodes_per_strategy_eval} episodes per run)...")

    # Fig. 8 的策略只有 (encode, offload)
    strategies_fig8 = get_strategies_fig8()

    # 選擇要使用的試驗模擬函數
    simulate_func = simulate_trial_fig8_sdt if use_sdt else simulate_trial_fig8_original

    # 存儲所有 run 中，每個策略被選為最佳策略的次數
    overall_best_strategy_counts: Dict[StrategyFig8, int] = {s: 0 for s in strategies_fig8}

    # 累計成功完成模擬的 runs 數量 (避免除以零)
    successful_runs_count = 0

    # 遍歷所有的 runs
    for i_run in range(params.n_model_runs):
        # 可以在這裡選擇打印進度，但 Fig 8 的 Runs 較少，可能不需要太頻繁
        # if (i_run + 1) % (max(1, params.n_model_runs // 100)) == 0:
        #     print(f"  運行中... Fig 8 Run {i_run + 1}/{params.n_model_runs}")

        # 初始化本次 run 中每個策略的總獎勵和樣本計數
        total_reward_per_strategy_this_run: Dict[StrategyFig8, float] = defaultdict(float)
        sample_count_per_strategy_this_run: Dict[StrategyFig8, int] = defaultdict(int)

        # 在本次 run 中進行多次 episodes (試驗)
        for i_episode in range(params.n_episodes_per_strategy_eval):
            # 隨機選擇一個策略
            chosen_strategy = random.choice(strategies_fig8)
            chosen_strategy_tuple = tuple(chosen_strategy) # 確保是 tuple 作為字典鍵

            # 模擬一次試驗，注意這裡調用的是 simulate_trial_fig8_sdt 或 simulate_trial_fig8_original
            reward, item_recalled_normal, item_recalled_surprise = simulate_func(chosen_strategy_tuple, params, internal_accuracy_scalar)

            # 將結果累加到本次 run 的統計中
            total_reward_per_strategy_this_run[chosen_strategy_tuple] += reward
            sample_count_per_strategy_this_run[chosen_strategy_tuple] += 1

        # 在本次 run 結束後，計算每個策略的平均獎勵
        mean_rewards_this_run: Dict[StrategyFig8, float] = {}
        max_avg_reward_this_run = -float('inf')
        strategies_sampled_this_run = [s for s in strategies_fig8 if sample_count_per_strategy_this_run[s] > 0]

        if not strategies_sampled_this_run:
             # 如果本次 run 中沒有任何策略被 sampled，則沒有最佳策略，跳過本次 run 的統計
             # print(f"警告: Fig 8 Run {i_run+1} 沒有策略被 sampled。跳過。")
             continue

        for strategy in strategies_sampled_this_run:
             if sample_count_per_strategy_this_run[strategy] > 0:
                mean_reward = total_reward_per_strategy_this_run[strategy] / sample_count_per_strategy_this_run[strategy]
                mean_rewards_this_run[strategy] = mean_reward
                if mean_reward > max_avg_reward_this_run:
                    max_avg_reward_this_run = mean_reward

        # 找出本次 run 中平均獎勵最高的策略
        tolerance = 1e-9
        best_strategies_this_run = [
            s for s, avg_r in mean_rewards_this_run.items() if abs(avg_r - max_avg_reward_this_run) < tolerance
        ]
    
        # 如果有多個並列最佳，隨機選擇一個作為本次 run 的最佳策略
        best_strategy_for_this_run = random.choice(best_strategies_this_run)

        # 累加本次 run 的最佳策略的選擇次數
        overall_best_strategy_counts[best_strategy_for_this_run] += 1

        successful_runs_count += 1

    # 在所有 runs 結束後，計算包含特定決策 (encode only, offload only, etc.) 的策略被選為最佳的比例
    total_runs_considered = successful_runs_count

    if total_runs_considered == 0:
        # print(f"警告: 沒有成功完成的 runs。無法計算 Fig 8 結果。")
        return {
            "prop_encode_only": 0.0,
            "prop_offload_only": 0.0,
            "prop_encode_and_offload": 0.0,
            "prop_neither": 0.0
        }

    # 計算每種特定策略組合被選為最佳的比例
    strategy_proportions = {s: count / total_runs_considered for s, count in overall_best_strategy_counts.items()}

    # 根據 Fig 8 的圖表需求，返回特定策略組合的比例
    return {
        "prop_encode_only": strategy_proportions.get((True, False), 0.0), # (encode=True, offload=False)
        "prop_offload_only": strategy_proportions.get((False, True), 0.0), # (encode=False, offload=True)
        "prop_encode_and_offload": strategy_proportions.get((True, True), 0.0), # (encode=True, offload=True)
        "prop_neither": strategy_proportions.get((False, False), 0.0) # (encode=False, offload=False)
    }

# --- 模擬 Fig. 2: 不允許卸載 ---
def simulate_fig2(base_params: ModelParameters, use_sdt: bool = False):
    """
    模擬 Fig. 2: 不允許卸載的條件。
    base_params: 基礎模型參數。
    use_sdt: 是否使用 SDT 後設認知模型。
    """
    method_name = "SDT 後設認知" if use_sdt else "原始模型"
    print(f"\n開始模擬 Fig. 2 (不允許卸載) - {method_name} 方法...")

    # Fig.2 使用標準參數，但不允許卸載
    params_fig2 = replace(base_params,
        n_high_items=3, n_low_items=3 # Fig. 2 標準項目數量
    )
    # Fig.2 不允許卸載，所以 offload_H_allowed 和 offload_L_allowed 總是 False
    strategies_fig2 = get_all_strategies(allow_offloading=False)

    # 使用 run_simulation 函數運行模擬
    results = run_simulation(params_fig2, strategies_fig2, use_sdt=use_sdt)

    # 匯總和打印結果
    # 計算選擇內部記憶低價值和高價值策略的比例 (無論是否同時選擇另一種內部記憶)
    prop_encode_low = sum(prop for s, prop in results["strategy_proportions"].items() if s[1]) # s[1] 是 store_L_internal
    prop_encode_high = sum(prop for s, prop in results["strategy_proportions"].items() if s[0]) # s[0] 是 store_H_internal
    acc_L_norm = results["mean_accuracy_L"]
    acc_H_norm = results["mean_accuracy_H"]

    print(f"Fig. 2 結果 ({method_name}): ")
    print(f"  策略 - 編碼低價值比例: {prop_encode_low:.3f}")
    print(f"  策略 - 編碼高價值比例: {prop_encode_high:.3f}")
    print(f"  正常準確率 - 低價值: {acc_L_norm:.3f}")
    print(f"  正常準確率 - 高價值: {acc_H_norm:.3f}")
    
    # 繪圖
    fig, axs = plt.subplots(1, 2, figsize=(10, 4))
    fig.suptitle(f"Fig. 2 Simulation: No Offloading ({method_name})", fontsize=14)

    # 策略選擇比例圖 (編碼低價值 vs 編碼高價值)
    axs[0].bar(["編碼低價值", "編碼高價值"], [prop_encode_low, prop_encode_high], color='cornflowerblue')
    axs[0].set_title("策略選擇比例")
    axs[0].set_ylabel("比例")
    axs[0].set_ylim(0, 1.05)
    
    # 正常準確率圖 (低價值 vs 高價值)
    axs[1].bar(["低價值", "高價值"], [acc_L_norm, acc_H_norm], color='cornflowerblue')
    axs[1].set_title("正常測試準確率")
    axs[1].set_ylabel("平均經驗準確率")
    axs[1].set_ylim(0, 1.05)
    
    plt.tight_layout(rect=[0, 0, 1, 0.96]) # 調整圖片間距以防止標題重疊
    plt.show()
    
    return results

# --- 模擬 Fig. 3: 允許卸載 ---
def simulate_fig3(base_params: ModelParameters, use_sdt: bool = False):
    """
    模擬 Fig. 3: 允許所有類型項目卸載的條件。
    base_params: 基礎模型參數。
    use_sdt: 是否使用 SDT 後設認知模型。
    """
    method_name = "SDT 後設認知" if use_sdt else "原始模型"
    print(f"\n開始模擬 Fig. 3 (允許卸載) - {method_name} 方法...")

    # Fig.3 使用標準參數，且允許所有卸載選項
    params_fig3 = replace(base_params,
        n_high_items=3, n_low_items=3 # Fig. 3 標準項目數量
    )
    # Fig.3 允許所有卸載
    strategies_fig3 = get_all_strategies(allow_offloading=True, offload_restriction="none")

    # 使用 run_simulation 函數運行模擬
    results = run_simulation(params_fig3, strategies_fig3, use_sdt=use_sdt)

    # 匯總和打印結果
    # 計算選擇內部記憶低價值和高價值策略的比例
    prop_encode_low = sum(prop for s, prop in results["strategy_proportions"].items() if s[1])
    prop_encode_high = sum(prop for s, prop in results["strategy_proportions"].items() if s[0])
    # 計算選擇允許卸載低價值和高價值策略的比例
    prop_offload_low_allowed = sum(prop for s, prop in results["strategy_proportions"].items() if s[3]) # s[3] 是 offload_L_allowed
    prop_offload_high_allowed = sum(prop for s, prop in results["strategy_proportions"].items() if s[2]) # s[2] 是 offload_H_allowed

    # 注意: run_simulation 返回的是實際的平均經驗卸載率，而不是策略中「允許卸載」的比例
    mean_offload_L = results["mean_offload_L"]
    mean_offload_H = results["mean_offload_H"]


    acc_L_norm = results["mean_accuracy_L"]
    acc_H_norm = results["mean_accuracy_H"]

    print(f"Fig. 3 結果 ({method_name}): ")
    print(f"  策略 - 編碼低價值比例: {prop_encode_low:.3f}")
    print(f"  策略 - 編碼高價值比例: {prop_encode_high:.3f}")
    print(f"  策略 - 允許卸載低價值比例: {prop_offload_low_allowed:.3f}") # 打印策略選擇比例
    print(f"  策略 - 允許卸載高價值比例: {prop_offload_high_allowed:.3f}") # 打印策略選擇比例
    print(f"  平均經驗正常準確率 - 低價值: {acc_L_norm:.3f}")
    print(f"  平均經驗正常準確率 - 高價值: {acc_H_norm:.3f}")
    print(f"  平均經驗卸載率 - 低價值: {mean_offload_L:.3f}") # 打印實際經驗卸載率
    print(f"  平均經驗卸載率 - 高價值: {mean_offload_H:.3f}") # 打印實際經驗卸載率

    
    # 繪圖
    fig, axs = plt.subplots(1, 3, figsize=(15, 5))
    fig.suptitle(f"Fig. 3 Simulation: Offloading Allowed ({method_name})", fontsize=14)

    # 策略選擇比例圖 (編碼和允許卸載的比例)
    strategy_labels = ["編碼\n低價值", "編碼\n高價值", "允許卸載\n低價值", "允許卸載\n高價值"]
    strategy_values = [prop_encode_low, prop_encode_high, prop_offload_low_allowed, prop_offload_high_allowed]
    axs[0].bar(strategy_labels, strategy_values, color='cornflowerblue')
    axs[0].set_title("策略選擇比例")
    axs[0].set_ylabel("比例")
    axs[0].set_ylim(0, 1.05)
    plt.setp(axs[0].get_xticklabels(), rotation=15, ha="right") # 旋轉 x 軸標籤

    # 正常準確率圖 (低價值 vs 高價值)
    axs[1].bar(["低價值", "高價值"], [acc_L_norm, acc_H_norm], color='cornflowerblue')
    axs[1].set_title("正常測試準確率")
    axs[1].set_ylabel("平均經驗準確率")
    axs[1].set_ylim(0, 1.05)
    
    # 平均經驗卸載率圖 (低價值 vs 高價值)
    axs[2].bar(["低價值", "高價值"], [mean_offload_L, mean_offload_H], color=['cornflowerblue', 'darkred']) # 使用不同顏色區分
    axs[2].set_title("平均經驗卸載率")
    axs[2].set_ylabel("平均卸載率")
    axs[2].set_ylim(0, 1.05)
    
    plt.tight_layout(rect=[0, 0, 1, 0.96])
    plt.show()
    
    return results

# --- 模擬 Fig. 4: 只允許卸載高價值項目 ---
def simulate_fig4(base_params: ModelParameters, use_sdt: bool = False):
    """
    模擬 Fig. 4: 只允許卸載高價值項目的條件。
    base_params: 基礎模型參數。
    use_sdt: 是否使用 SDT 後設認知模型。
    """
    method_name = "SDT 後設認知" if use_sdt else "原始模型"
    print(f"\n開始模擬 Fig. 4 (只允許卸載高價值項目) - {method_name} 方法...")

    # Fig.4 使用標準參數，只允許卸載高價值
    params_fig4 = replace(base_params,
        n_high_items=3, n_low_items=3 # Fig. 4 標準項目數量
    )
    # 策略: 允許卸載，但僅限高價值 (即 offload_L_allowed 總是 False)
    strategies_fig4 = get_all_strategies(allow_offloading=True, offload_restriction="high_only")

    # 使用 run_simulation 函數運行模擬
    results = run_simulation(params_fig4, strategies_fig4, use_sdt=use_sdt)

    # 匯總和打印結果
    # 計算選擇內部記憶低價值和高價值策略的比例
    prop_encode_low = sum(prop for s, prop in results["strategy_proportions"].items() if s[1])
    prop_encode_high = sum(prop for s, prop in results["strategy_proportions"].items() if s[0])
    # 計算選擇允許卸載低價值和高價值策略的比例 (驗證低價值卸載比例是否為 0)
    prop_offload_low_allowed = sum(prop for s, prop in results["strategy_proportions"].items() if s[3])
    prop_offload_high_allowed = sum(prop for s, prop in results["strategy_proportions"].items() if s[2])

    # 實際經驗卸載率
    mean_offload_L = results["mean_offload_L"]
    mean_offload_H = results["mean_offload_H"]

    acc_L_norm = results["mean_accuracy_L"]
    acc_H_norm = results["mean_accuracy_H"]

    print(f"Fig. 4 結果 ({method_name}, 只允許卸載高價值): ")
    print(f"  策略 - 編碼低價值比例: {prop_encode_low:.3f}")
    print(f"  策略 - 編碼高價值比例: {prop_encode_high:.3f}")
    print(f"  策略 - 允許卸載低價值比例 (應為0): {prop_offload_low_allowed:.3f}")
    print(f"  策略 - 允許卸載高價值比例: {prop_offload_high_allowed:.3f}")
    print(f"  平均經驗正常準確率 - 低價值: {acc_L_norm:.3f}")
    print(f"  平均經驗正常準確率 - 高價值: {acc_H_norm:.3f}")
    print(f"  平均經驗卸載率 - 低價值: {mean_offload_L:.3f}")
    print(f"  平均經驗卸載率 - 高價值: {mean_offload_H:.3f}")
    
    # 繪圖
    fig, axs = plt.subplots(1, 3, figsize=(15, 5))
    fig.suptitle(f"Fig. 4 Simulation: High-Value Offloading Only ({method_name})", fontsize=14)

    # 策略選擇比例圖
    strategy_labels = ["編碼\n低價值", "編碼\n高價值", "允許卸載\n低價值", "允許卸載\n高價值"]
    strategy_values = [prop_encode_low, prop_encode_high, prop_offload_low_allowed, prop_offload_high_allowed]
    axs[0].bar(strategy_labels, strategy_values, color='cornflowerblue')
    axs[0].set_title("策略選擇比例")
    axs[0].set_ylabel("比例")
    axs[0].set_ylim(0, 1.05)
    plt.setp(axs[0].get_xticklabels(), rotation=15, ha="right")

    # 正常準確率圖
    axs[1].bar(["低價值", "高價值"], [acc_L_norm, acc_H_norm], color='cornflowerblue')
    axs[1].set_title("正常測試準確率")
    axs[1].set_ylabel("平均經驗準確率")
    axs[1].set_ylim(0, 1.05)
    
    # 平均經驗卸載率圖
    axs[2].bar(["低價值", "高價值"], [mean_offload_L, mean_offload_H], color=['cornflowerblue', 'darkred'])
    axs[2].set_title("平均經驗卸載率")
    axs[2].set_ylabel("平均卸載率")
    axs[2].set_ylim(0, 1.05)
    
    plt.tight_layout(rect=[0, 0, 1, 0.96])
    plt.show()
    
    return results

# --- 新增: 模擬 Fig. 5: 意外記憶測試 ---
def simulate_fig5(base_params: ModelParameters, use_sdt: bool = False):
    """
    模擬 Fig. 5: 意外記憶測試條件下的準確率。
    base_params: 基礎模型參數。
    use_sdt: 是否使用 SDT 後設認知模型。
    """
    method_name = "SDT 後設認知" if use_sdt else "原始模型"
    print(f"\n開始模擬 Fig. 5 (意外記憶測試) - {method_name} 方法...")

    # Fig. 5 比較兩種條件下的意外測試準確率:
    # 條件 1: 不允許卸載 (所有回憶都來自內部記憶)
    # 條件 2: 只允許卸載高價值項目 (低價值回憶來自內部，高價值回憶可能來自內部或外部，但意外測試只看內部)

    params_common = replace(base_params,
        n_high_items=3, n_low_items=3 # Fig. 5 標準項目數量
    )

    # 條件1: 不允許卸載
    print(f"  Fig. 5 條件1 ({method_name}): 不允許卸載")
    strategies_cond1 = get_all_strategies(allow_offloading=False)
    results_cond1 = run_simulation(params_common, strategies_cond1, use_sdt=use_sdt)

    # 條件2: 只允許卸載高價值項目
    print(f"  Fig. 5 條件2 ({method_name}): 只允許卸載高價值")
    strategies_cond2 = get_all_strategies(allow_offloading=True, offload_restriction="high_only")
    results_cond2 = run_simulation(params_common, strategies_cond2, use_sdt=use_sdt)

    # 匯總和打印結果 (重點是意外測試準確率)
    acc_L_cond1_surprise = results_cond1["mean_accuracy_L_surprise"]
    acc_H_cond1_surprise = results_cond1["mean_accuracy_H_surprise"]
    acc_L_cond2_surprise = results_cond2["mean_accuracy_L_surprise"]
    acc_H_cond2_surprise = results_cond2["mean_accuracy_H_surprise"]
    
    print(f"Fig. 5 結果 ({method_name}):")
    print(f"  條件1 (不允許卸載) 意外測試準確率: 低價值={acc_L_cond1_surprise:.3f}, 高價值={acc_H_cond1_surprise:.3f}")
    print(f"  條件2 (只允許卸載高價值) 意外測試準確率: 低價值={acc_L_cond2_surprise:.3f}, 高價值={acc_H_cond2_surprise:.3f}")

    # 繪圖
    fig, axs = plt.subplots(1, 2, figsize=(10, 4), sharey=True) # 共用 y 軸
    fig.suptitle(f"Fig. 5 Simulation: Surprise-Test Accuracy ({method_name})", fontsize=14)

    # 條件1 意外測試準確率圖
    axs[0].bar(["低價值", "高價值"], [acc_L_cond1_surprise, acc_H_cond1_surprise], color='cornflowerblue')
    axs[0].set_title("不允許卸載")
    axs[0].set_ylabel("平均經驗意外測試準確率")
    axs[0].set_ylim(0, 1.0) # 準確率範圍

    # 條件2 意外測試準確率圖
    axs[1].bar(["低價值", "高價值"], [acc_L_cond2_surprise, acc_H_cond2_surprise], color='lightcoral')
    axs[1].set_title("只允許卸載高價值")
    # axs[1].set_ylabel("平均經驗意外測試準確率") # 共用 y 軸，所以不需要這個標籤
    axs[1].set_ylim(0, 1.0)

    plt.tight_layout(rect=[0, 0, 1, 0.94])
    plt.show()

    # 返回所有結果，方便比較
    return {"cond1": results_cond1, "cond2": results_cond2}

# --- 比較原始方法和 SDT 方法的輔助函數 (用於 Fig 2-5) ---
def compare_methods(base_params: ModelParameters, figure_func, figure_name: str):
    """
    運行並比較原始模型和 SDT 後設認知模型在特定圖表上的結果。
    base_params: 基礎模型參數。
    figure_func: 對應圖表的模擬函數 (如 simulate_fig2)。
    figure_name: 圖表名稱 (如 "Fig. 2")。
    """
    print(f"\n=== {figure_name} 方法比較 ===")

    print(f"\n--- 原始模型 ---")
    results_original = figure_func(base_params, use_sdt=False)

    print(f"\n--- SDT 後設認知模型 ---")
    results_sdt = figure_func(base_params, use_sdt=True)

    print(f"\n--- 比較結果摘要 ({figure_name}) ---")
    # 提取並比較關鍵指標 (正常準確率和卸載率)
    print(f"  高價值正常準確率: 原始={results_original.get('mean_accuracy_H', 0.0):.3f}, SDT={results_sdt.get('mean_accuracy_H', 0.0):.3f}")
    print(f"  低價值正常準確率: 原始={results_original.get('mean_accuracy_L', 0.0):.3f}, SDT={results_sdt.get('mean_accuracy_L', 0.0):.3f}")

    # 檢查是否有卸載率指標 (Fig 2 沒有卸載率)
    if 'mean_offload_H' in results_original and 'mean_offload_H' in results_sdt:
        print(f"  高價值平均經驗卸載率: 原始={results_original['mean_offload_H']:.3f}, SDT={results_sdt['mean_offload_H']:.3f}")
        print(f"  低價值平均經驗卸載率: 原始={results_original['mean_offload_L']:.3f}, SDT={results_sdt['mean_offload_L']:.3f}")

    # 檢查是否有意外測試準確率 (Fig 5 有)
    if 'mean_accuracy_H_surprise' in results_original and 'mean_accuracy_H_surprise' in results_sdt:
         print(f"  高價值平均經驗意外測試準確率: 原始={results_original['mean_accuracy_H_surprise']:.3f}, SDT={results_sdt['mean_accuracy_H_surprise']:.3f}")
         print(f"  低價值平均經驗意外測試準確率: 原始={results_original['mean_accuracy_L_surprise']:.3f}, SDT={results_sdt['mean_accuracy_L_surprise']:.3f}")


    return {'original': results_original, 'sdt': results_sdt}

# --- 模擬 Fig. 6: 記憶負荷對卸載率的影響 (僅 SDT 模型) ---
def simulate_fig6(base_params: ModelParameters, use_sdt: bool = True):
    # 實現圖表 6 的模擬邏輯
    return {}



# --- 新增: 模擬 Fig. 6: 記憶負荷對卸載率的影響 (僅 SDT 模型) ---
def simulate_fig6(base_params: ModelParameters, use_sdt: bool = True):
    # 實現圖表 6 的模擬邏輯
    # Fig 6 的原始邏輯是模擬不同記憶負荷下的卸載率
    method_name = "SDT 後設認知" if use_sdt else "原始模型"
    print(f"\n開始模擬 Fig. 6 (記憶負荷對卸載率的影響) - {method_name} 方法...")

    # Fig. 6 比較兩種條件下的卸載率:
    # 條件1: 每種價值1個項目 (1 high, 1 low) -> 總負荷 2
    # 條件2: 每種價值3個項目 (3 high, 3 low) -> 總負荷 6

    # 條件1: 每種價值1個項目 (總負荷 2)
    print(f"  Fig. 6 條件1 ({method_name}): 每種價值1個項目 (負荷 2)")
    params_load2 = replace(base_params,
        n_high_items=1, n_low_items=1 # Fig. 6 條件1 項目數量
    )
    # 允許所有卸載策略
    strategies_fig6 = get_all_strategies(allow_offloading=True)
    results_load2 = run_simulation(params_load2, strategies_fig6, use_sdt=use_sdt)

    # 匯總卸載率 (這是在最佳策略下實際的平均經驗卸載率)
    mean_offload_L_load2 = results_load2["mean_offload_L"]
    mean_offload_H_load2 = results_load2["mean_offload_H"]
    print(f"    平均經驗卸載率 (負荷 2) - 低價值: {mean_offload_L_load2:.3f}, 高價值: {mean_offload_H_load2:.3f}")


    # 條件2: 每種價值3個項目 (總負荷 6)
    print(f"  Fig. 6 條件2 ({method_name}): 每種價值3個項目 (負荷 6, 標準負荷)")
    params_load6 = replace(base_params,
        n_high_items=3, n_low_items=3 # Fig. 6 條件2 項目數量 (標準負荷)
    )
    # 允許所有卸載策略
    strategies_fig6 = get_all_strategies(allow_offloading=True)
    results_load6 = run_simulation(params_load6, strategies_fig6, use_sdt=use_sdt)

    # 匯總卸載率 (這是在最佳策略下實際的平均經驗卸載率)
    mean_offload_L_load6 = results_load6["mean_offload_L"]
    mean_offload_H_load6 = results_load6["mean_offload_H"]
    print(f"    平均經驗卸載率 (負荷 6) - 低價值: {mean_offload_L_load6:.3f}, 高價值: {mean_offload_H_load6:.3f}")


    # 繪圖
    fig, axs = plt.subplots(1, 2, figsize=(10, 4), sharey=True) # 共用 y 軸
    fig.suptitle(f"Fig. 6 Simulation: Effect of Memory Load on Offloading Rate ({method_name})", fontsize=14)

    # 負荷 2 條件下的卸載率圖
    axs[0].bar(["低價值", "高價值"], [mean_offload_L_load2, mean_offload_H_load2], color='cornflowerblue')
    axs[0].set_title("負荷 2 (1高, 1低)")
    axs[0].set_ylabel("平均經驗卸載率")
    axs[0].set_ylim(0, 1.05) # 卸載率範圍

    # 負荷 6 條件下的卸載率圖
    axs[1].bar(["低價值", "高價值"], [mean_offload_L_load6, mean_offload_H_load6], color='lightcoral')
    axs[1].set_title("負荷 6 (3高, 3低)")
    axs[1].set_ylim(0, 1.05)

    plt.tight_layout(rect=[0, 0, 1, 0.94])
    plt.show()
    
    return {"load2": results_load2, "load6": results_load6}


# --- 新增: 模擬 Fig. 7: 卸載成本的影響 (僅 SDT 模型) ---
def simulate_fig7(base_params: ModelParameters, use_sdt: bool = True):
    """
    模擬 Fig. 7: 不同卸載成本下，內部記憶編碼率和實際卸載率的變化。
    base_params: 基礎模型參數。
    use_sdt: 是否使用 SDT 後設認知模型 (圖 7 僅為 SDT)。
    """
    if not use_sdt:
        print("警告: Fig. 7 模擬主要針對 SDT 模型設計，原始模型結果可能不適用或無意義。")
        # 可以選擇在這裡返回空結果或者運行原始模型，但圖表可能需要調整
        # 為了與主執行區塊 Fig 6-8 僅運行 SDT 的邏輯一致，這裡只運行 SDT
        return {}

    method_name = "SDT 後設認知" # 圖 7 僅為 SDT
    print(f"\n開始模擬 Fig. 7 (卸載成本的影響) - {method_name} 方法...")

    # 卸載成本從 0.0 到 2.0 分成 9 個點
    offloading_costs_to_test = np.linspace(0.0, 2.0, 9)

    # 初始化結果存儲列表
    # 這裡存儲的是在每個成本下，不同策略被選為最佳策略的比例中的「包含內部記憶/包含允許卸載」的比例
    memory_encoding_rate_low = []
    memory_encoding_rate_high = []
    # 這裡存儲的是 run_simulation 返回的「平均經驗卸載率」
    mean_empirical_offload_low = []
    mean_empirical_offload_high = []

    # 所有策略 (允許所有卸載選項)
    strategies_fig7 = get_all_strategies(allow_offloading=True)

    # 針對每個卸載成本運行模擬
    for cost_idx, cost in enumerate(offloading_costs_to_test):
        print(f"  模擬卸載成本 ({cost_idx+1}/{len(offloading_costs_to_test)}): {cost:.2f}")

        # 為當前成本創建參數對象
        current_params = replace(base_params,
            n_high_items=3, n_low_items=3, # Fig. 7 使用標準項目數量
            cost_offloading=cost, # 使用當前的卸載成本
            # Fig 7 通常關注成本對決策和表現的影響，其他參數保持 base_params
        )

        # 使用 run_simulation 函數運行 SDT 模擬
        sim_results = run_simulation(current_params, strategies_fig7, use_sdt=True) # 圖 7 固定使用 SDT

        # 從結果中提取策略選擇比例和平均經驗卸載率
        # 策略選擇比例中「包含內部記憶低價值」的比例
        prop_encode_low_strategy = sum(prop for s, prop in sim_results["strategy_proportions"].items() if s[1]) # s[1] 是 store_L_internal
        # 策略選擇比例中「包含內部記憶高價值」的比例
        prop_encode_high_strategy = sum(prop for s, prop in sim_results["strategy_proportions"].items() if s[0]) # s[0] 是 store_H_internal

        # 從 run_simulation 結果中獲取平均經驗卸載率
        actual_mean_offload_L = sim_results["mean_offload_L"]
        actual_mean_offload_H = sim_results["mean_offload_H"]


        # 將結果添加到列表中
        memory_encoding_rate_low.append(prop_encode_low_strategy)
        memory_encoding_rate_high.append(prop_encode_high_strategy)
        mean_empirical_offload_low.append(actual_mean_offload_L)
        mean_empirical_offload_high.append(actual_mean_offload_H)


    print(f"Fig. 7 結果 ({method_name}): ")
    for i, cost_val in enumerate(offloading_costs_to_test):
        print(f"  成本={cost_val:.2f}: 編碼低={memory_encoding_rate_low[i]:.3f}, 編碼高={memory_encoding_rate_high[i]:.3f}, 卸載低={mean_empirical_offload_low[i]:.3f}, 卸載高={mean_empirical_offload_high[i]:.3f}")


    # 繪圖
    fig, axs = plt.subplots(1, 2, figsize=(12, 5))
    fig.suptitle(f"Fig. 7 Simulation: Effect of Offloading Cost ({method_name})", fontsize=14)

    # 左側子圖：內部記憶編碼率 vs 卸載成本 (高/低價值)
    axs[0].plot(offloading_costs_to_test, memory_encoding_rate_low, 'o-', label="編碼低價值", color='cornflowerblue')
    axs[0].plot(offloading_costs_to_test, memory_encoding_rate_high, 's-', label="編碼高價值", color='brown')
    axs[0].set_title("內部記憶編碼策略選擇比例") # 修改標題以反映是策略選擇比例
    axs[0].set_xlabel("卸載成本")
    axs[0].set_ylabel("策略選擇比例")
    axs[0].set_ylim(0, 1.05)
    axs[0].legend()
    axs[0].grid(True, linestyle='--', alpha=0.7)

    # 右側子圖：平均經驗卸載率 vs 卸載成本 (高/低價值)
    axs[1].plot(offloading_costs_to_test, mean_empirical_offload_low, 'o--', label="卸載低價值 (實際)", color='cornflowerblue')
    axs[1].plot(offloading_costs_to_test, mean_empirical_offload_high, 's--', label="卸載高價值 (實際)", color='brown')
    axs[1].set_title("平均經驗卸載率")
    axs[1].set_xlabel("卸載成本")
    axs[1].set_ylabel("平均卸載率")
    axs[1].set_ylim(0, 1.05)
    axs[1].legend()
    axs[1].grid(True, linestyle='--', alpha=0.7)


    plt.tight_layout(rect=[0, 0, 1, 0.96])
    plt.show()

    return {
        "offloading_costs": list(offloading_costs_to_test),
        "memory_encoding_rate_low": memory_encoding_rate_low,
        "memory_encoding_rate_high": memory_encoding_rate_high,
        "mean_empirical_offload_low": mean_empirical_offload_low,
        "mean_empirical_offload_high": mean_empirical_offload_high,
    }


# --- 新增: 模擬 Fig. 8: 內部記憶準確率對卸載率的影響 (僅 SDT 模型) ---
def simulate_fig8(base_params: ModelParameters, use_sdt: bool = True):
    """
    模擬 Fig. 8: 不同內部記憶準確率下，低成本和高成本條件的實際卸載率以及成本敏感度。
    base_params: 基礎模型參數。
    use_sdt: 是否使用 SDT 後設認知模型 (圖 8 僅為 SDT)。
    """
    if not use_sdt:
        print("警告: Fig. 8 模擬主要針對 SDT 模型設計，原始模型結果可能不適用或無意義。")
        # 為了與主執行區塊 Fig 6-8 僅運行 SDT 的邏輯一致，這裡只運行 SDT
        return {}

    method_name = "SDT 後設認知" # 圖 8 僅為 SDT
    print(f"\n開始模擬 Fig. 8 (內部記憶準確率的影響) - {method_name} 方法...")

    # Fig. 8 變化的是內部記憶準確率 (internal accuracy)
    # 測試從 0.55 到 0.95 共 9 個點
    internal_accuracies_to_test = np.linspace(0.55, 0.95, 9)

    # 初始化結果存儲列表
    # 這裡存儲的是 run_simulation_fig8 返回的 'prop_offload' (即包含 offload 決策的策略被選為最佳的比例)
    low_cost_offload_rates = []
    high_cost_offload_rates = []
    internal_acc_values_recorded = [] # 記錄實際使用的 internal accuracy 值


    # 遍歷不同的內部記憶準確率
    for int_acc in internal_accuracies_to_test:
        print(f"  模擬內部記憶準確率: {int_acc:.2f}")

        # Fig 8 模擬的是單一項目，價值為 base_params.value_high (8.0)
        # 項目數量設為 1 個高價值項目，0 個低價值項目
        # 內部記憶準確率由當前的 int_acc 值決定，傳入 simulate_trial_fig8_sdt 或 simulate_trial_fig8_original

        # 低卸載成本條件
        params_low_cost = replace(base_params,
            n_high_items=1, n_low_items=0, # Fig 8 使用單一高價值項目
            value_high=base_params.value_high, value_low=0, # 使用高價值
            cost_offloading=1.0, # 低卸載成本設定為 1.0 (如 R 程式碼所示)
            # Fig 8 模擬中，內部記憶準確率直接由 internal_accuracy_scalar 傳入 simulate_trial_fig8_sdt/original
            # 所以這裡不需要修改 accuracy_internal_X_items 參數
        )
        # 運行低卸載成本的模擬 (使用 Fig 8 專用的 run_simulation_fig8)
        # 注意將當前的 int_acc 和 use_sdt 傳入 run_simulation_fig8
        results_low_cost = run_simulation_fig8(params_low_cost, int_acc, use_sdt=True) # 圖 8 固定使用 SDT
        low_cost_offload_rates.append(results_low_cost['prop_offload'])


        # 高卸載成本條件
        params_high_cost = replace(base_params,
             n_high_items=1, n_low_items=0, # Fig 8 使用單一高價值項目
             value_high=base_params.value_high, value_low=0,
             cost_offloading=2.0, # 高卸載成本設定為 2.0 (如 R 程式碼所示)
        )

        # 運行高卸載成本的模擬
        results_high_cost = run_simulation_fig8(params_high_cost, int_acc, use_sdt=True) # 圖 8 固定使用 SDT
        high_cost_offload_rates.append(results_high_cost['prop_offload'])

        # 記錄當前使用的 internal accuracy 值
        internal_acc_values_recorded.append(int_acc)

    # 計算成本敏感度 (低成本卸載策略選擇比例 - 高成本卸載策略選擇比例)
    cost_sensitivity = np.array(low_cost_offload_rates) - np.array(high_cost_offload_rates)


    print(f"Fig. 8 結果 ({method_name}): ")
    for i, int_acc_val in enumerate(internal_acc_values_recorded):
        print(f"  內部準確率={int_acc_val:.2f}: 低成本卸載比例={low_cost_offload_rates[i]:.3f}, 高成本卸載比例={high_cost_offload_rates[i]:.3f}, 成本敏感度={cost_sensitivity[i]:.3f}")


    # 繪圖
    fig, axs = plt.subplots(1, 2, figsize=(12, 5))
    fig.suptitle(f"Fig. 8 Simulation: Effect of Internal Accuracy ({method_name})", fontsize=14)

    # 左側子圖：卸載策略選擇比例 vs 內部記憶準確率 (低/高成本)
    axs[0].plot(internal_acc_values_recorded, low_cost_offload_rates, 'o-', label="低成本 (成本=1.0)", color='blue')
    axs[0].plot(internal_acc_values_recorded, high_cost_offload_rates, 's-', label="高成本 (成本=2.0)", color='darkred')
    axs[0].set_title("卸載策略選擇比例") # 修改標題
    axs[0].set_xlabel("內部記憶準確率")
    axs[0].set_ylabel("策略選擇比例 (包含卸載)") # 修改標籤
    axs[0].set_ylim(0, 1.05)
    axs[0].legend()
    axs[0].grid(True, linestyle='--', alpha=0.7)

    # 右側子圖：成本敏感度 vs 內部記憶準確率
    axs[1].plot(internal_acc_values_recorded, cost_sensitivity, '^-', label="成本敏感度", color='green')
    axs[1].set_title("成本敏感度")
    axs[1].set_xlabel("內部記憶準確率")
    axs[1].set_ylabel("卸載策略選擇比例差異") # 修改標籤
    axs[1].set_ylim(0, 0.55) # R 程式碼中的範圍
    axs[1].legend()
    axs[1].grid(True, linestyle='--', alpha=0.7)

    plt.tight_layout(rect=[0, 0, 1, 0.96])
    plt.show()

    return {
        "internal_accuracies": internal_acc_values_recorded,
        "low_cost_offload_prop": low_cost_offload_rates, # 返回策略選擇比例
        "high_cost_offload_prop": high_cost_offload_rates, # 返回策略選擇比例
        "cost_sensitivity": list(cost_sensitivity)
    }


# --- 模擬 SDT 偏差效應 (僅 SDT 模型) ---
def simulate_sdt_bias_effect(base_params: ModelParameters, use_sdt: bool = True):
    # 實現 SDT 偏差效應的模擬邏輯
    # 這個函數模擬不同 Metacognitive Bias (Meta-c) 參數下，策略選擇和卸載行為的變化
    if not use_sdt:
        print("警告: SDT 偏差效應模擬僅針對 SDT 模型設計。")
        return {}

    method_name = "SDT 後設認知"
    print(f"\n開始模擬 SDT 偏差效應 - {method_name} 方法...")

    # 測試不同的 Metacognitive Bias (Meta-c) 值
    # 假設測試從 -1.0 到 1.0 分成 9 個點
    bias_values_to_test = np.linspace(-1.0, 1.0, 9)

    # 初始化結果存儲列表
    # 這裡存儲的是在每個偏差值下，不同策略被選為最佳策略的比例
    memory_encoding_rate_low = []
    memory_encoding_rate_high = []
    mean_empirical_offload_low = []
    mean_empirical_offload_high = []

    # 所有策略 (允許所有卸載選項)
    strategies_bias = get_all_strategies(allow_offloading=True)

    # 針對每個偏差值運行模擬
    for bias_idx, bias_val in enumerate(bias_values_to_test):
        print(f"  模擬偏差值 ({bias_idx+1}/{len(bias_values_to_test)}): {bias_val:.2f}")

        # 為當前偏差值創建參數對象
        current_params = replace(base_params,
            n_high_items=3, n_low_items=3, # 標準項目數量
            confidence_criterion_bias=bias_val, # 使用當前的偏差值
            # 其他參數保持 base_params
        )

        # 使用 run_simulation 函數運行 SDT 模擬
        sim_results = run_simulation(current_params, strategies_bias, use_sdt=True) # 偏差效應模擬固定使用 SDT

        # 從結果中提取策略選擇比例和平均經驗卸載率
        prop_encode_low_strategy = sum(prop for s, prop in sim_results["strategy_proportions"].items() if s[1])
        prop_encode_high_strategy = sum(prop for s, prop in sim_results["strategy_proportions"].items() if s[0])

        actual_mean_offload_L = sim_results["mean_offload_L"]
        actual_mean_offload_H = sim_results["mean_offload_H"]


        # 將結果添加到列表中
        memory_encoding_rate_low.append(prop_encode_low_strategy)
        memory_encoding_rate_high.append(prop_encode_high_strategy)
        mean_empirical_offload_low.append(actual_mean_offload_L)
        mean_empirical_offload_high.append(actual_mean_offload_H)


    print(f"SDT 偏差效應結果 ({method_name}): ")
    for i, bias_val in enumerate(bias_values_to_test):
        print(f"  偏差={bias_val:.2f}: 編碼低={memory_encoding_rate_low[i]:.3f}, 編碼高={memory_encoding_rate_high[i]:.3f}, 卸載低={mean_empirical_offload_low[i]:.3f}, 卸載高={mean_empirical_offload_high[i]:.3f}")


    # 繪圖
    fig, axs = plt.subplots(1, 2, figsize=(12, 5))
    fig.suptitle(f"SDT Bias Effect Simulation ({method_name})", fontsize=14)

    # 左側子圖：內部記憶編碼率 vs 偏差值
    axs[0].plot(bias_values_to_test, memory_encoding_rate_low, 'o-', label="編碼低價值", color='cornflowerblue')
    axs[0].plot(bias_values_to_test, memory_encoding_rate_high, 's-', label="編碼高價值", color='brown')
    axs[0].set_title("內部記憶編碼策略選擇比例")
    axs[0].set_xlabel("後設認知偏差 (Meta-c)")
    axs[0].set_ylabel("策略選擇比例")
    axs[0].set_ylim(0, 1.05)
    axs[0].legend()
    axs[0].grid(True, linestyle='--', alpha=0.7)

    # 右側子圖：平均經驗卸載率 vs 偏差值
    axs[1].plot(bias_values_to_test, mean_empirical_offload_low, 'o--', label="卸載低價值 (實際)", color='cornflowerblue')
    axs[1].plot(bias_values_to_test, mean_empirical_offload_high, 's--', label="卸載高價值 (實際)", color='brown')
    axs[1].set_title("平均經驗卸載率")
    axs[1].set_xlabel("後設認知偏差 (Meta-c)")
    axs[1].set_ylabel("平均卸載率")
    axs[1].set_ylim(0, 1.05)
    axs[1].legend()
    axs[1].grid(True, linestyle='--', alpha=0.7)

    plt.tight_layout(rect=[0, 0, 1, 0.96])
    plt.show()

    return {
        "bias_values": list(bias_values_to_test),
        "memory_encoding_rate_low": memory_encoding_rate_low,
        "memory_encoding_rate_high": memory_encoding_rate_high,
        "mean_empirical_offload_low": mean_empirical_offload_low,
        "mean_empirical_offload_high": mean_empirical_offload_high,
    }





# --- 主執行區塊 ---
if __name__ == '__main__':
    # 設定基礎模型參數
    # 請根據您的需求調整 n_model_runs 和 n_episodes_per_strategy_eval 的值。
    # 論文中的運行次數通常非常大，以確保結果穩定。
    shared_base_params = ModelParameters(
        n_model_runs=1000, # 建議增加到至少 10000 或更多以獲得更穩定的結果
        n_episodes_per_strategy_eval=50 # 每個 run 中每個策略評估的試驗次數
    )

    print("=== SDT 後設認知影響認知卸載模擬 ===")
    print(f"使用參數:")
    print(f"  - 總模擬運行次數 (n_model_runs): {shared_base_params.n_model_runs}")
    print(f"  - 每個策略評估的試驗次數 (n_episodes_per_strategy_eval): {shared_base_params.n_episodes_per_strategy_eval}")
    print(f"  - Type 1 任務敏感度 (d'): {shared_base_params.d_prime}")
    print(f"  - 後設認知效率: {shared_base_params.metacognitive_efficiency}")
    print(f"  - 初始信心判斷偏差 (Meta-c): {shared_base_params.confidence_criterion_bias}")
    print(f"  - 高價值卸載信心閾值: {shared_base_params.offload_confidence_threshold_high}")
    print(f"  - 低價值卸載信心閾值: {shared_base_params.offload_confidence_threshold_low}")
    print(f"  - 價值敏感的後設認知: {shared_base_params.value_sensitive_metacognition}")
    if shared_base_params.value_sensitive_metacognition:
         print(f"  - 高價值信心提振量: {shared_base_params.high_value_confidence_boost}")


    # --- 模擬 Fig. 2-5 並比較原始模型和 SDT 模型 ---
    print(f"\n=== 圖表比較 (Fig 2-5: 原始模型 vs SDT 模型) ===")
    comparison_fig2 = compare_methods(shared_base_params, simulate_fig2, "Fig. 2")
    comparison_fig3 = compare_methods(shared_base_params, simulate_fig3, "Fig. 3")
    comparison_fig4 = compare_methods(shared_base_params, simulate_fig4, "Fig. 4")
    
    # Fig 5 的比較
    print(f"\n=== Fig. 5 方法比較 ===")
    print(f"\n--- 原始模型 ---")
    results_fig5_original = simulate_fig5(shared_base_params, use_sdt=False)
    print(f"\n--- SDT 後設認知模型 ---")
    results_fig5_sdt = simulate_fig5(shared_base_params, use_sdt=True)
    print(f"\n--- 比較結果摘要 (Fig. 5) ---")
    print(f"  條件1 (不允許卸載) 意外測試準確率:")
    print(f"    原始: 低價值={results_fig5_original['cond1']['mean_accuracy_L_surprise']:.3f}, 高價值={results_fig5_original['cond1']['mean_accuracy_H_surprise']:.3f}")
    print(f"    SDT: 低價值={results_fig5_sdt['cond1']['mean_accuracy_L_surprise']:.3f}, 高價值={results_fig5_sdt['cond1']['mean_accuracy_H_surprise']:.3f}")
    print(f"  條件2 (只允許卸載高價值) 意外測試準確率:")
    print(f"    原始: 低價值={results_fig5_original['cond2']['mean_accuracy_L_surprise']:.3f}, 高價值={results_fig5_original['cond2']['mean_accuracy_H_surprise']:.3f}")
    print(f"    SDT: 低價值={results_fig5_sdt['cond2']['mean_accuracy_L_surprise']:.3f}, 高價值={results_fig5_sdt['cond2']['mean_accuracy_H_surprise']:.3f}")


    # --- 模擬 Fig. 6-8 和 SDT 偏差效應 (僅 SDT 模型) ---
    print(f"\n=== 進階分析 (Fig 6-8 及 SDT 偏差效應 - 僅 SDT 模型) ===")
    
    fig6_results = simulate_fig6(shared_base_params, use_sdt=True)
    fig7_results = simulate_fig7(shared_base_params, use_sdt=True)
    fig8_results = simulate_fig8(shared_base_params, use_sdt=True)

    # SDT 偏差效應模擬
    bias_results = simulate_sdt_bias_effect(shared_base_params, use_sdt=True)

    print(f"\n=== 所有模擬完成 ===")
    print(f"基於 SDT 後設認知模型的模擬結果已生成並繪圖。")
    print(f"主要發現:")
    print(f"  - SDT 框架成功模擬了信心判斷、卸載決策以及參數 (如 Meta-c 偏差、記憶負荷、卸載成本、內部記憶準確率) 對決策行為的影響。")
    # 可以根據具體模擬結果添加更多發現描述
