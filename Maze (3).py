import numpy as np
import matplotlib.pyplot as plt
from  matplotlib.animation import FuncAnimation

## draw maze
fig=plt.figure(figsize=(5,5))
ax=plt.gca() # get current axis
def ini_maze():
    # draw walls
    plt.plot([1,1],[0,1], color='red', linewidth=2)
    plt.plot([1,2],[2,2], color='red', linewidth=2)
    plt.plot([2,2],[2,1], color='red', linewidth=2)
    plt.plot([2,3],[1,1], color='red', linewidth=2)
    # draw text
    plt.text(0.5,2.5, 'S0', size=14, ha='center')
    plt.text(1.5,2.5, 'S1', size=14, ha='center')
    plt.text(2.5,2.5, 'S2', size=14, ha='center')
    plt.text(0.5,1.5, 'S3', size=14, ha='center')
    plt.text(1.5,1.5, 'S4', size=14, ha='center')
    plt.text(2.5,1.5, 'S5', size=14, ha='center')
    plt.text(0.5,0.5, 'S6', size=14, ha='center')
    plt.text(1.5,0.5, 'S7', size=14, ha='center')
    plt.text(2.5,0.5, 'S8', size=14, ha='center')
    plt.text(0.5,2.3, 'START', ha='center')
    plt.text(2.5,0.3, 'GOAL', ha='center')
    # set size of figure
    ax.set_xlim(0,3)
    ax.set_ylim(0,3)
#plt.tick_params(axis='both', which='both', bottom='off', top='off', labelbottom='off', right='off', left='off', labelleft='off')

# draw circle
ini_maze()
line, = ax.plot([0.5],[2.5], marker='o', color='lightgreen', markersize=60)
plt.show()

# initial theta
theta_0=np.array([[np.nan, 1, 1, np.nan],[np.nan, 1, np.nan, 1], [np.nan , np.nan, 1, 1], [1, 1, 1, np.nan], [np.nan , np.nan, 1, 1],[1 , np.nan, np.nan, np.nan], [1, np.nan , np.nan, np.nan], [1, 1, np.nan, np.nan]])
# convert theta to pi
def simple_theta_to_pi(theta):
    [m, n]=theta.shape
    pi=np.zeros((m, n))
    for i in range(0,m):
        pi[i,:]=theta[i,:]/np.nansum(theta[i,:])
    pi=np.nan_to_num(pi)
    return pi
def softmax_pi_from_theta(theta):
    beta=2
    [m,n]=theta.shape
    pi=np.zeros((m,n))
    exp_theta=np.exp(beta*theta)
    for i in range(0,m):
        sum_exp=np.nansum(exp_theta[i,:])
        pi[i,:]=exp_theta[i,:]/sum_exp
    pi=np.nan_to_num(pi)
    return pi
pi_0=softmax_pi_from_theta(theta_0)
[a,b]=theta_0.shape
Q_0 = np.random.rand(a,b) * theta_0 * 0.1
print("Initial Q = ",Q_0)
# move to next state
direction = ["up", "right", "down", "left"]
def get_next_s(next_direct, s):
    #next_direct = np.random.choice(direction, p=pi[s,:])
    if next_direct == 0:
        s_next=s-3
    elif next_direct == 1:
        s_next=s+1
    elif next_direct == 2:
        s_next=s+3
    elif next_direct == 3:
        s_next=s-1
    return s_next

#Q-learning
def get_action(s, Q, epsilon, pi):
    if np.random.rand() < epsilon:
        next_direction = np.random.choice(direction, p=pi[s, :])
    else:
        next_direction = direction[np.nanargmax(Q[s,:])]
    if next_direction=="up":
        next_action=0
    elif next_direction=="right":
        next_action=1
    elif next_direction=="down":
        next_action=2
    else:
        next_action=3
    return next_action
""""
state_history=[]
action_history=[]
for i in range(0,100):
    s=i % 8
    action = get_action(s, Q_0, 0.5, pi_0)
    state_history.append(s)
    action_history.append(action)
print(state_history)
print(action_history)
"""
def update_theta(theta, pi, history_s, history_a):
    eta = 0.5
    TotalN = len(history_s)
    [m, n] = theta.shape
    delta_theta = theta.copy()
    N_i = np.zeros(m)
    N_ij = np.zeros((m, n))
    for t in range(0, TotalN-1):
        i=history_s[t]
        j=history_a[t]
        N_i[i]=N_i[i]+1
        N_ij[i,j]=N_ij[i,j]+1
    for i in range(0, m):
        for j in range(0, n):
            delta_theta[i, j] = (N_ij[i,j] - pi[i, j] * N_i[i]) / TotalN
    new_theta = theta + eta * delta_theta
    return new_theta

def Sarsa(s, a, s_next, a_next, r, Q, eta, gamma):
    if s_next == 8:
        Q[s,a] = Q[s,a]+eta*(r-Q[s,a])
    else:
        Q[s,a]=Q[s,a]+eta*(r-Q[s,a]+gamma*Q[s_next,a_next])
    return Q

#newQ=Sarsa(6, 0, 3, 2, Q_0, 0.1, 0.8)
#print("New Q = ", newQ)

def Q_Learning(s,a,s_next,r,Q,eta,gamma):
    if (s_next<8):
        Q[s,a]=Q[s,a]+eta*(r-Q[s,a]+gamma*np.nanmax(Q[s_next,:]))
    else:
        Q[s,a]=Q[s,a]+eta*(r-Q[s,a])
    return Q

def run_maze(pi,Q):
    eps=0.5
    eta =0.1
    gamma=0.8
    s=0 # starting state
    history_s=[0] # records of states
    action = get_action(s, Q, eps, pi)
    history_a=[action] # records of action
    while(s < 8):
        next_s = get_next_s(action, s)
        if (next_s<8):
            r=0
            next_a = get_action(next_s, Q, eps, pi)
        else:
            r=1
            next_a = np.nan
        history_s.append(next_s)
        history_a.append(next_a)
        Q = Q_Learning(s, action, next_s, r, Q, eta, gamma)
        action = next_a
        s=next_s
    return (history_s, history_a, Q)

theta_run=theta_0
pi_run = pi_0
#state_history, action_history, Q_run=run_maze(pi_run, Q_0)
#print(state_history)
#print(action_history)
step_history=[]
Q_run = Q_0
for i in range(0,50):
    pi_run= softmax_pi_from_theta(theta_run)
    state_history, action_history, Q_run = run_maze(pi_run,Q_run)
    step_history.append(len(state_history))
    theta_run = update_theta(theta_run, pi_run, state_history, action_history)
print(step_history)
#pi_run= softmax_pi_from_theta(theta_run)
#state_history, action_history = run_maze(pi_run)
#print(state_history)
#print(action_history)
#print(theta_0)
print("Final Q = ", Q_run)

def update(i):
    state = state_history[i]
    x = (state % 3) + 0.5
    y = 2.5 - int(state / 3)
    plt.cla()
    ini_maze()
    ax.plot(x, y, marker='o', color='green', markersize=60)
# anim=FuncAnimation(fig=fig, func=update, frames=len(state_history), interval=200, repeat=False)
plt.plot(step_history)
plt.show()
